<!--
 * @Author: sx <EMAIL>
 * @Date: 2023-01-10 09:58:36
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-16 18:06:20
 * @FilePath: \bpo-website-pc\components\header.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
.site-header(:class="{ 'header-en' : lang == 'zh' }")
  template(v-if="lang == 'zh'")
    NuxtLink.header-logo(to="/")
      figure
        img(src="~/assets/images/index/sd_logo.png")
    nav.header-nav
      ul.head-nav-list
        li.head-nav-item(@click="scrollTo('#service-recruitment')")
          .nav-text-main 全球人力资源服务

        li.head-nav-item(@click="scrollTo('#solution')")
          .nav-text-main 行业案例

        li.head-nav-item(@click="router.push('/zh/calculator')")
          .nav-text-main 雇主成本计算器

        li.head-nav-item.contries(@click.stop="status.showContriesPopper = !status.showContriesPopper")
          .div
            .nav-text-main 资源库
          .icon
            ArrowDownBold(style="width: 12px; height: 12px;")

          .contries-popper(v-show="status.showContriesPopper" @click.stop="() => {}" ref="contriesPopper")
            .contries-popper_arrow
            .we-serve
              .we-serve__title 全球雇佣
              .we-serve__desc 掌握全球各国家地区人力资源法律法规，安全且高效地完成全球人才招聘与雇佣，为顺利开展全球化业务奠定坚实的基础。
            .country-list
              a.country-list__item(href="/zh/countries/" @click.stop="() => {}") 国家雇佣指南
              a.country-list__item(href="/zh/marketing/" @click.stop="() => {}") 媒体报道&市场活动
              //a.country-list__item(href="/zh/calculator/" @click.stop="() => {}") 雇主成本计算器
              a.country-list__item(href="/zh/articles/" @click.stop="() => {}") 文章

    .header-extra
      client-only
        el-dropdown.language-selector
          img(src="~/assets/images/index/icon_earth.png" class="switch-language-logo")
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語
      .contact-us(v-if="showContactUs" @click="$emit('showForm')")
        button.header-contact 联系我们



  template(v-if="lang == 'en'")
    NuxtLink.header-logo(to="/")
      figure
        img(src="~/assets/images/index/sd_logo.png")
    nav.header-nav
      ul.head-nav-list
        li.head-nav-item(@click="scrollTo('#service-recruitment')")
          .nav-text-main Global HR Services
          //- .nav-text-side Recruitment
        
        li.head-nav-item(@click="scrollTo('#solution')")
          .nav-text-main Success Stories
          //- .nav-text-side EOR

        li.head-nav-item(@click="router.push('/en/calculator')")
          .nav-text-main Employer Cost Calculator

        li.head-nav-item.contries(@click.stop="status.showContriesPopper = !status.showContriesPopper")
          .div
            .nav-text-main Resources
          .icon
            ArrowDownBold(style="width: 12px; height: 12px;")

          .contries-popper(v-show="status.showContriesPopper" @click.stop="() => {}" ref="contriesPopper")
            .contries-popper_arrow
            .we-serve()
              .we-serve__title COUNTRIES WE SERVE
              .we-serve__desc Confidently engage talent 150+ countries with expert insights into Iocal Iaws and regulations.
            .country-list
              a.country-list__item(href="/en/countries/" @click.stop="() => {}") Global Hiring Guide
              a.country-list__item(href="/en/marketing/" @click.stop="() => {}") Media & Marketing Activity
              //a.country-list__item(href="/en/calculator" @click.stop="() => {}") Employee Cost Calculator
              a.country-list__item(href="/en/articles/" @click.stop="() => {}") Articles
    .header-extra
      client-only
        el-dropdown.language-selector
          img(src="~/assets/images/index/icon_earth.png" class="switch-language-logo")
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語
      .contact-us(v-if="showContactUs" @click="$emit('showForm')")
        button.header-contact Contact Us

  template(v-if="lang == 'ja'")
    NuxtLink.header-logo(to="/")
      figure
        img(src="~/assets/images/index/sd_logo.png")
    nav.header-nav
      ul.head-nav-list
        li.head-nav-item(@click="scrollTo('#service-recruitment')")
          .nav-text-main 海外人事サービス
        li.head-nav-item(@click="scrollTo('#solution')")
          .nav-text-main 導入事例
        li.head-nav-item(@click="router.push('/ja/calculator')")
          .nav-text-main 採用コスト計算ツール
        li.head-nav-item.contries(@click.stop="status.showContriesPopper = !status.showContriesPopper")
          .div
            .nav-text-main お役立ち資料
          .icon
            ArrowDownBold(style="width: 12px; height: 12px;")
          .contries-popper(v-show="status.showContriesPopper" @click.stop="() => {}" ref="contriesPopper")
            .contries-popper_arrow
            .we-serve()
              .we-serve__title カバー国
              .we-serve__desc 150+ 国以上の専門家による法令や規制の専門知識を活用して、テレワークワーカーを安全かつ効率的に採用できます。
            .country-list
              a.country-list__item(href="/ja/countries/" @click.stop="() => {}") 国家雇佣指南
              a.country-list__item(href="/ja/marketing/" @click.stop="() => {}") メディア&マーケティング活動
              a.country-list__item(href="/ja/articles/" @click.stop="() => {}") お役立ち資料

    .header-extra
      client-only
        el-dropdown.language-selector
          img(src="~/assets/images/index/icon_earth.png" class="switch-language-logo")
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語
      .contact-us(v-if="showContactUs" @click="$emit('showForm')")
        button.header-contact お問い合わせ

</template>

<script lang="ts" setup>
  import langTool from '~/assets/utils/lang'
  import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage } from 'element-plus'
  import { ArrowDown, ArrowDownBold } from '@element-plus/icons-vue'
  import {useCookie} from "#app";
  import {useSessionStorage} from "@vueuse/core";
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const { lang, source, showContactUs } = defineProps({
    lang: {
      type: String,
      default: ''
    },
    source: {
      type: String,
      default: ''
    },
    showContactUs: {
      type: Boolean,
      default: true
    }
  })
  const USER_REF_KEY = 'USER_REF'

  definePageMeta({ layout: 'basic' })
  
  const status = reactive({
    showContriesPopper: false
  })
  const contriesPopper: any = ref([])

  onMounted(() => {
    document.addEventListener('click',(e)=>{
      if (!contriesPopper.value.contains(e.target)) {
        status.showContriesPopper = false
      }
    })
    const userRef = useSessionStorage(USER_REF_KEY, '')
    if (!userRef.value && document.referrer !== '') {
      userRef.value = document.referrer
    }
  })
  
  function switchLang(lang) {
    const pathName = window.location.pathname
    const newPathName = pathName.substring(3)

    langTool.swithLang(lang, newPathName)
  }

  function scrollTo(tag) {
    if (source === 'home') {
      const ele = window.document.querySelector(tag)
      if (ele) window.scrollTo({
        top: ele.offsetTop,
        behavior: 'smooth'
      })
    } else {
      langTool.swithLang(lang as any, `/?scroll=${tag.substring(1)}`)
    }
  }
</script>
<style lang="scss" scoped>
.site-header {
  z-index: 1000;
  position: relative;
  width: 1204px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 38px 0;

  .header-logo {
    font-family: HelveticaNeue-Bold;
    font-size: 28.78px;
    color: #FF8600;
    letter-spacing: 0;
    text-align: center;
    flex: 0 0 auto;
    margin-right: 75px;

    img {
      width: 219px;
      height: 44px;
      display: block;
    }
  }

  .header-nav {
    flex: 1 1 auto;
    align-items: center;

    .head-nav-list {
      display: flex;
      align-items: center;

      .head-nav-item {
        // padding: 0 28px;
        line-height: 1;
        cursor: pointer;
        transition: all .2s;
        margin-right: 56px;

        &:hover {
          color: #FF8600;
          transition: all .2s;

          .icon {
            color: #FF8600;
            transition: all .2s;
          }
        }

        .nav-text-main {
          font-size: 16px;
          font-weight: bold;
        }

        .nav-text-side {
          font-size: 12px;
        }
      }

      .contries {
        position: relative;
        margin-right: 0;
        display: flex;
        align-items: center;

        .icon {
          // font-size: 12px;
          margin-left: 12px;
          color: #000;
        }

        .contries-popper {
          position: absolute;
          width: 528px;
          // height: 238px;
          background: #343434;
          bottom: -210px;
          right: -156px;
          padding: 32px;
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          color: #fff;
          z-index: 99999;

          .contries-popper_arrow {
            display: none;
            width: 100px;
            height: 72px;
            position: absolute;
            right: 0;
            top: -72px;
          }

          .we-serve {
            width: 182px;
            .we-serve__title {
              font-size: 16px;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 19px;
            }

            .we-serve__desc  {
              margin-top: 12px;
              font-size: 14px;
              line-height: 21px;
            }
          }

          .country-list {
            .country-list__title {
              font-size: 16px;
              font-weight: bold;
              line-height: 19px;
              margin-bottom: 12px;
              padding-left: 10px;
            }

            .country-list__item {
              font-weight: bold;
              width: 210px;
              height: 32px;
              line-height: 32px;
              font-size: 14px;
              padding-left: 10px;
              cursor: pointer;
              transition: all .2s;
              display: block;
              color: #fff;

              &:hover {
                background: #5C5C5C;
                color: #FE9111;
                transition: all .2s;
              }
            }
          }
        }
      }
    }
  }

  .header-extra {
    flex: 0 0 auto;
    display: flex;

    .contact-us {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 20px;

      .text {
        font-size: 16px;
      }

    }

    .language-selector {
      align-items: center;
      display: flex;

      .icon {
        width: 16px;
        height: 16px;
        margin-right: 16px;
      }

      .text {
        font-size: 16px;
      }
    }
  }
}

ul li {
  list-style: none;
}

.header-en {

  .head-nav-list {
    margin: 0 !important;

    .nav-text-main {
      margin-bottom: 0 !important
    }
  }
}
.switch-language-logo {
  width: 26px;
  height: 26px;
  cursor: pointer;
}
.header-contact {
  width: 93px;
  height: 33px;
  padding: 0;
  line-height: 30px;
  font-size: 14px;
  font-weight: bold;
  color: #FFF;
  background: #2D2D2D;
  border-radius: 20px;
  border: none;
}
</style>