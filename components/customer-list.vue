<template lang="pug">
.carousel
  el-carousel(ref="carouselRef" indicator-position="outside" :autoplay="true" height='270px' :interval="2500" :initial-index="0" arrow="always")
    el-carousel-item(v-for="(item, itemIndex) in customerList")
      .customer-item(:key="itemIndex" v-for="(row, rowIndex) in item")
        figure(:key="rowIndex")
          //- img(src="~/assets/images/customer/1-1.png")
          img(:src="getImageUrl(row)")

  .prev(@click="handleClickArrow('left')" @mouseenter="handleMouseenter('left')" @mouseleave="handleMouseleave('left')")
    figure
      img(v-show="!prevShow" src="~/assets/images/index/carousel-d.png")
      img.f(v-show="prevShow" src="~/assets/images/index/carousel-h.png")

  .next(@click="handleClickArrow('right')" @mouseenter="handleMouseenter('right')" @mouseleave="handleMouseleave('right')")
    figure
      img(v-show="!nextShow" src="~/assets/images/index/carousel-d.png")
      img.f(v-show="nextShow" src="~/assets/images/index/carousel-h.png")
</template>
  
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElCarousel, ElCarouselItem } from 'element-plus'
const loading = ref(true)
const carouselRef =  ref()
const autoplay = ref(true)
const prevShow =  ref(false)
const nextShow =  ref(false)

const customerList = ref([])

const modules: any = import.meta.glob('~/assets/images/customer/*', { eager: true }) // 这里可以写 @ (路径别名) 或者 /src (绝对或相对路径)

const getImageUrl = (name: string) => {
  const src = `/assets/images/customer/${name}`

  return modules[src] && modules[src].default
}

const handleClickArrow = (val: string) => {
  if (val === 'left') {
    carouselRef.value.prev()
  } else {
    carouselRef.value.next()
  }
}

const handleMouseenter = (val: string) => {
  autoplay.value = false
  if (val === 'left') {
    prevShow.value = true
  } else {
    nextShow.value = true
  }
}

const handleMouseleave = (val: string) => {
  autoplay.value = true
  if (val === 'left') {
    prevShow.value = false
  } else {
    nextShow.value = false
  }
}

onMounted(() => {
  const list = [
    "249.png","250.png","251.png",
    "220.png","221.png","222.png","223.png","224.png","225.png","226.png","227.png",
    "228.png","229.png","230.png","231.png","232.png","233.png","234.png","235.png",
    "236.png","237.png","238.png","239.png","240.png","241.png","242.png","243.png",
    "244.png","245.png","246.png","247.png","248.png","175.png","176.png","177.png","178.png","179.png","180.png","181.png","182.png",
    "183.png","184.png","185.png","186.png","187.png","188.png","189.png","190.png",
    "191.png","192.png","193.png","194.png","195.png","196.png","197.png","198.png",
    "199.png","200.png","201.png","202.png","203.png","204.png","205.png","206.png",
    "207.png","208.png","209.png","210.png","211.png","212.png","213.png","214.png",
    "215.png","216.png","217.png","218.png","219.png","1-1.png","1-2.png","1-3.png",
    "1-4.png","1-5.png","1-6.png","1-7.png","1-8.png","1-9.png","2.png","3.png","4.png",
    "7.png","8.png","9.png","11.png","14.png","15.png","16.png","17.png","18.png","19.png",
    "20.png","22.png","23.png","24.png","27.png","28.png","29.png","31.png","34.png","35.png",
    "36.png","37.png","38.png","39.png","41.png","43.png","44.png","45.png","46.png","48.png",
    "49.png","50.png","51.png","52.png","54.png","55.png","56.png","60.png","61.png","62.png",
    "64.png","65.png","66.png","67.png","69.png","70.png","71.png","72.png","74.png","75.png",
    "76.png","77.png","79.png","81.png","83.png","84.png","86.png","88.png","89.png","91.png",
    "92.png","93.png","95.png","96.png","97.png","98.png","100.png","101.png","102.png",
    "103.png","105.png","107.png","109.png","110.png","112.png","114.png","115.png","116.png",
    "117.png","120.png","122.png","123.png","125.png","126.png","129.png","130.png","131.png",
    "133.png","135.png","136.png","137.png","138.png","139.png","140.png","142.png","143.png",
    "144.png","145.png","146.png","148.png","149.png","150.png","151.png","152.png","154.png",
    "155.png","156.png","157.png","160.png","161.png","162.png","163.png","164.png","165.png",
    "166.png","167.png","168.png","169.png","170.png","171.png","172.png","173.png","174.png"
  ]
  
  const result = []; 
  for (let i = 0; i < list.length; i += 32) {  
    result.push(list.slice(i, i + 32));  
  }

  customerList.value = result
})
</script>

<style lang="scss" scoped>
  :deep(.el-carousel__arrow) {
    display: none;
  }

  .carousel {
    position: relative;

    &:hover {
      .prev, .next {
        display: block;
      }
    }

    .prev, .next {
      width: 100px;
      height: 100px;
      position: absolute;
      top: 50%;
      cursor: pointer;
      margin-top: -50px;
      display: none;

      img {
        width: 100%;
        height: 100%;
      }

      .f {
        transform: rotate(180deg);
      }
    }

    .prev {
      left: -87px;
      .f {
        margin-top: -3.5px;
      }
    }

    .next {
      right: -87px;
      transform: rotate(180deg);
      .f {
        margin-top: -3.5px;
      }
    }
  }

  :deep(.el-carousel__item) {
    display: flex;
    flex-wrap: wrap;
  }


  .customer-item {
    margin-bottom: 15px;
    width: 12.5%;
    height: 55.34px;
    
    figure {
      width: 100%;

      img {
        width: 100%;
        user-select: none;
      }
    }
  }

</style>