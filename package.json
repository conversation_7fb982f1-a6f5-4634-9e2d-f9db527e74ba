{"private": true, "scripts": {"build": "nuxt build", "dev": "set BUILD_ENV=development && nuxt dev", "generate:production": "export BUILD_ENV=production &&  nuxt generate", "generate:test": "export BUILD_ENV=test &&  nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "seo:report": "node scripts/seo-monitor.js", "seo:push-baidu": "node scripts/baidu-push.js", "seo:validate": "npm run seo:report && echo 'SEO validation completed'", "build:seo": "npm run seo:validate && npm run generate:production", "build:articles": "node scripts/build_article_refactored.js", "test": "cd scripts && jest", "test:watch": "cd scripts && jest --watch", "test:coverage": "cd scripts && jest --coverage"}, "devDependencies": {"glob": "^11.0.3", "jest": "^30.0.5", "nuxt": "3.0.0-rc.12", "video.js": "^8.21.0"}, "dependencies": {"@element-plus/icons-vue": "^2.0.10", "cos-nodejs-sdk-v5": "^2.14.6", "ejs": "^3.1.9", "element-plus": "^2.2.20", "graphql-request": "^6.1.0", "openai": "^4.60.0", "pug": "^3.0.2", "sass": "^1.55.0", "unplugin-element-plus": "^0.4.1"}}