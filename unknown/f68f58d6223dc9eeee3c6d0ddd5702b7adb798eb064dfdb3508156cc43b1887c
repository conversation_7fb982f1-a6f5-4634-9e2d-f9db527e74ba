#!/usr/bin/env node

/**
 * SmartDeer网站SEO审计工具
 * 分析网站的meta标签、页面标题、索引配置等SEO元素
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class SEOAuditor {
  constructor() {
    this.results = {
      pages: [],
      issues: {
        shortDescriptions: [],
        duplicateDescriptions: [],
        shortTitles: [],
        indexingIssues: [],
        missingElements: []
      },
      summary: {
        totalPages: 0,
        pagesWithIssues: 0,
        avgDescriptionLength: 0,
        avgTitleLength: 0
      }
    };
  }

  // 扫描所有页面文件
  scanPages() {
    const pagesDir = path.join(process.cwd(), 'pages');
    const pageFiles = glob.sync('**/*.vue', { cwd: pagesDir });
    
    console.log(`🔍 扫描到 ${pageFiles.length} 个页面文件`);
    
    pageFiles.forEach(file => {
      const filePath = path.join(pagesDir, file);
      this.analyzePage(filePath, file);
    });
    
    this.results.summary.totalPages = this.results.pages.length;
    this.analyzeIssues();
    this.generateReport();
  }

  // 分析单个页面
  analyzePage(filePath, relativePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const pageData = this.extractSEOData(content, relativePath);
      
      if (pageData) {
        this.results.pages.push(pageData);
      }
    } catch (error) {
      console.error(`❌ 分析页面失败: ${relativePath}`, error.message);
    }
  }

  // 提取SEO数据
  extractSEOData(content, relativePath) {
    const seoData = {
      path: relativePath,
      url: this.getPageURL(relativePath),
      title: '',
      description: '',
      keywords: '',
      robots: '',
      canonical: '',
      hasOpenGraph: false,
      hasTwitterCard: false,
      hasStructuredData: false,
      language: this.getLanguageFromPath(relativePath),
      issues: []
    };

    // 提取useHead配置
    const useHeadMatch = content.match(/useHead\s*\(\s*\{([\s\S]*?)\}\s*\)/);
    if (useHeadMatch) {
      const headConfig = useHeadMatch[1];
      
      // 提取title
      const titleMatch = headConfig.match(/title:\s*['"`]([^'"`]*?)['"`]/);
      if (titleMatch) {
        seoData.title = titleMatch[1];
      }

      // 提取meta标签
      const metaMatch = headConfig.match(/meta:\s*\[([\s\S]*?)\]/);
      if (metaMatch) {
        const metaContent = metaMatch[1];
        
        // 提取description
        const descMatch = metaContent.match(/name:\s*['"`]description['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/);
        if (descMatch) {
          seoData.description = descMatch[1];
        }

        // 提取keywords
        const keywordsMatch = metaContent.match(/name:\s*['"`]keywords['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/);
        if (keywordsMatch) {
          seoData.keywords = keywordsMatch[1];
        }

        // 提取robots
        const robotsMatch = metaContent.match(/name:\s*['"`]robots['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/);
        if (robotsMatch) {
          seoData.robots = robotsMatch[1];
        }

        // 检查Open Graph
        seoData.hasOpenGraph = /property:\s*['"`]og:/.test(metaContent);
        
        // 检查Twitter Card
        seoData.hasTwitterCard = /name:\s*['"`]twitter:/.test(metaContent);
      }

      // 检查结构化数据
      seoData.hasStructuredData = /application\/ld\+json/.test(headConfig);
    }

    return seoData;
  }

  // 从路径获取语言
  getLanguageFromPath(path) {
    if (path.startsWith('zh/')) return 'zh';
    if (path.startsWith('en/')) return 'en';
    if (path.startsWith('ja/')) return 'ja';
    return 'zh'; // 默认中文
  }

  // 获取页面URL
  getPageURL(relativePath) {
    const baseUrl = 'https://www.smartdeer.work';
    let cleanPath = relativePath.replace('.vue', '');
    
    if (cleanPath === 'index') {
      return `${baseUrl}/`;
    }
    
    if (cleanPath.endsWith('/index')) {
      cleanPath = cleanPath.replace('/index', '');
    }
    
    return `${baseUrl}/${cleanPath}`;
  }

  // 分析问题
  analyzeIssues() {
    const descriptions = new Map();
    let totalDescLength = 0;
    let totalTitleLength = 0;
    let pagesWithIssues = 0;

    this.results.pages.forEach(page => {
      let hasIssues = false;

      // 检查短描述（小于120字符）
      if (page.description && page.description.length < 120) {
        this.results.issues.shortDescriptions.push({
          path: page.path,
          url: page.url,
          description: page.description,
          length: page.description.length,
          language: page.language
        });
        page.issues.push('短描述');
        hasIssues = true;
      }

      // 检查短标题（小于30字符）
      if (page.title && page.title.length < 30) {
        this.results.issues.shortTitles.push({
          path: page.path,
          url: page.url,
          title: page.title,
          length: page.title.length,
          language: page.language
        });
        page.issues.push('短标题');
        hasIssues = true;
      }

      // 收集重复描述
      if (page.description) {
        if (!descriptions.has(page.description)) {
          descriptions.set(page.description, []);
        }
        descriptions.get(page.description).push(page);
        totalDescLength += page.description.length;
      }

      if (page.title) {
        totalTitleLength += page.title.length;
      }

      // 检查缺失元素
      if (!page.description) {
        this.results.issues.missingElements.push({
          path: page.path,
          url: page.url,
          missing: 'description',
          language: page.language
        });
        page.issues.push('缺少描述');
        hasIssues = true;
      }

      if (!page.title) {
        this.results.issues.missingElements.push({
          path: page.path,
          url: page.url,
          missing: 'title',
          language: page.language
        });
        page.issues.push('缺少标题');
        hasIssues = true;
      }

      // 检查索引配置
      if (page.robots && page.robots.includes('noindex')) {
        this.results.issues.indexingIssues.push({
          path: page.path,
          url: page.url,
          robots: page.robots,
          language: page.language
        });
        page.issues.push('NOINDEX设置');
        hasIssues = true;
      }

      if (hasIssues) {
        pagesWithIssues++;
      }
    });

    // 找出重复描述
    descriptions.forEach((pages, description) => {
      if (pages.length > 1) {
        this.results.issues.duplicateDescriptions.push({
          description,
          pages: pages.map(p => ({ path: p.path, url: p.url, language: p.language })),
          count: pages.length
        });
      }
    });

    // 计算平均值
    this.results.summary.pagesWithIssues = pagesWithIssues;
    this.results.summary.avgDescriptionLength = Math.round(totalDescLength / this.results.pages.length);
    this.results.summary.avgTitleLength = Math.round(totalTitleLength / this.results.pages.length);
  }

  // 生成报告
  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('🔍 SmartDeer网站SEO审计报告');
    console.log('='.repeat(80));
    
    this.printSummary();
    this.printIssues();
    this.printRecommendations();
    
    // 保存详细报告到文件
    this.saveDetailedReport();
  }

  // 打印摘要
  printSummary() {
    const { summary } = this.results;
    console.log('\n📊 审计摘要:');
    console.log(`   总页面数: ${summary.totalPages}`);
    console.log(`   有问题的页面: ${summary.pagesWithIssues} (${Math.round(summary.pagesWithIssues/summary.totalPages*100)}%)`);
    console.log(`   平均描述长度: ${summary.avgDescriptionLength} 字符`);
    console.log(`   平均标题长度: ${summary.avgTitleLength} 字符`);
  }

  // 打印问题
  printIssues() {
    const { issues } = this.results;
    
    console.log('\n🚨 发现的问题:');
    
    if (issues.shortDescriptions.length > 0) {
      console.log(`\n   📝 短描述 (${issues.shortDescriptions.length}个页面):`);
      issues.shortDescriptions.slice(0, 5).forEach(item => {
        console.log(`      ${item.path} - ${item.length}字符 (${item.language})`);
      });
      if (issues.shortDescriptions.length > 5) {
        console.log(`      ... 还有 ${issues.shortDescriptions.length - 5} 个页面`);
      }
    }

    if (issues.shortTitles.length > 0) {
      console.log(`\n   🏷️  短标题 (${issues.shortTitles.length}个页面):`);
      issues.shortTitles.slice(0, 5).forEach(item => {
        console.log(`      ${item.path} - ${item.length}字符 (${item.language})`);
      });
      if (issues.shortTitles.length > 5) {
        console.log(`      ... 还有 ${issues.shortTitles.length - 5} 个页面`);
      }
    }

    if (issues.duplicateDescriptions.length > 0) {
      console.log(`\n   🔄 重复描述 (${issues.duplicateDescriptions.length}组):`);
      issues.duplicateDescriptions.slice(0, 3).forEach(item => {
        console.log(`      "${item.description.substring(0, 50)}..." - ${item.count}个页面使用`);
      });
    }

    if (issues.indexingIssues.length > 0) {
      console.log(`\n   🚫 索引问题 (${issues.indexingIssues.length}个页面):`);
      issues.indexingIssues.forEach(item => {
        console.log(`      ${item.path} - ${item.robots} (${item.language})`);
      });
    }
  }

  // 打印建议
  printRecommendations() {
    console.log('\n💡 优化建议:');
    console.log('   1. 将短描述扩展到150-160字符');
    console.log('   2. 将短标题扩展到50-60字符');
    console.log('   3. 为每个页面创建独特的描述');
    console.log('   4. 确保重要页面设置为INDEX, FOLLOW');
    console.log('   5. 添加结构化数据标记');
    console.log('   6. 优化多语言hreflang标签');
  }

  // 保存详细报告
  saveDetailedReport() {
    const reportPath = path.join(process.cwd(), 'SEO_AUDIT_REPORT.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// 运行审计
if (require.main === module) {
  const auditor = new SEOAuditor();
  auditor.scanPages();
}

module.exports = SEOAuditor;
