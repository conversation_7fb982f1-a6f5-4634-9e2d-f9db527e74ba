// 语言特定的 Sitemap 生成器 - PC版本

export default defineEventHandler(async (event) => {
  const lang = getRouterParam(event, 'lang')
  const config = useRuntimeConfig()
  // PC项目使用PC域名
  const baseUrl = config.public.pc_site_host || 'https://www.smartdeer.work'
  
  // 验证语言参数
  const supportedLanguages = ['zh', 'en', 'ja']
  if (!supportedLanguages.includes(lang)) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Language not supported'
    })
  }
  
  // 定义页面结构
  const pages = [
    { path: '', priority: '1.0', changefreq: 'daily' },
    { path: 'aboutus', priority: '0.8', changefreq: 'weekly' },
    { path: 'calculator', priority: '0.8', changefreq: 'weekly' },
    { path: 'countries', priority: '0.7', changefreq: 'weekly' },
    { path: 'marketing', priority: '0.7', changefreq: 'weekly' }
  ]
  
  // 定义文章页面
  const articles = [
    { path: 'articles/global-employment-guide', priority: '0.6', changefreq: 'monthly' },
    { path: 'articles/eor-vs-peo', priority: '0.6', changefreq: 'monthly' },
    { path: 'articles/global-payroll-management', priority: '0.6', changefreq: 'monthly' },
    { path: 'articles/remote-work-compliance', priority: '0.6', changefreq: 'monthly' }
  ]
  
  // 合并所有页面
  const allPages = [...pages, ...articles]
  
  // 当前时间
  const now = new Date().toISOString().split('T')[0]
  
  // 生成 XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => {
    const path = page.path ? `/${lang}/${page.path}` : `/${lang}/`
    return `  <url>
    <loc>${baseUrl}${path}</loc>
    <lastmod>${now}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`
  }).join('\n')}
</urlset>`

  // 设置响应头
  setHeader(event, 'Content-Type', 'application/xml')
  setHeader(event, 'Cache-Control', 'public, max-age=3600') // 缓存1小时
  
  return sitemap
})
