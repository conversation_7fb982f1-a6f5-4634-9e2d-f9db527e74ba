/**
 * 工具函数模块
 */

const fs = require('fs');
const path = require('path');

/**
 * 清理文章内容，移除换行符并转义单引号
 * @param {string} content - 原始内容
 * @returns {string} 清理后的内容
 */
function cleanContent(content) {
  if (!content) return '';
  
  return content
    .replace(/\n/g, '')
    .replace(/'/g, "\\'");
}

/**
 * 安全地获取嵌套对象属性
 * @param {Object} obj - 目标对象
 * @param {string} path - 属性路径，如 'a.b.c'
 * @param {*} defaultValue - 默认值
 * @returns {*} 属性值或默认值
 */
function safeGet(obj, path, defaultValue = '') {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : defaultValue;
  }, obj);
}

/**
 * 确保目录存在，如果不存在则创建
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
  const dir = path.dirname(dirPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

/**
 * 安全地写入文件，确保目录存在
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 */
function safeWriteFile(filePath, content) {
  ensureDirectoryExists(filePath);
  fs.writeFileSync(filePath, content);
}

/**
 * 安全地读取文件
 * @param {string} filePath - 文件路径
 * @returns {string} 文件内容
 * @throws {Error} 如果文件不存在或读取失败
 */
function safeReadFile(filePath) {
  if (!fs.existsSync(filePath)) {
    throw new Error(`Template file not found: ${filePath}`);
  }
  return fs.readFileSync(filePath, 'utf8');
}

/**
 * 替换模板中的所有占位符
 * @param {string} template - 模板内容
 * @param {Object} variables - 变量对象
 * @returns {string} 替换后的内容
 */
function replaceTemplateVariables(template, variables) {
  let result = template;
  
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `\${${key}}`;
    result = result.replaceAll(placeholder, value || '');
  });
  
  return result;
}

/**
 * 创建文章列表项数据
 * @param {Object} post - 文章数据
 * @returns {Object} 文章列表项
 */
function createArticleListItem(post) {
  return {
    id: post.id,
    title: post.title,
    image: safeGet(post, 'countryGuideExternal.listingImage'),
    countryName: safeGet(post, 'countryGuideExternal.countryName')
  };
}

/**
 * 生成文件路径
 * @param {string} basePath - 基础路径
 * @param {string} fileName - 文件名
 * @param {string} extension - 文件扩展名（可选）
 * @returns {string} 完整文件路径
 */
function generateFilePath(basePath, fileName, extension = '.vue') {
  return path.join(basePath, `${fileName}${extension}`);
}

/**
 * 记录错误日志
 * @param {string} message - 错误消息
 * @param {Error} error - 错误对象
 */
function logError(message, error) {
  console.error(`[ERROR] ${message}:`, error);
}

/**
 * 记录信息日志
 * @param {string} message - 信息消息
 */
function logInfo(message) {
  console.log(`[INFO] ${message}`);
}

module.exports = {
  cleanContent,
  safeGet,
  ensureDirectoryExists,
  safeWriteFile,
  safeReadFile,
  replaceTemplateVariables,
  createArticleListItem,
  generateFilePath,
  logError,
  logInfo
};
