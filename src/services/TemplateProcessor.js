/**
 * 模板处理服务
 * 负责模板文件读取、变量替换和模板选择逻辑
 */

const { TEMPLATE_CONFIG, LANGUAGE_CONFIG, SEO_CONFIG, CATEGORY_PATH_MAPPING } = require('../config');
const { safeReadFile, replaceTemplateVariables, cleanContent, safeGet, logError } = require('../utils');

class TemplateProcessor {
  constructor() {
    this.templateCache = new Map();
  }

  /**
   * 读取并缓存模板文件
   * @param {string} templateType - 模板类型
   * @returns {string} 模板内容
   */
  getTemplate(templateType) {
    if (this.templateCache.has(templateType)) {
      return this.templateCache.get(templateType);
    }

    const templatePath = TEMPLATE_CONFIG[templateType];
    if (!templatePath) {
      throw new Error(`Unknown template type: ${templateType}`);
    }

    try {
      const template = safeReadFile(templatePath);
      this.templateCache.set(templateType, template);
      return template;
    } catch (error) {
      logError(`Failed to read template: ${templatePath}`, error);
      throw error;
    }
  }

  /**
   * 根据分类ID确定模板类型
   * @param {number} categoryId - 分类ID
   * @returns {string} 模板类型
   */
  getTemplateType(categoryId) {
    const mapping = CATEGORY_PATH_MAPPING[categoryId];
    return mapping ? mapping.template : 'default';
  }

  /**
   * 构建模板变量对象
   * @param {Object} post - 文章数据
   * @param {string} language - 语言代码
   * @returns {Object} 模板变量
   */
  buildTemplateVariables(post, language) {
    const langConfig = LANGUAGE_CONFIG[language];
    if (!langConfig) {
      throw new Error(`Unsupported language: ${language}`);
    }

    // 清理内容
    const cleanedContent = cleanContent(post.content);

    // 构建基础变量
    const variables = {
      TITLE: post.title || '',
      CONTENT: cleanedContent,
      LANGUAGE: langConfig.code,
      LANGUAGE_SIMPLE: langConfig.simple,
      FORM_CONFIRM_PROMPT: langConfig.confirmPrompt,
      DESCRIPTION: safeGet(post, 'seo.opengraphDescription') || safeGet(post, 'seo.metaDesc') || post.title || '',
      SITE_NAME: safeGet(post, 'seo.opengraphSiteName') || SEO_CONFIG.defaultSiteName,
      FEATURE_IMAGE_URL: safeGet(post, 'featuredImage.node.sourceUrl') || safeGet(post, 'countryGuideExternal.listingImage') || '',
      COUNTRY_FLAT_IMAGE_URL: safeGet(post, 'countryGuideExternal.countryFlagImage.node.sourceUrl') || ''
    };

    return variables;
  }

  /**
   * 处理单个语言的模板
   * @param {Object} post - 文章数据
   * @param {number} categoryId - 分类ID
   * @param {string} language - 语言代码
   * @returns {string} 处理后的模板内容
   */
  processTemplate(post, categoryId, language) {
    try {
      // 获取模板类型和内容
      const templateType = this.getTemplateType(categoryId);
      const template = this.getTemplate(templateType);

      // 构建变量
      const variables = this.buildTemplateVariables(post, language);

      // 替换变量
      const processedContent = replaceTemplateVariables(template, variables);

      return processedContent;
    } catch (error) {
      logError(`Failed to process template for post ${post.id}, category ${categoryId}, language ${language}`, error);
      throw error;
    }
  }

  /**
   * 批量处理多语言模板
   * @param {Object} post - 文章数据
   * @param {number} categoryId - 分类ID
   * @param {Array<string>} languages - 语言列表
   * @returns {Object} 各语言的处理结果
   */
  processMultiLanguageTemplates(post, categoryId, languages) {
    const results = {};

    languages.forEach(language => {
      try {
        results[language] = this.processTemplate(post, categoryId, language);
      } catch (error) {
        logError(`Failed to process template for language ${language}`, error);
        // 继续处理其他语言，不中断整个流程
        results[language] = null;
      }
    });

    return results;
  }

  /**
   * 清空模板缓存
   */
  clearCache() {
    this.templateCache.clear();
  }

  /**
   * 获取缓存状态
   * @returns {Object} 缓存信息
   */
  getCacheInfo() {
    return {
      size: this.templateCache.size,
      keys: Array.from(this.templateCache.keys())
    };
  }

  /**
   * 验证模板文件是否存在
   * @returns {Object} 验证结果
   */
  validateTemplates() {
    const results = {};
    
    Object.entries(TEMPLATE_CONFIG).forEach(([type, path]) => {
      try {
        this.getTemplate(type);
        results[type] = { exists: true, path };
      } catch (error) {
        results[type] = { exists: false, path, error: error.message };
      }
    });

    return results;
  }
}

module.exports = TemplateProcessor;
