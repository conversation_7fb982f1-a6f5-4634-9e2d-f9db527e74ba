/**
 * 配置文件 - 包含所有硬编码的常量和配置项
 */

// GraphQL API 配置
const API_CONFIG = {
  endpoint: 'https://blog.smartdeer.work/graphql',
  pageSize: 10
};

// 分类ID映射配置
const CATEGORY_CONFIG = {
  // 国家指南
  COUNTRY_GUIDE_ZH: 4,
  COUNTRY_GUIDE_EN: 5,
  
  // 文章
  ARTICLE_ZH: 8,
  ARTICLE_EN: 7,
  
  // 营销
  MARKETING_ZH: 11,
  MARKETING_EN: 10,
  
  // 法律
  LEGAL_ZH: 13,
  LEGAL_EN: 14
};

// 语言配置
const LANGUAGE_CONFIG = {
  zh: {
    code: 'zh-CN',
    simple: 'zh',
    confirmPrompt: '您的请求已收到，我们会尽快与您联系。'
  },
  en: {
    code: 'en-US',
    simple: 'en',
    confirmPrompt: 'We have received your request and we will contact with you as soon as possible.'
  },
  ja: {
    code: 'ja-JP',
    simple: 'ja',
    confirmPrompt: 'お問い合わせを受け付けました。できるだけ早くご連絡いたします。'
  }
};

// 模板文件路径配置
const TEMPLATE_CONFIG = {
  default: './article-detail-template.vue',
  flag: './article-detail-flag-template.vue',
  noBanner: './article-detail-nobanner-template.vue',
  noBannerNoFlagNoContact: './article-detail-nobanner-noflag-nocontact-template.vue'
};

// 输出路径配置
const OUTPUT_CONFIG = {
  pages: {
    zh: {
      countries: './pages/zh/countries',
      articles: './pages/zh/articles',
      marketing: './pages/zh/marketing',
      legal: './pages/zh/legal'
    },
    en: {
      countries: './pages/en/countries',
      articles: './pages/en/articles',
      marketing: './pages/en/marketing',
      legal: './pages/en/legal'
    },
    ja: {
      countries: './pages/ja/countries',
      articles: './pages/ja/articles',
      marketing: './pages/ja/marketing',
      legal: './pages/ja/legal'
    }
  },
  lists: {
    zh: {
      countries: './pages/zh/countries/article-list-zh.json',
      articles: './pages/zh/articles/article-list-zh.json',
      marketing: './pages/zh/marketing/article-list-zh.json',
      legal: './pages/zh/legal/article-list-zh.json'
    },
    en: {
      countries: './pages/en/countries/article-list-en.json',
      articles: './pages/en/articles/article-list-en.json',
      marketing: './pages/en/marketing/article-list-en.json',
      legal: './pages/en/legal/article-list-en.json'
    },
    ja: {
      countries: './pages/ja/countries/article-list-ja.json',
      articles: './pages/ja/articles/article-list-ja.json',
      marketing: './pages/ja/marketing/article-list-ja.json',
      legal: './pages/ja/legal/article-list-ja.json'
    }
  }
};

// 分类到路径的映射
const CATEGORY_PATH_MAPPING = {
  [CATEGORY_CONFIG.COUNTRY_GUIDE_ZH]: {
    language: 'zh',
    type: 'countries',
    template: 'flag'
  },
  [CATEGORY_CONFIG.COUNTRY_GUIDE_EN]: {
    language: 'en',
    type: 'countries',
    template: 'flag',
    generateJapanese: true
  },
  [CATEGORY_CONFIG.ARTICLE_ZH]: {
    language: 'zh',
    type: 'articles',
    template: 'noBanner'
  },
  [CATEGORY_CONFIG.ARTICLE_EN]: {
    language: 'en',
    type: 'articles',
    template: 'noBanner',
    generateJapanese: true
  },
  [CATEGORY_CONFIG.MARKETING_ZH]: {
    language: 'zh',
    type: 'marketing',
    template: 'default'
  },
  [CATEGORY_CONFIG.MARKETING_EN]: {
    language: 'en',
    type: 'marketing',
    template: 'default',
    generateJapanese: true
  },
  [CATEGORY_CONFIG.LEGAL_ZH]: {
    language: 'zh',
    type: 'legal',
    template: 'noBannerNoFlagNoContact'
  },
  [CATEGORY_CONFIG.LEGAL_EN]: {
    language: 'en',
    type: 'legal',
    template: 'noBannerNoFlagNoContact'
  }
};

// 默认SEO配置
const SEO_CONFIG = {
  defaultSiteName: 'SmartDeer'
};

module.exports = {
  API_CONFIG,
  CATEGORY_CONFIG,
  LANGUAGE_CONFIG,
  TEMPLATE_CONFIG,
  OUTPUT_CONFIG,
  CATEGORY_PATH_MAPPING,
  SEO_CONFIG
};
