const OpenAI = require("openai");
const COS = require("cos-nodejs-sdk-v5");

const bucketName = 'website-seo-article-1314636188'
const bucketRegion = 'ap-guangzhou'
const prefix = ''
const cosService = new COS({
  SecretId: process.env.COS_SECRET_ID,
  SecretKey: process.env.COS_SECRET_KEY
})

let fileKeys = []

async function getBucketList(maker) {
  cosService.getBucket({
    Bucket: bucketName,
    Region: bucketRegion,
    Prefix: prefix,
    MaxKeys: 1000
  }).then( async (res) => {
    res.Contents.map(item => {
      fileKeys.push(item.Key)
    })
    console.log(fileKeys)
    if (res.IsTruncated === 'true') {
      await getBucketList(res.NextMarker)
    }
  })
}

async function publishContentByKey(fileKey) {

}

getBucketList()