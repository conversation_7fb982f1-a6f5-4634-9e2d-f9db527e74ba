<template lang="pug">
.contries-page
  site-header(lang="en" :showContactUs="false")

  .countries
    .countries-content
      h1.article-title 迪拜的中高端货卖不好是因为没有“高贵的身份”
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="en" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="en" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '迪拜的中高端货卖不好是因为没有“高贵的身份”',
  ogDescription: '在迪拜你千万不要卖高贵的made China, 不然容易血本无归。我们smartdeer有一个客户，在迪拜做化 […]',
  ogSiteName: 'SmartDeer',
  description: '在迪拜你千万不要卖高贵的made China, 不然容易血本无归。我们smartdeer有一个客户，在迪拜做化 […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: '迪拜的中高端货卖不好是因为没有“高贵的身份”'
})
const status = reactive({
  showForm: false
})
const langSimple = 'en';
const pageTitle = '迪拜的中高端货卖不好是因为没有“高贵的身份”';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/08/WechatIMG1014.png';
const flagImage = '';
const htmlContent = '<p>在迪拜你千万不要卖高贵的made China, 不然容易血本无归。我们smartdeer有一个客户，在迪拜做化妆品生意，开了个实体店，本来以为这边儿是低端市场好做，带了很多低端产品，她自己本身的品牌高端线根本就没敢带来，结果发现太卷了，就想做高端的产品，结果老外说了，你这个产品跟你以前的产品有什么区别呢？哪里更好？我为什么要买这用到脸上的东西？你这made inChina的产品对脸不会有任何影响吗？但是呢，我换个国家，Made in japan的中国老板创立的品牌就很好卖，哪怕是国产也是工厂变成了日本而已，产地换成马/来西亚、新加坡，一样的套路就被海外人的人买账，没有办法改变老外的思维定势，至少我们可以找到一条立足之地，再去慢慢的发展，那么国内是不是也可以用这样的套路呢，如果您想在海外建立品牌，我们smartdeer可以给你提供支持，我们smartdeer不仅能帮助企业在海外快速搭建本地化团队解决用工问题，还有强大的风险预警和合规能力，解决您企业海外用人基本问题，让您安心把精力放在公司业务上，评论区留言，给您发一份国家手册，比同行提前一步了解相关讯息。</p>';
function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>