header {
  background: #fff6ec;
  .header-banner {
    position: relative;
    width: 1204px;
    height: 500px;
    margin: 0 auto;
  }

  .header-banner-text {
    position: absolute;
    left: 0;
    width: 641px;
    .header-title {
      font-size: 46px;
      font-weight: bold;
      margin-top: 80px;
    }
    .header-desc {
      font-size: 18px;
      line-height: 30px;
      letter-spacing: 3px;
      font-weight: 300;
    }
    .header-contact {
      width: 143px;
      height: 56px;
      border-radius: 50px;
      background: #474747;
      font-size: 16px;
      text-align: center;
      color: #FFF;
      cursor: pointer;
    }
  }

  .header-banner-image {
    position: absolute;
    right: 0;
    width: 692px;
    height: 451px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.country-index-main {
  width: 1204px;
  margin: 60px auto 50px;

  h2 {
    font-size: 36px;
    line-height: 48px;
    letter-spacing: 1px;
    text-align: center;
  }

  p {
    font-size: 16px;
    font-weight: 350;
    line-height: 26px;
    letter-spacing: 2px;
  }
  .search-bar {
    margin: 0 auto;
    width: 800px;
    height: 78px;
    border: 1px solid #EFF0F6;
    box-sizing: border-box;
    border-radius: 60px;
    box-shadow: 0 8px 25px 0 rgba(13, 10, 44, 0.06);

    .text-search {
      border: none;
      outline: none;
      font-size: 18px;
      background: #FFF;
      width: 700px;
      line-height: 60px;
      margin-top: 6px;
      margin-left: 20px;
    }

    .btn-search {
      cursor: pointer;
      background-color: #FE9111;
      width: 64px;
      height: 64px;
      font-size: 0;
      border: none;
      border-radius: 56px;
      background-repeat: no-repeat;
      background-position: center;
      vertical-align: middle;
      background-image: url("~/assets/images/countries/search.png");
    }
  }

  .country-list {
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 20px;
    a {
      display: block;
      width: calc(33% - 10px);
      height: 290px;
      box-sizing: border-box;
      border: 1px solid #EFF0F6;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      overflow: hidden;
    }

    li{
      list-style: none;
      img {
        width: 100%;
        height: 240px;
      }

      .link-container {
        font-size: 16px;
        text-align: center;
        line-height: 50px;
        padding: 0 15px;
        height: 50px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #000;
        font-weight: bold;
      }
    }
  }

  .paginator {
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    margin: 20px 0;

    a {
      cursor: pointer;
      margin: 0 20px;
    }
  }
}