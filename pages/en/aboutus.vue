<template lang="pug">
mixin header-nav
  section.header-nav
    NuxtLink(to="/")
      figure.logo
        img(src="~/assets/images/aboutus/sd_logo.png")
    .extra
      .contact-us(@click="()=>{status.showForm = true}")
        .text Contact Us
      client-only
        el-dropdown.language-selector
          .text(style="color: #000;") 中 / EN / 日
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

mixin company-profile 
  section.company-profile
    .section-title
      h2.title Your Global HR Solutions Partner

    .profile-content
      .figure
        img(src="~/assets/images/aboutus/background.jpeg")
        h3 Company Background
        p SmartDeer is a one-stop "HR service and SaaS" platform dedicated to global recruitment and employment solutions. Incubated by Trustbridge Partners, with investments led by Welight Capital, WeWork, and Hash Global, SmartDeer helps businesses transcend geographical boundaries, swiftly recruit global talent, and manage the entire employment lifecycle, including onboarding, offboarding, payroll, taxes, and benefits. Our platform ensures compliance and efficiency at every step, simplifying global team management.
        p SmartDeer has achieved ISO 27001 certification, ensuring comprehensive protection of customers' data and privacy, and delivering a trusted and reliable service.
      .figure.right
        img(src="~/assets/images/aboutus/globalization.jpeg")
        h3 Global Influence
        p SmartDeer is dual-headquartered in Hong Kong and Singapore, providing us with a strategic global advantage while ensuring compliance with local data storage and processing regulations. In addition to subsidiaries and branches in regions like the United States, the United Kingdom, the UAE, Saudi Arabia, Australia, Japan, South Korea, Thailand, Malaysia, Indonesia, the Philippines, Vietnam, Mainland China，Mexico and Brazil.Our services, delivered through our self-operated network and partners, span over 150 countries and regions worldwide.
      .figure
        img(src="~/assets/images/index/eor.webp")
        h3 Our Team
        p SmartDeer has more than 150 employees across over a dozen countries, offering multilingual services, with a focus on Chinese and English, and strong capabilities in various local languages to cater to regional needs. Our team’s deep expertise in local laws and regulations allows us to provide professional, localized services with global support.
      .figure.right
        img(src="~/assets/images/aboutus/scope.jpeg")
        h3 Service Scope
        p We provide comprehensive HR solutions for talent recruitment, global compliant employment, visa processing, payroll management, benefits, and tax administration. SmartDeer’s robust Global HR SaaS system enables businesses to easily manage the complexities of global HR operations, streamlining processes and mitigating compliance risks. Our platform allows companies to recruit talent globally, manage onboarding and offboarding, track vacations, and handle payroll and taxes seamlessly, helping businesses succeed globally.

mixin profession
  section.profession
    .profession-list
      .profession-item
        figure 
          img(src="~/assets/images/aboutus/earth.svg")
        .content
          .title Global Collaboration
          .desc Our teams are located in the United States, the United Kingdom, the United Arab Emirates, Saudi Arabia, Australia, Singapore, Japan, South Korea, Thailand, Malaysia, Indonesia, the Philippines, Vietnam, Hong Kong and other places.

      .profession-item
        figure

          img(src="~/assets/images/aboutus/team.svg")
        .content
          .title Professional Team
          .desc Our members come from all over the world, with over 10 years of HR experience in top companies.

      .profession-item
        figure 
          img(src="~/assets/images/aboutus/cover.svg")
        .content
          .title Full Coverage of Services
          .desc Our services include global talent recruitment, EOR, global contractors, global human service professional outsourcing and consulting.

mixin global-office
  section.global-office
    .section-title
      h2.title Global Office

    .office
      .office-list
        .office-item
          figure 
            img(src="~/assets/images/aboutus/hongkong.webp")
          .content
            .title Hong Kong
            .location Address: Room 705-706, 7/F., China Insurance Group Building, No. 141 Des Voeux Road Central, Central, Hong Kong
        .office-item
          figure
            img(src="~/assets/images/aboutus/singapore.webp")
          .content
            .title Singapore
            .location Address：3 Fraser Street, #5-25 Duo Tower, Singapore (189352)
        .office-item
          figure
            img(src="~/assets/images/aboutus/california.jpeg")
          .content
            .title United States
            .location Address：15025 PROCTOR AVAVE CITY OF INDUSTRY, Y, CA CA 91746
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/uk.jpeg")
          .content
            .title United Kingdom
            .location Address：69 Aberdeen Avenue, Cambridge, England, CB2 8DL
        .office-item
          figure
            img(src="~/assets/images/aboutus/australia.jpeg")
          .content
            .title Australia
            .location Address：135 KING STREET, SYDNEY, NSW 2000
        .office-item
          figure
            img(src="~/assets/images/aboutus/aue.jpeg")
          .content
            .title The United Arab Emirates
            .location Address：Office328,BlockB,Business Village, Deira, Dubai, UAE
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/japan.jpeg")
          .content
            .title Japan
            .location Address：神奈川県横浜市中区山下町98GSハイム山下町4階403号室
        .office-item
          figure
            img(src="~/assets/images/aboutus/korea.jpeg")
          .content
            .title South Korea
            .location Address：서울특별시 중랑구 동일로825，2층 205호 (중화동)
        .office-item
          figure
            img(src="~/assets/images/aboutus/thailand.jpeg")
          .content
            .title Thailand
            .location Address：11 / 152-153 Room No. GDC 101, 1st Floor, Moo 5, Kookong District, Lam Luka District, Pathum Thani Province
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/malaysia.jpeg")
          .content
            .title Malaysia
            .location Address：332F-­2, HARMONY SQUARE, JALAN PERAK 11600 JELUTONG PULAU PINANG MALAYSIA
        .office-item
          figure
            img(src="~/assets/images/aboutus/indonesia.jpeg")
          .content
            .title Indonesia
            .location Address：Gedung Wirausaha Lantai 1 Unit 104, Jalan HR Rasuna Said Kav. C-5,Desa/Kelurahan Karet, Kec. Setiabudi, Kota Adm. Jakarta Selatan, ProvinsiDKI Jakarta
        .office-item
          figure
            img(src="~/assets/images/aboutus/philippines.jpeg")
          .content
            .title The Philippines
            .location Address：UNIT 25D 2ND FLOOR ZETA II BLDG.191 SALCEDO ST., SAN LORENZO,CITY OF MAKATI,FOURTH DIRECT, NATIONAL CAPITAL REGION (NCR), 1223
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/vietnam.jpeg")
          .content
            .title Vietnam
            .location Address：Room(s):28 ,Level 14, Saigon Centre Tower 1, No. 65 Le Loi Street, Ben Nghe Ward, District 1, Ho Chi Minh City,Vietnam
        .office-item
          figure
            img(src="~/assets/images/aboutus/beijing.webp")
          .content
            .title BeiJing
            .location Address: 4F, Wangfu International Center wework,Wangfujing, Dongcheng District, Beijing

        .office-item
          figure
            img(src="~/assets/images/aboutus/shanghai.webp")
          .content
            .title ShangHai
            .location Address: Floor 12, Huirong Building, No. 535, Caoyang Road, Putuo District, Shanghai Chengdu: 6F-K0
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/shenzhen.webp")
          .content
            .title ShenZhen
            .location Address: 0701-D021, Port Building, Ocean Freight Center, No. 59, Linhai Avenue, Nanshan Street, Shenzhen-Hong Kong Modern Service Industry Cooperation Zone in Qianhai, Shenzhen
        .office-item
          figure
            img(src="~/assets/images/aboutus/chengdu.webp")
          .content
            .title ChengDu
            .location Address: 6F-K0063, Lei Shing Hong Plaza, No. 5 Hangtian Road, Chenghua District, Chengdu City, Sichuan Province
        .office-item
          figure
            img(src="~/assets/images/aboutus/hangzhou.webp")
          .content
            .title HangZhou
            .location Address: Room 205, Building 2, No. 8-1, Longquan Road, Qianjie Street, Yuhang District, Hangzhou City, Zhejiang Province

mixin contact-us
  .contact-form
    //- 吃瓜就要付出代价，否则影响页面跳转
    //- https://github.com/element-plus/element-plus/pull/9731
    client-only
      el-dialog(v-model="status.showForm" title="" :width="600")
        contact-us-form(@submit="submitSuccess" lang="en")

.page-about-us
  .header
    +header-nav
    +company-profile

  +profession
  +global-office

  +contact-us

  SiteFooter(lang="en" @contact-us="()=>{ status.showForm = true }")

</template>

<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
definePageMeta({ layout: 'basic' })
useHead({
  htmlAttrs: { lang: 'en' },
  title: 'About SmartDeer - Leading Global HR Solutions Provider | International Employment',
  meta: [
    // Basic SEO
    { name: 'description', content: 'Learn about SmartDeer, a leading global HR solutions provider. We offer professional EOR services, international recruitment, payroll management across 150+ countries with global offices and ISO 27001 certification for secure compliance.' },
    { name: 'keywords', content: 'about SmartDeer, global HR company, international employment services, EOR provider, global recruitment agency, HR solutions, international payroll, overseas business services, global compliance' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },

    // Canonical and hreflang
    { name: 'canonical', content: 'https://smartdeer.work/en/aboutus' },

    // Open Graph
    { property: 'og:title', content: 'About SmartDeer - Global HR Solutions & Employment Services Provider' },
    { property: 'og:description', content: 'Learn about SmartDeer, a leading global HR solutions provider. We offer EOR services, international recruitment, payroll management across 150+ countries with offices worldwide.' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://smartdeer.work/en/aboutus' },
    { property: 'og:image', content: 'https://smartdeer.work/images/aboutus/company-overview.png' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: 'en_US' },

    // Twitter Card
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'About SmartDeer - Global HR Solutions Provider' },
    { name: 'twitter:description', content: 'Learn about SmartDeer, a leading global HR solutions provider offering EOR services, international recruitment, and payroll management across 150+ countries.' },
    { name: 'twitter:image', content: 'https://smartdeer.work/images/aboutus/company-overview.png' },
    { name: 'twitter:image:alt', content: 'SmartDeer Global Offices and Team' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: 'https://smartdeer.work/en/aboutus' },

    // Hreflang for multilingual SEO
    { rel: 'alternate', hreflang: 'en', href: 'https://smartdeer.work/en/aboutus' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://smartdeer.work/zh/aboutus' },
    { rel: 'alternate', hreflang: 'ja-JP', href: 'https://smartdeer.work/ja/aboutus' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://smartdeer.work/en/aboutus' }
  ],
  script: [
    // Structured Data - AboutPage
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "AboutPage",
        "name": "About SmartDeer",
        "description": "Learn about SmartDeer, a global HR solutions provider offering comprehensive employment services worldwide",
        "url": "https://smartdeer.work/en/aboutus",
        "mainEntity": {
          "@type": "Organization",
          "name": "SmartDeer",
          "description": "Global HR solutions and employment services provider",
          "url": "https://smartdeer.work",
          "logo": "https://smartdeer.work/images/logo.png",
          "foundingDate": "2020",
          "numberOfEmployees": "150+",
          "address": [
            {
              "@type": "PostalAddress",
              "addressLocality": "Hong Kong",
              "addressRegion": "Hong Kong",
              "streetAddress": "Room 705-706, 7/F., China Insurance Group Building, No. 141 Des Voeux Road Central, Central",
              "addressCountry": "HK"
            },
            {
              "@type": "PostalAddress",
              "addressLocality": "Singapore",
              "addressRegion": "Singapore",
              "streetAddress": "3 Fraser Street, #5-25 Duo Tower",
              "postalCode": "189352",
              "addressCountry": "SG"
            }
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["English", "Chinese", "Japanese"]
          },
          "sameAs": [
            "https://www.linkedin.com/company/smartdeer-global/"
          ],
          "areaServed": "Worldwide",
          "serviceArea": {
            "@type": "GeoCircle",
            "name": "Global Coverage - 150+ Countries"
          }
        }
      })
    }
  ]
})

const status = reactive({
  showForm: false
})

function switchLang(lang) {
  langTool.swithLang(lang, '/aboutus')
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('Submit success!')
}
</script>

<style lang="scss" scoped>
@import url('@/assets/styles/en.scss');

.page-about-us {
  font-family: Helvetica;

  section {
    .section-title {
      text-align: center;

      h2.title {
        font-weight: bold;
        font-size: 48px;
        position: relative;
        display: inline-block;
        line-height: 58px;

        &::before {
          content: '';
          display: block;
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          background-color: #FF8600;
          height: 8px;
          z-index: -1;
        }
      }
      
      p {
        font-size: 18px;
        color: #999999;
        letter-spacing: 5px;
      }
    }
  }

  .header {
    background-image: url("~/assets/images/aboutus/map-bg.png");
    background-position: center 16px;
    background-repeat: no-repeat;
  }
}

section.header-nav {
  width: 1204px;
  margin: 0 auto;
  padding-top: 47px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;

  figure.logo {
    flex: 0 0 auto;

    img {
      width: 160px;
    }
  }

  .page-title {
    flex: 1 1 auto;
    padding-left: 24px;

    h1 {
      font-size: 20px;
      font-weight: bold;

      &::before {
        content: "|";
        display: inline-block;
        color: #BFBFBF;
        margin-right: 24px;
      }

    }
  }

  .extra {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #000000;

    .language-selector {
      margin-left: 50px;

      .text {
        font-size: 16px;
      }
    }
  }
}

section.company-profile {
  width: 1204px;
  margin: 0 auto;
  padding-top: 100px;

  .profile-content {
    align-items: center;
    margin-top: 72px;
    padding: 0 12px;
    margin-bottom: 40px;

    .figure {
      width: 1204px;
      height: 350px;
      margin-bottom: 20px;
      img {
        width: 580px;
        height: 310px;
        float: left;
        margin: 0 40px 25px 0;
        border-radius: 15px;
      }
      &::after {
        content: '';
        clear: both;
      }
    }

    .figure.right {
      img {
        float: right;
        margin: 0 0 25px 40px;
      }
      &::after {
        content: '';
        clear: both;
      }
    }

    h3 {
      font-size: 24px;
      color: #333;
      margin-bottom: 10px;
    }
    h4 {
      color: #333;
      font-size: 20px;
      margin: 0 0 25px;
    }
    p {
      color: #333;
      margin: 0;
      font-size: 18px;
      line-height: 26px;
      letter-spacing: 1px;
      margin: 0 0 25px;
    }
  }
}

section.profession {
  background-color: #F7F9FA;

  .profession-list {
    width: 1204px;
    margin: 0 auto;
    height: 399px;
    display: flex;
    padding-top: 40px;
    justify-content: space-between;


    .profession-item {
      width: 360px;
      box-sizing: border-box;

      figure {
        img {
          width: 64px;
          margin: 0 auto;
        }
      }

      .content {
        margin-top: 33px;
        
        .title {
          font-size: 29px;
          color: #333333;
          line-height: 35px;
          text-align: center;
        }

        .desc {
          font-size: 20px;
          color: #333333;
          line-height: 28px;
          margin-top: 17px;
        }
      }
    }
  }
}

section.global-office {
  width: 1204px;
  margin: 0 auto;
  padding: 156px 0 200px 0;

  .office-list {
    margin-top: 60px;
    display: flex;
    justify-content: space-between;

    &:first-child{
      margin-top: 70px;
    }

    .office-item {
      width: 382px;
      box-sizing: border-box;

      figure {
        img {
          width: 100%;
          display: block;
          height: 246px;
          border-radius: 20px;
        }
      }

      .content {
        margin-top: 32px;

        .title {
          text-align: center;
          font-size: 26px;
          color: #333333;
          line-height: 32px;
          margin-bottom: 16px;
        }

        .location, .contact{
          font-size: 18px;
          color: #333333;
          line-height: 29px;
        }
      }
    }
  }
}
</style>