<template lang="pug">
mixin header
  header
    site-header(
      lang="en" 
      source="home" 
      @showForm="()=>{status.showForm = true}"
      @scrollTo="scrollTo"
    )

    .header-banner
      .header-banner-text
        h2.slogon Global Recruitment & Global Employment
        h1.title International HR One-stop Service
        .desc 
          span  Global recruitment hires the worldwide talents, we handle the compliance of global recruitment and salary and payroll, provide professional one-stop Human Resources solution.
          figure
              img(src="~/assets/images/index/global-desc.webp")
      .header-banner-image
        figure.global-min_1
          img(src="~/assets/images/index/global-min_1.png")
        figure.global-min_2
          img(src="~/assets/images/index/global-min_2.png")
        figure.global-min_3
          img(src="~/assets/images/index/global-min_3.png")

mixin customer
  section.customer
    .section-title
      h2 Serve Global Customers
    
    CustomerList
    
mixin service
  section.service
    .section-title
      h2 Global HR Services & SaaS
    .service-list
      .service-item(id="service-recruitment" v-scroll-show="{ delayOffset: 100 }")
        .service-content
          .service-title
            h3 Global Recruitment
          .service-desc
            p Need to acquire talents globally? Our expert team ensures seamless hiring in your target markets.
            p With an extensive talent pool and a skilled headhunting team, we manage recruitment and payments efficiently. Plus, enjoy 24/7 support for smooth operations.
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") Request More Information
              ArrowRight(class="inline-arrow")
        .figure-area
          figure
            img(src="~/assets/images/index/recruitment.webp" alt="Global Recruitment")
      .service-item(id="service-eor" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/eor.webp" alt="EOR")
        .service-content
          .service-title
            h3 Employer of Record
          .service-desc
            p No legal setup abroad? We help you hire full-time employees legally while minimizing overhead costs.
            p Our end-to-end services cover compliance, visas, onboarding, team management, payroll, taxes, and benefits. Automated tools streamline reporting and ensure smooth process management.
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") Request More Information
              ArrowRight(class="inline-arrow")
      .service-item(id="service-contractor" v-scroll-show="{ delayOffset: 100 }")
        .service-content
          .service-title
            h3 Independent Contractor
          .service-desc
            p Need short-term, flexible employment in a new market? We help you hire independent contractors quickly, even without a local legal team.   
            p We provide compliant contract templates and a streamlined online signing process to ensure global employees meet local regulations. Background checks are conducted to prevent legal risks, and we offer payroll services with transparent fees.
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") Request More Information
              ArrowRight(class="inline-arrow")
        .figure-area
          figure
            img(src="~/assets/images/index/contractor.webp" alt="Contractor")

      .service-item(id="service-peo" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/peo.webp" alt="Human Resource Outsourcing")
        .service-content
          .service-title
            h3 Human Resource Outsourcing
          .service-desc
            p Already set up a legal entity but want to offload HR tasks? We handle your payroll, taxes, insurance, benefits, onboarding, and more.
            p Our services are flexible and customized to meet your needs, ensuring compliance across regions while saving you time and costs.
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") Request More Information
              ArrowRight(class="inline-arrow")
      .service-item(id="service-fintech" v-scroll-show="{ delayOffset: 100 }")
        .service-content
          .service-title
            h3 FinTech-Driven Global Payroll Solutions
          .service-desc
            p SmartDeer’s platform supports payroll processing in over 150 currencies, enabling seamless and efficient payments to employees and contractors worldwide. With competitive exchange rates, robust hedging capabilities, and batch payment functionality, SmartDeer significantly reduces international transfer costs and minimizes the impact of exchange rate fluctuations on payroll expenses.
            p Additionally, SmartDeer’s expertise in compliance and tax management ensures smooth global HR operations, making it a trusted partner for international employment solutions.
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") Request More Information
              ArrowRight(class="inline-arrow")
        .figure-area
          figure
            img(src="~/assets/images/index/fintech.png" alt="FinTech-Driven Global Payroll Solutions")

      .service-item(id="service-saas" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            video(id="video-player" class="video-js" preload="auto" controls="true")
              source(src="https://static.smartdeer.com/Global_HR_SaaS.mp4", type="video/mp4")
        .service-content
          .service-title
            h3 Global HR SaaS
          .service-desc
            p SmartDeer provides enterprises with professional, integrated solutions for global expansion through a unique "HR Services + SaaS System" model. The SmartDeer Global HR SaaS platform empowers businesses to achieve data-driven, digitalized global HR management.
            p It enables global employees, HR teams, and line managers to efficiently manage HR processes and related tasks while maintaining and centralizing core HR data for streamlined operations.
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") Request More Information
              ArrowRight(class="inline-arrow")
mixin advantage
  section.advantages
    .section-title
      h2 Why choose us?

    .advantage-list(v-scroll-show="{ delayOffset: 100 }")
      .advantage-item(style="transition-delay:0s;")
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-global.svg" alt="Global Connected")
        .advantage-title Global Network
        .advantage-content Supporting your business 24/7 across 150+ countries.

      .advantage-item(style="transition-delay:0.4s;")
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-professional-team.svg" alt="Compliance-ready")
        .advantage-title Compliance-Ready
        .advantage-content Ensuring global policy and data compliance at every step.

      .advantage-item(style="transition-delay:.8s;")
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-service.svg" alt="24-hours service response")
        .advantage-title 24/7 Support
        .advantage-content Bilingual service in Chinese and English for fast responses.

      .advantage-item(style="transition-delay:1.2s;")
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-price.svg" alt="Competitive Service Fee")
        .advantage-title Competitive Pricing
        .advantage-content High-quality services at the best possible rates.

mixin lifecycle
  section.lifecycle()
    .section-title
      h2 Full-Life Cycle Management
    .lifecycle-list(v-scroll-show="{ delayOffset: 100 }")
      .lifecycle-item(style="transition-delay: .1s")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/recruitment.svg")
        .lifecycle-item-title Recruitment


      .lifecycle-item(style="top:20px;transition-delay: .2s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/compliance.svg")
        .lifecycle-item-title Compliance

      .lifecycle-item(style="top:40px;transition-delay: .3s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/contract.svg")
        .lifecycle-item-title  Contracts Signing

      .lifecycle-item(style="top:60px;transition-delay: .4s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/on-boarding.svg")
        .lifecycle-item-title On-boarding

      .lifecycle-item(style="top:40px;transition-delay: .5s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/management.svg")
        .lifecycle-item-title Management

      .lifecycle-item(style="top:20px;transition-delay: .6s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/payment.svg")
        .lifecycle-item-title Payment
      
      .lifecycle-item(style="transition-delay: .7s;")
        .lifecycle-item-icon
          figure 
            img(src="~/assets/images/index/off-boarding.svg")
        .lifecycle-item-title Off-boarding

    .lifecycle-repeat
      figure
        img(src="~/assets/images/index/repeat.svg" alt="重复")

mixin process
  section.process()
    .section-title
      h2 How It Works

    .process-list
      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-right
          .process-num 01

        .process-content-wrapper
          .process-content
            .process-title
              h3 Global Services Coverage, Localized Recruitment Across Industries
            .process-desc
              p With 80+ recruitment consultants spanning China, Southeast Asia, North America, the Middle East, and beyond, we specialize in IT, SaaS, Games, Intelligent Manufacturing, and more. From operations and BD to sales and engineering roles, we offer tailored recruitment services worldwide.
          .figure-area
            figure
              img(src="~/assets/images/index/en/recriument.png" alt="Global Services Coverage, Localized Recruitment Across Industries")

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-left
          .process-num 02

        .process-content-wrapper 
          .figure-area
            figure
              img(src="~/assets/images/index/compliance.webp" alt="Visa Services & Compliance Support")
          .process-content
            .process-title
              h3 Visa Services & Compliance Support
            .process-desc
              p We provide end-to-end visa assistance, background checks, and compliance handling. Our services streamline onboarding, ensuring smooth processes and legal security while minimizing risks for global hires.
              
      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-right
          .process-num 03

        .process-content-wrapper
          .process-content
            .process-title
              h3 Select & Customize Contracts, Sign in Just 3 Minutes
            .process-desc
              p Choose a compliance-ready contract template based on your business model or employment plan. Complete the process—from drafting to electronic signing—within 3 minutes, fully supported by our online contracting system.

          .figure-area
            figure
              img(src="~/assets/images/index/en/contract.png" alt="Select & Customize Contracts, Sign in Just 3 Minutes")

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-left
          .process-num 04

        .process-content-wrapper 
          .figure-area
            figure
              img(src="~/assets/images/index/en/staff.png" alt="Streamline Onboarding with Automated Audits")
          .process-content
            .process-title
              h3 Streamline Onboarding with Automated Audits
            .process-desc
              p Upload employee information effortlessly for automatic platform review. Our online onboarding system ensures timely approvals, so employees are set up and ready to work without delays.

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-right
          .process-num 05

        .process-content-wrapper
          .process-content
            .process-title
              h3 Online Contract Management, Employee Records, Leave & Attendance
            .process-desc
              p Easily track contract progress, manage employee records, and oversee personnel changes. Support multiple clocking methods, automate approval processes, and monitor attendance with real-time updates.

          .figure-area
            figure
              img(src="~/assets/images/index/en/progress.png" alt="Online Contract Management, Employee Records, Leave & Attendance")

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-left
          .process-num 06

        .process-content-wrapper 
          .figure-area
            figure
              img(src="~/assets/images/index/en/pay.png" alt="Compensation, Payroll & Insurance Processing")
          .process-content
            .process-title
              h3 Compensation, Payroll & Insurance Processing
            .process-desc
              p Streamline reimbursements, allowances, and expense approvals with self-service tools. Automate payroll management while staying compliant with regional laws and regulations. Enable multi-currency payments with transparent exchange fees.

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-right
          .process-num 07

        .process-content-wrapper
          .process-content
            .process-title
              h3 Off-Boarding Coordination & Final Payment Settlement
            .process-desc
              p Easily manage resignations with scheduled applications, streamlined off-boarding, and final payments processed through the platform. Ensure compliance with local regulations at every step of the separation process.

          .figure-area
            figure
              img(src="~/assets/images/index/en/dimission.png" alt="Coordination of Off-boarding, Settlement of Final Payment, and Separation Procedures Handling")

mixin solutionCase
  section.solution(id="solution")
    .section-title
      h2 Solution
    .solution-list
      .solution-item.case1-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case1.png')
          .solution-content
            .solution-title
              h3 Client Success: A Leading Chinese ICT Solutions Provider – Best Practices in Global Team Expansion and Management
            .solution-desc(:style="getSolutionDescStyle('s1')")
              h4 Background & Challenges
              p Since launching its global expansion in 2020, this company has entered over 30 countries and regions, encountering specific needs such as:
              ol
                li #[strong Global Employment:] EOR services and visa processing in 16 countries, along with HRO services in 5 countries.
                li #[strong Employer Cost Assessment:] Accurate multi-country employer cost calculations with expert advice on policy compliance.
                li #[strong Policy Advisory & Rapid Support:] Keeping up with changing employment policies worldwide and responding quickly.
              h4 Key challenges faced include:
              ul
                li #[strong Complex Administrative Tasks:] Overseas HR teams were overwhelmed by daily inquiries requiring quick and accurate responses.
                li #[strong Difficult Visa Applications:] Target countries involved high-risk and complex visa processes.
                li #[strong Heavy Reimbursement Workload:] Large, concentrated reimbursement requests from business departments strained HR and finance teams.
              h4 Solutions Provided by SmartDeer
              p To address the company’s global needs and challenges, SmartDeer developed a comprehensive solution:
              ol
                li #[strong EOR and HRO Services]
                  ul
                    li Delivered EOR services and visa support across 16 countries.
                    li Provided localized HRO services in 5 countries, including payroll management, employee benefits, and labor contract handling.
                li #[strong Real-Time Policy Advisory]
                  ul
                    li SmartDeer’s expert team offered real-time advice on multi-country employment policies and tax regulations, enabling swift adjustments to hiring strategies.
                li #[strong Visa Support Services]
                  ul
                    li Comprehensive visa application assistance, from document preparation to submission, focusing on high-complexity countries and improving approval rates.
                li #[strong Automated Data Management]
                  ul
                    li SmartDeer’s platform streamlined reimbursement workflows with automated data submission and analysis, reducing manual effort.
                li #[strong Efficient Support Mechanisms]
                  ul
                    li Assigned a dedicated account manager and a 24/7 support team to ensure timely resolution of complex issues, with response times cut to under 24 hours.
              h4 Results Achieved
              ul
                li #[strong Efficient Global Operations:] Successfully helped the company hire teams in 16 countries and complete visa processing, ensuring seamless market entry.
                li #[strong 40% Reduction in Time Costs:] Automation significantly reduced HR teams’ administrative workload.
                li #[strong 95% Visa Approval Rate:] Expert support boosted visa success rates in high-difficulty regions.
                li #[strong 50% Improvement in Reimbursement Efficiency:] Automated data handling optimized reimbursement processes for business teams.
              h4 Client Testimonial
              p "SmartDeer has provided us with comprehensive support in our global expansion. From employment services to visa processing, they have demonstrated exceptional professionalism and efficiency. SmartDeer is an indispensable strategic partner in our international operations." -— #[strong Head of International Business]
            .solution-expand
              button(@click="toggle('s1')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s1") Expand
                span(v-else) Collapse
                ArrowDown(v-if="!solutionExpandStatus.s1" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case2-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-content
            .solution-title
              h3 A Leading Chinese Game Development Company – Pioneering Success in the Japanese Market
            .solution-desc(:style="getSolutionDescStyle('s2')")
              h4 Background
              p In 2022, this young Chinese game development company registered a legal entity in Japan, officially beginning its expansion into the Japanese market in 2023. As its first overseas venture, the company faced significant uncertainties, including the need to quickly build a local team and ensure compliance with Japan’s strict labor laws and complex HR requirements.
              h4 Specific challenges included:
              ul
                li #[strong Lack of Local HR Expertise:] The company had no internal HR team with expertise in Japan’s labor compliance and employee management processes.
                li #[strong Complex Labor Regulations:] Japan’s stringent labor laws required meticulous compliance across all personnel and labor management activities to avoid legal risks.
              h4 Solutions Provided by SmartDeer
              p SmartDeer 针对企业的初期需求和痛点，提供了全面且定制化的解决方案：
              ol
                li #[strong Comprehensive HRO Services]
                  ul
                    li Developed and filed employee management policies to meet local requirements.
                    li Streamlined onboarding workflows and prepared essential HR documentation.
                    li Set up data usage policies to ensure compliance with Japanese laws regarding employee personal information.
                li #[strong Local Legal and Compliance Support]
                  ul
                    li Provided hands-on support for employment contracts, payroll processing, and social insurance administration.
                    li Delivered real-time advice on labor law updates to help the company navigate Japan’s legal landscape.
                li #[strong Recruitment & Team Building]
                  ul
                    li Leveraged SmartDeer’s extensive local network to recruit highly qualified talent.
                    li Supported the rapid formation of a functional team to kickstart business operations.
                li #[strong Customized Support Services]
                  ul
                    li Delivered targeted, on-site support tailored to the company’s startup phase, ensuring a smooth transition into the Japanese market.
              h4 Results Achieved
              ul
                li #[strong Rapid Team Formation:] Successfully recruited and onboarded a local team in Japan within three months, ensuring timely business launch.
                li #[strong Compliance Assurance:] Achieved full compliance with Japanese labor regulations, eliminating potential legal risks.
                li #[strong 40% Time and Cost Savings:] Automated and streamlined HR processes significantly reduced operational costs and administrative workload.
              h4 Client Testimonial
              p "SmartDeer’s team provided not just standardized solutions but also customized services that deeply understood our needs as a first-time entrant into Japan. Their expertise and professionalism made our journey from zero to one in the Japanese market smooth and highly efficient." #[strong -- HR Lead, Client Company]
            .solution-expand
              button(@click="toggle('s2')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s2") Expand
                span(v-else) Collapse
                ArrowDown(v-if="!solutionExpandStatus.s2" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
          .solution-figure
            figure
              img(src='~/assets/images/index/case2.png')
      .solution-item.case3-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case3.png')
          .solution-content
            .solution-title
              h3 A Leading Smart Robotics Solutions Provider – Streamlined Global Payroll Management
            .solution-desc(:style="getSolutionDescStyle('s3')")
              h4 Background
              p As a global leader in the smart robotics industry, the company has rapidly expanded its operations across multiple industries and regions in recent years. With its growing international team, the company faced significant challenges in managing global payroll, including:
              ol
                li #[strong Diverse Payroll Regulations:] Complex differences in payroll laws, tax policies, and employee benefits across countries.
                li #[strong Fragmented Payment Processes:] Coordinating multiple payment channels for international payroll added time and operational costs.
                li #[strong Team Management Pressure:] The HR team struggled with large-scale, multi-regional payroll calculations and data management.
              h4 Key Challenges:
              ul
                li #[strong Compliance Assurance:] Ensuring payroll processes adhered to local laws to mitigate compliance risks.
                li #[strong Cost Optimization:] Reducing the impact of currency fluctuations and international transfer fees on payroll costs.
                li #[strong Efficiency Improvement:] Enhancing payroll processing efficiency to ensure timely payments for employees worldwide.
              h4 Solutions Provided by SmartDeer
              ol
                li #[strong Global Payroll Services]
                  ul
                    li Streamlined multi-currency payroll calculations and payments through the SmartDeer platform, simplifying international payment workflows.
                li #[strong Tax and Compliance Support]
                  ul
                    li Provided real-time monitoring and application of the latest payroll and tax policies across multiple countries to ensure compliance.
                li #[strong Exchange Rate Management]
                  ul
                    li Offered advanced currency locking services to mitigate exchange rate fluctuations and reduce payment costs.
                li #[strong Automated Payroll Management]
                  ul
                    li Deployed intelligent tools for payroll calculation and disbursement, significantly reducing repetitive tasks for HR teams and boosting operational efficiency.
                li #[strong Customized Reporting & Analytics]
                  ul
                    li Delivered detailed payroll reports and multidimensional data analysis, enabling the company to effectively control global labor costs.
              h4 Results Achieved
              ul
                li #[strong Zero Compliance Risks:] Managed payroll across 10+ countries while ensuring full compliance with local regulations.
                li #[strong 30% Cost Savings:] Optimized exchange rates and centralized payment processes significantly reduced international payroll costs.
                li #[strong 50% Efficiency Boost:] Automated payroll tools alleviated HR workloads, enhancing overall efficiency.
                li #[strong Enhanced Employee Satisfaction:] Ensured timely salary payments for the global team, boosting loyalty and job satisfaction.
              h4 Client Testimonial
              p "SmartDeer’s global payroll management services have enabled us to expand rapidly while ensuring compliance and efficiency in payroll operations. Their professional team and advanced tools have been a critical support for our global strategy." #[strong -- HR Lead, Client Company]
            .solution-expand
              button(@click="toggle('s3')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s3") Expand
                span(v-else) Collapse
                ArrowDown(v-if="!solutionExpandStatus.s3" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case4-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-content
            .solution-title
              h3 A Leading Fresh Food E-Commerce Platform – Comprehensive Recruitment and Employment Support for Middle East Expansion
            .solution-desc(:style="getSolutionDescStyle('s4')")
              h4 Background
              p As one of China’s top fresh food e-commerce platforms, the company achieved significant success in the domestic market and set its sights on the Middle East in 2023. To establish a strong foothold, the company needed to build a localized team comprising procurement, operations, business analysis, and marketing professionals while dispatching key personnel from China to support its initial operations in Saudi Arabia. However, the company faced several critical challenges during the expansion process:
              h4 Key Challenges
              ul
                li #[strong Entity Establishment and Compliance:] Without a legal entity in Saudi Arabia, direct employee hiring posed legal risks. Additionally, the company needed to meet Saudi labor law requirements, such as nationalization ratios (Saudization).
                li #[strong Complex Recruitment Needs:] The Middle Eastern talent market is unevenly distributed, making it challenging to recruit high-skilled talent for procurement, operations, business analysis, and marketing roles.
                li #[strong Cross-Border Employee Deployment:] Sending employees from China to Saudi Arabia required handling complex processes such as visa applications, work card issuance, and compliance with local regulations.
              h4 Solutions Provided by SmartDeer
              p SmartDeer developed a tailored and comprehensive solution to address the company’s needs:
              ol
                li #[strong EOR Service Support]
                  ul
                    li SmartDeer’s local entity directly employed the company’s staff, eliminating the need for the client to establish a legal entity.
                    li This ensured compliance while bypassing Saudization requirements, enabling the company to quickly launch operations in Saudi Arabia and the wider Middle East.
                li #[strong Regional Recruitment Services]
                  ul
                    li Leveraging SmartDeer’s extensive recruitment network in the Middle East, the company rapidly hired key talent for procurement, operations, business analysis, and marketing roles, covering Saudi Arabia and other major markets in the region.
                li #[strong Cross-Border Deployment Management]
                  ul
                    li SmartDeer provided full-process support for employees dispatched from China, including visa applications, work card processing, and labor documentation preparation, ensuring efficient and compliant cross-border deployment.
                li #[strong Ongoing Support & Advisory]
                  ul
                    li SmartDeer’s local team offered daily support with labor contract management, payroll processing, and regulatory advisory, reducing the client’s HR management burden in the Middle East.
              h4 Results Achieved
              ul
                li #[strong Rapid Team Formation:] Completed recruitment for critical roles in the Middle East within 2 months and successfully deployed 10 Chinese employees to Saudi Arabia, enabling smooth business operations.
                li #[strong Zero Compliance Risks:] Through SmartDeer’s entity-based employment model, the company met legal requirements, ensuring full compliance in Saudi Arabia and other regional markets.
                li #[strong Time and Cost Optimization:] Avoiding the need to establish a legal entity saved significant time and financial resources, allowing the company to focus on core business operations.
                li #[strong Improved Regional Efficiency:] With SmartDeer’s comprehensive support, the company achieved efficient market expansion in Saudi Arabia and the broader Middle East, successfully meeting its initial operational goals.
              h4 Client Testimonial
              p "SmartDeer’s support made our expansion into Saudi Arabia and the Middle East seamless. They helped us overcome local recruitment challenges and provided end-to-end support for cross-border deployments, ensuring all processes were compliant and efficient. They are a reliable partner in our international expansion efforts." #[strong -- HR Lead, Client Company]
            .solution-expand
              button(@click="toggle('s4')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s4") Expand
                span(v-else) Collapse
                ArrowDown(v-if="!solutionExpandStatus.s4" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
          .solution-figure
            figure
              img(src='~/assets/images/index/case4.png')
      .solution-item.case5-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case5.png')
          .solution-content
            .solution-title
              h3 A Global Blockchain Technology Platform – Comprehensive Global EOR Support
            .solution-desc(:style="getSolutionDescStyle('s5')")
              h4 Background
              p As a global leader in blockchain technology, this company focuses on building decentralized internet infrastructure while continuously attracting top technical and operational talent worldwide. With rapid business expansion, the company needed to hire employees across multiple countries to support global operations but faced significant challenges:
              h4 Key Challenges
              ul
                li #[strong Multi-Country Compliance:] Complex labor laws, tax regulations, and social benefit policies across different countries required fully compliant global hiring processes.
                li #[strong Efficiency and Cost Optimization:] Rapid expansion demanded quick hiring and onboarding of global teams while minimizing administrative and management costs.
                li #[strong Multi-Currency Payroll Management:] Handling payroll in multiple currencies posed financial risks due to exchange rate fluctuations.
              h4 Solutions Provided by SmartDeer
              p SmartDeer offered a one-stop global EOR (Employer of Record) solution, enabling the company to expand its teams across multiple countries quickly and compliantly:
              ol
                li #[strong Compliance Management]
                  ul
                    li Leveraged SmartDeer’s global network to legally hire employees in different countries.
                    li Ensured that every labor contract and payroll process complied with local regulations.
                li #[strong Fast Employee Onboarding]
                  ul
                    li Facilitated efficient onboarding, including contract signing, social insurance registration, and tax processing, to get employees operational quickly.
                li #[strong Multi-Currency Payroll Services]
                  ul
                    li Supported payroll in over 150 currencies via the SmartDeer platform.
                    li Integrated exchange rate locking features to mitigate financial risks from currency fluctuations.
                li #[strong Ongoing HR Support]
                  ul
                    li Provided continuous HR support, including policy advisory, employee management, and contract renewals, reducing the burden on the client’s HR team.
              h4 Results Achieved
              ul
                li #[strong Rapid Global Expansion:] Successfully helped the client establish teams in multiple countries within three months, supporting their global growth strategy.
                li #[strong Compliance Assurance:] Ensured all hiring and payroll processes adhered to local laws, avoiding potential legal and tax risks.
                li #[strong 30% Cost Savings:] Centralized HR management reduced administrative costs and time investment by 30%.
                li #[strong Improved Employee Satisfaction:] Accurate and timely payroll distribution, coupled with robust HR support, increased employee satisfaction and loyalty.
              h4 Client Testimonial
              p "SmartDeer’s global EOR services allow us to focus on our core business without worrying about the complexities of hiring and compliance in different countries. Their professional support and efficient services are a vital pillar of our global operations." #[strong -- HR Lead, Client Company]
            .solution-expand
              button(@click="toggle('s5')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s5") Expand
                span(v-else) Collapse
                ArrowDown(v-if="!solutionExpandStatus.s5" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case6-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-content
            .solution-title
              h3 A Leading Online Education Platform – Hong Kong HRO Services for Streamlined HR Management
            .solution-desc(:style="getSolutionDescStyle('s6')")
              h4 Background
              p As one of China’s top online education platforms, the company focuses on delivering high-quality educational resources to students worldwide. With its expanding international presence, the company established an operations center in Hong Kong to support its global business growth. However, the specific requirements and complexities of HR management in Hong Kong posed several challenges:
              h4 Key Challenges
              ul
                li #[strong Payroll and Statutory Benefits Management:] Handling payroll, mandatory benefits such as MPF contributions, and ensuring all processes complied with local regulations.
                li #[strong Data Management and Compliance:] Centralizing employee information and payroll data while adhering to Hong Kong's privacy protection and labor laws.
                li #[strong HR Efficiency Enhancement:] Optimizing HR management processes using technology to reduce administrative costs and improve operational efficiency.
              h4 Solutions Provided by SmartDeer
              ol
                li #[strong Payroll and Benefits Management Services]
                  ul
                    li SmartDeer managed payroll calculations and disbursements, ensuring accurate and timely salary payments.
                    li Handled MPF contributions and other statutory benefits, ensuring full compliance with legal requirements and reducing compliance risks.
                li #[strong HR SaaS Platform Support]
                  ul
                    li Delivered an integrated HR SaaS platform, enabling seamless management of employee information, attendance, payroll data, and contract records.
                    li The platform supported real-time data updates and automated report generation, minimizing manual errors and saving time.
                li #[strong Daily HR Support Services]
                  ul
                    li Provided policy advisory and operational support, keeping the client informed of Hong Kong labor laws and policy updates.
                    li Assisted with onboarding and offboarding processes, ensuring all actions were legally compliant.
              h4 Results Achieved
              ul
                li #[strong 40% Increase in Management Efficiency:] The HR SaaS platform significantly optimized payroll and data management workflows, reducing the daily workload for the HR team.
                li #[strong Risk-Free Compliance:] Professional payroll and benefits management eliminated legal risks and boosted employee satisfaction and loyalty.
                li #[strong 30% Cost Savings:] Outsourcing HRO services effectively reduced operational HR costs, allowing the company to focus resources on core business growth.
                li #[strong Real-Time Data Control:] Centralized and digitalized employee data management provided the company with efficient support for decision-making.
              h4 Client Testimonial
              p "SmartDeer’s Hong Kong HRO services and HR SaaS platform have greatly improved our HR management efficiency. Their professionalism in payroll and statutory benefits management has been exceptional. SmartDeer is a vital partner in our global expansion efforts." #[strong -- HR Lead, Client Company]
            .solution-expand
              button(@click="toggle('s6')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s6") Expand
                span(v-else) Collapse
                ArrowDown(v-if="!solutionExpandStatus.s6" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
          .solution-figure
            figure
              img(src='~/assets/images/index/case6.png')

mixin contactForm
  .contact-form
    //- 吃瓜就要付出代价，否则影响页面跳转
    //- https://github.com/element-plus/element-plus/pull/9731
    client-only
      el-dialog(v-model="status.showForm" title="" :width="600")
        contact-us-form(@submit="submitSuccess" lang="en")

mixin anchor
  .anchor
    .consultant(@click="status.showConsultantCode = !status.showConsultantCode")
      figure
        img(src="~/assets/images/index/anchor-avatar-en.png" )
    .consultant-code(v-show="status.showConsultantCode")
      .close(@click="status.showConsultantCode = false")
      figure
        img(src="~/assets/images/index/anchor-code-en.png" )

mixin botButton
  .bot-container(@click="toggleChat")
    img(src="~/assets/images/index/bot_logo_en.png")

mixin goTop
  .go-top-container(@click="smoothScrollTo(500, 0)")
    img(src="~/assets/images/index/top_icon.png")

.index-page
  +header

  main
    +customer
    +service
    +advantage
    +lifecycle
    +process
    +solutionCase

  +contactForm
  site-footer(lang="en" @contact-us="()=>{status.showForm=true}")
  +botButton
  +goTop
</template>

<script lang="ts" setup>
import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem, ElCarousel, ElCarouselItem } from 'element-plus'
import phoneArea from '~/assets/utils/global-phone-area'
import langTool from '~/assets/utils/lang'
import CustomerList from '@/components/customer-list.vue'
import {getQueryString} from "assets/utils";
import {ref} from "vue";
import videojs from "video.js";
import {ArrowUp, ArrowDown, ArrowRight} from '@element-plus/icons-vue';

definePageMeta({ layout: 'basic' })
useHead({
  htmlAttrs: { lang: 'en' },
  title: 'SmartDeer - Global Employment & Recruitment Solutions | International HR Services',
  meta: [
    // Basic SEO
    { name: 'description', content: 'SmartDeer provides comprehensive global employment and recruitment solutions. Professional EOR services, international payroll, compliance management for companies expanding overseas. Covering 150+ countries with expert support.' },
    { name: 'keywords', content: 'global employment, international recruitment, EOR services, global payroll, HR outsourcing, overseas expansion, compliance management, international hiring, global HR solutions, remote work' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },

    // Canonical and hreflang
    { name: 'canonical', content: 'https://smartdeer.work/en/' },

    // Open Graph
    { property: 'og:title', content: 'SmartDeer - Global Employment & Recruitment Solutions | International HR Services' },
    { property: 'og:description', content: 'SmartDeer provides comprehensive global employment and recruitment solutions. EOR services, international payroll, compliance management for companies expanding overseas.' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://smartdeer.work/en/' },
    { property: 'og:image', content: 'https://smartdeer.work/images/tg_banner.png' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: 'en_US' },

    // Twitter Card
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'SmartDeer - Global Employment & Recruitment Solutions' },
    { name: 'twitter:description', content: 'SmartDeer provides comprehensive global employment and recruitment solutions. EOR services, international payroll, compliance management for companies expanding overseas.' },
    { name: 'twitter:image', content: 'https://smartdeer.work/images/tg_banner.png' },
    { name: 'twitter:image:alt', content: 'SmartDeer - Global HR Solutions' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: 'https://smartdeer.work/en/' },

    // Hreflang for multilingual SEO
    { rel: 'alternate', hreflang: 'en', href: 'https://smartdeer.work/en/' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://smartdeer.work/zh/' },
    { rel: 'alternate', hreflang: 'ja-JP', href: 'https://smartdeer.work/ja/' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://smartdeer.work/en/' }
  ],
  script: [
    // Structured Data - Organization
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "SmartDeer",
        "description": "Global employment and recruitment solutions provider",
        "url": "https://smartdeer.work",
        "logo": "https://smartdeer.work/images/logo.png",
        "contactPoint": {
          "@type": "ContactPoint",
          "contactType": "customer service",
          "availableLanguage": ["English", "Chinese", "Japanese"]
        },
        "sameAs": [
          "https://www.linkedin.com/company/smartdeer-global/"
        ],
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "Global"
        },
        "service": [
          {
            "@type": "Service",
            "name": "Global Recruitment",
            "description": "International talent acquisition and recruitment services"
          },
          {
            "@type": "Service",
            "name": "Employer of Record (EOR)",
            "description": "Legal employment services without local entity setup"
          },
          {
            "@type": "Service",
            "name": "Global Payroll",
            "description": "International payroll management and compliance"
          },
          {
            "@type": "Service",
            "name": "HR Outsourcing",
            "description": "Comprehensive human resources management services"
          }
        ]
      })
    }
  ]
})

const scrollItems = []

const solutionExpandStatus = ref({
  s1: false,
  s2: false,
  s3: false,
  s4: false,
  s5: false,
  s6: false
})

const defaultHeight = '380px'
const fullHeight = '2000px'

function getSolutionDescStyle(key) {
  return {
    maxHeight: solutionExpandStatus.value[key] ? fullHeight : defaultHeight,
    overflow: 'hidden',
    transition: 'max-height 1s ease-out'
  }
}

function toggle(key) {
  solutionExpandStatus.value[key] = !solutionExpandStatus.value[key];
}

const status = reactive({
  showForm: false,
  showConsultantCode: false
})

const form = reactive({
  name: '',
  company: '',
  service: '',
  countryCode: '+86',
  mobile: '',
  email: '',
  extra: ''
})

const serviceOptions = ref([
  { label: 'Recruitment' },
  { label: 'EOR' },
  { label: 'Contractor' },
  { label: 'PEO' },
  { label: 'Other' }
])

const countryCode = ref(phoneArea)

function scrollTo(tag) {
  const ele = window.document.querySelector(tag)
  if (ele) window.scrollTo({
    top: ele.offsetTop,
    behavior: 'smooth'
  })
}


function smoothScrollTo(duration, target) {
  const start = window.scrollY
  const startTime = performance.now()

  function scrollStep (currentTime) {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)
    const newScrollY = start + (target - start) * progress
    window.scrollTo(0, newScrollY)
    if (progress < 1) {
      requestAnimationFrame(scrollStep)
    }
  }
  requestAnimationFrame(scrollStep)
}

const cozeWebSDK = ref(null)

function toggleChat() {
  console.log(cozeWebSDK.value)
  if (cozeWebSDK.value) {
    cozeWebSDK.value.showChatBot()
  }
}

// 自定义指令
const vScrollShow = {
  mounted: (el, bindings) => {
    const delayOffset = bindings.value.delayOffset || 0
    scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el })
  }
}

let timer = null
function scroll(e) {
  if (timer) return
  timer = setTimeout(() => {
    const offset = window.scrollY + window.innerHeight
    scrollItems.forEach((item, index) => {
      if (item.offsetTop < offset) {
        item.el.setAttribute('show', true)
        scrollItems.splice(index, 1)
      }
    })
    timer = null
  }, 30)
}

onMounted(() => {
  window.addEventListener('scroll', scroll)

  const curScroll = getQueryString('scroll')
  if (curScroll) {
    scrollTo('#' + curScroll)
  }

  const player = videojs('video-player', {
    controls: true
  });

  cozeWebSDK.value = new CozeWebSDK.WebChatClient({
    config: {
      botId: '7439335660751716386'
    },
    ui: {
      base: {
        icon: 'https://static.smartdeer.com/bot_logo.png',
        layout: 'pc',
        zIndex: 1000
      },
      chatBot: {
        title: '顾问 杰哥',
        uploadable: false,
      },
      asstBtn: {
        isNeed: false
      },
      footer: {
        isShow: true,
        expressionText: 'Powered by SmartDeer.'
      }
    }
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', scroll)
})

function switchLang(lang) {
  langTool.swithLang(lang)
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('Submit success!')
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/en.scss';

/* 旋转 */
@keyframes aniRotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.index-page {
  font-family: Helvetica;

  header {
    background-color: #FFF6EC;
    height: 800px;
    margin-bottom: 180px;
    overflow: hidden;
    position: relative;

    .header-banner {
      width: 1204px;
      box-sizing: border-box;
      margin: 0 auto;
      display: flex;
      margin-top: 8px;

      .header-banner-text {

        h2.slogon {
          height: 162px;
          font-size: 58px;
          font-weight: bold;
          color: #000000;
          line-height: 78px;
          padding-top: 97px;
        }

        h1.title {
          height: 47px;
          font-size: 36px;
          font-weight: bold;
          color: #000000;
          line-height: 36px;
          margin-top: 28px;
        }

        .desc {
          width: 641px;
          font-size: 20px;
          color: #000000;
          line-height: 33px;
          margin-top: 28px;
          position: relative;

          span {
            position: relative;
            z-index: 1;
          }

          figure {
            position: absolute;
            left: -56px;
            top: -6px;
            
            img{
              width: 140px;
              height: 140px;
            }
          }
        }
      }

      .header-banner-image {
        user-select: none;
        position: relative;
        width: 638px;
        min-width: 638px;
        height: 637px;
        margin-left: 20px;
        padding: 19px;

        .global-min_1{
          position: absolute;
          top: 50%;
          left: 50%;
          width: 598px;
          height: 600px;
          margin-top: -300px;
          margin-left: -299px;
          z-index: 1;
          
          animation: aniRotate 18s linear infinite;
          animation-fill-mode: forwards;

          img {
            transform: rotate(45deg);
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        .global-min_2{
          position: absolute;
          top: 50%;
          left: 50%;
          z-index: 2;
          width: 480px;
          height: 480px;
          margin-top: -240px;
          margin-left: -240px;

          animation: aniRotate 30s linear infinite;
          animation-fill-mode: forwards;

          img {
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        .global-min_3{
          position: absolute;
          top: 50%;
          left: 50%;
          width: 598px;
          height: 600px;
          margin-top: -300px;
          margin-left: -299px;
          z-index: 3;

          img {
            width: 559px;
            height: 556px;
            display: block;
            position: absolute;
            top: 19px;
            left: -10px;
          }
        }
      }
    }
  }

  main {
    section {
      margin-bottom: 160px;
      &:last-child {
        margin-bottom: 0;
      }
    }

    section .section-title {
      text-align: center;
      margin-bottom: 40px;

      h2 {
        font-size: 48px;
        font-weight: 500;
        line-height: 67px;
        font-weight: bold;
      }

      p {
        font-size: 18px;
        color: #999999;
        letter-spacing: 5px;
        margin-top: 8px;
        line-height: 22px;
        margin-bottom: 0;
      }
    }
  }
}

.anchor {
  position: fixed;
  right: 40px;
  bottom: 40px;
  z-index: 99;

  .consultant {
    width: 91px;
    height: 101px;
    cursor: pointer;
  }
  .consultant-code {
    width: 236px;
    height: 321px;
    position: fixed;
    right: 130px;
    bottom: 25px;

    .close {
      width: 20px;
      height: 20px;
      cursor: pointer;
      position: absolute;
      top: 22px;
      right: 19px;
      // background: #000;
    }
  }

  figure {
    width: 100%;
    height: 100%;

    img{
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}

// 服务客户模块
section.customer {
  width: 1204px;
  // padding: 0 20px;
  margin: 0 auto;
}

// 服务模式模块
section.service {
  overflow: hidden;
  min-width: 1280px;

  .service-contact {
    .service-contact-button {
      background: transparent;
      line-height: 28px;
      font-size: 14px;
      border-radius: 20px;
      padding: 0 20px;
      border: 1px #000 solid;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }

  .service-list {
    width: 1204px;
    margin: 0 auto;
    // padding: 0 20px;

    .service-item {
      display: flex;
      justify-content: space-between;
      padding: 58px 0px;

      &:nth-child(2n+1) {
        .figure-area {
          right: -100px;
        }

        .service-content {
          left: -100px;
        }
      }

      &:nth-child(2n) {
        .figure-area {
          left: -100px;
        }

        .service-content {
          right: -100px;
        }
      }

      &[show="true"] {
        &:nth-child(2n+1) {
          .figure-area {
            right: 0px;
            opacity: 1;
          }

          .service-content {
            left: 0px;
            opacity: 1;
          }
        }

        &:nth-child(2n) {
          .figure-area {
            left: 0px;
            opacity: 1;
          }

          .service-content {
            right: 0px;
            opacity: 1;
          }
        }
      }

      .figure-area {
        position: relative;
        opacity: 0;
        min-height: 333px;
        transition: all .5s;
        figure {
          img, #video-player{
            width: 587px;
          }
          #video-player {
            height: 333px;
            background: transparent;

            video {
              border-radius: 20px;
              overflow: hidden;
            }
          }
        }
      }

      .service-content {
        position: relative;
        opacity: 0;
        width: 500px;
        flex: 0 0 auto;
        padding: 0 16px;
        box-sizing: border-box;
        transition: all .5s;

        .service-title {
          color: #333333;

          h3 {
            font-size: 36px;
            font-weight: bold;
            line-height: 1;
            margin-bottom: 8px;
          }

          p {
            font-size: 18px;
            color: #333333;
            letter-spacing: 0;
            line-height: 1;
            margin: 0;
            padding: 0;
          }
        }

        .service-desc {
          font-size: 16px;
          color: #454545;
          line-height: 27px;

          p{
            text-indent: 1em;
            position: relative;

            &::before{
              content: '';
              width: 3px;
              height: 3px;
              background: #FF7F00;
              position: absolute;
              left: 0;
              top: 12px;
            }
          }
        }
      }
    }
  }
}

section.advantages {
  width: 1204px;
  margin: 0 auto;

  .advantage-list {
    display: flex;
    justify-content: space-between;

    &[show="true"] {
      .advantage-item {
        transform: scale(1);
        transition: all .4s;
      }
    }

    .advantage-item {
      padding-bottom: 40px;
      width: 279px;
      background-color: #FEEFDF;
      border-radius: 18px;
      transform: scale(0);
      transition: all .4s;
      color: #333333;
      text-align: center;
      height: 318px;
      padding-top: 48px;
      box-sizing: border-box;

      .advantage-icon-area {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 64px;
        }
      }

      .advantage-title {
        font-size: 28px;
        margin-top: 24px;
        line-height: 28px;
      }

      .advantage-content {
        font-size: 18px;
        margin: 0 auto;
        margin-top: 14px;
        width: 222px;
      }
    }
  }
}

section.lifecycle {
  width: 1204px;
  // padding: 0 20px;
  margin: 0 auto;

  .lifecycle-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 86px;

    &[show="true"] {
      .lifecycle-item {
        left: 0;
        opacity: 1;
        transition: all .4s;
      }
    }

    .lifecycle-item {
      position: relative;
      left: -40px;
      opacity: 0;

      .lifecycle-item-icon {
        background: #FFFFFF;
        // box-shadow: 0 2px 20px 0 rgba(254, 145, 18, 0.10);
        height: 152px;
        width: 152px;
        border-radius: 50%;
        margin-bottom: 32px;
        position: relative;

        .arrow {
          background-image: url("~/assets/images/index/arrow.svg");
          height: 14px;
          width: 12px;
          background-size: contain;
          background-repeat: no-repeat;
          position: absolute;
          top: 67px;
          right: -18px;
        }
      }

      .lifecycle-item-title {
        text-align: center;
      }
    }
  }

  .lifecycle-repeat {
    width: 1000px;
    margin: 0 auto;

    img {
      width: 100%;
    }
  }
}

// 服务模式模块
section.process {
  .process-list {
    .process-item {
      position: relative;
      min-width: 1280px;
      margin-bottom: 160px;
      height: 506px;

      &[show="true"] {
        .figure-area {
          top: 0;
          transition: all .8s;
          opacity: 1;
        }
      }

      &:last-child {
        .process-content-wrapper {
          padding-bottom: 0;
        }
      }

      &:nth-child(6){
        .process-content-wrapper{
          position: relative;
          top: -46px;
        }
      }

      .process-content-wrapper {
        width: 1253px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 506px;
      }

      .process-background {
        width: 50%;
        height: 506px;
        position: absolute;
        z-index: -1;

        .process-num {
          font-family: Verdana;
          font-size: 64px;
          color: #333333;
          position: absolute;
          line-height: 1;
          top: 100px;
        }

        &.layout-left {
          left: 0;
          background: linear-gradient(270deg, #FFFFFF 0%, #FFF2E3 100%);

          .process-num {
            right: 610px
          }
        }

        &.layout-right {
          right: 0;
          background-image: linear-gradient(90deg, #FFFFFF 0%, #FFF2E3 100%);

          .process-num {
            left: 610px
          }
        }
      }

      .figure-area {
        flex: 1 1 auto;
        display: flex;
        justify-content: center;
        position: relative;
        top: 120px;
        transition: all .8s;
        opacity: 0;

        img {
          width: 659px;
          filter: drop-shadow(0 2px 16px RGBA(0, 0, 0, .18));
        }
      }

      .process-content {
        margin-top: 48px;
        width: 500px;
        flex: 0 0 auto;
        padding: 0 8px;
        box-sizing: border-box;

        .process-title {

          h3 {
            font-size: 32px;
            font-weight: 500;
            line-height: 40px;
            font-weight: bold;
            color: #222222;
          }

          p {
            width: 484px;
            font-size: 16px;
            color: #454545;
            line-height: 26px;
            margin-top: 24px;
          }
        }

        .process-desc {
          font-size: 16px;
          color: #666666;
          line-height: 27px;
        }
      }
    }
  }
}

.contact-form {
  .form-title {
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    margin-top: -30px;
  }

  .form-body {
    padding: 0 80px;

    .mobile {
      line-height: 46px;
      border: 1px solid;
      border-radius: 8px;
      width: 100%;
    }
  }
}
// 服务模式模块
section.solution {
  overflow: hidden;
  min-width: 1280px;

  .solution-list {
    margin: 0 auto;

    .case1-bg {
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(239, 223, 207, 0.4));
    }
    .case2-bg {
      background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
    }
    .case3-bg {
      background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rgba(149, 201, 246, 0.4));
    }
    .case4-bg {
      background: linear-gradient(to bottom, rgba(251, 235, 186, 0.04), rgba(251, 235, 186, 0.4));
    }
    .case5-bg {
      background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
    }
    .case6-bg {
      background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rrgba(149, 201, 246, 0.4));
    }

    .solution-item {
      padding: 68px 0;

      &:nth-child(2n+1) {
        .figure-area {
          right: -70px;
        }

        .service-content {
          left: -70px;
        }
      }

      &:nth-child(2n) {
        .figure-area {
          left: -70px;
        }

        .service-content {
          right: -70px;
        }
      }

      &[show="true"] {
        &:nth-child(2n+1) {
          .solution-figure {
            right: 0;
            opacity: 1;
          }

          .solution-content {
            left: 0;
            opacity: 1;
          }
        }

        &:nth-child(2n) {
          .solution-figure {
            left: 0;
            opacity: 1;
          }

          .solution-content {
            right: 0;
            opacity: 1;
          }
        }
      }

      .solution-wrapper {
        width: 1204px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;

        .solution-figure {
          position: relative;
          opacity: 0;
          min-height: 333px;
          transition: all .5s;

          figure {
            img {
              width: 400px;
            }
          }
        }

        .solution-content {
          position: relative;
          opacity: 0;
          width: 734px;
          flex: 0 0 auto;
          box-sizing: border-box;
          transition: all .5s;

          .solution-title {
            color: #333333;

            h3 {
              font-size: 30px;
              font-weight: 500;
              font-family: PingFangSC-Medium, PingFang SC;
              line-height: 40px;
              color: #333333;
              margin: 0;
            }

            p {
              font-size: 18px;
              font-family: DIN-Regular, DIN;
              font-weight: 400;
              color: #999;
              line-height: 26px;
              margin: 10px 0 0 0;
            }
          }

          .solution-desc {
            font-size: 16px;
            color: #333;
            line-height: 27px;
            h4 {
              margin: 20px 0 10px 0;
              font-weight: bold;
            }
            ol {
              position: relative;
              padding-left: 20px;
              margin: 0
            }
            ol li {
              list-style: decimal;
              position: relative;
            }
            ul {
              position: relative;
              padding-left: 20px;
              margin: 0;
            }
            ul li {
              position: relative;
            }

            li>ul {
              padding-left: 20px;
            }
            li>ul li{
              list-style: circle;
            }
            p{
              position: relative;
              margin: 0;
            }
          }

          .solution-expand {
            margin-top: 20px;
            .solution-toggle {
              background: transparent;
              border: 1px #000 solid;
              padding: 0 20px;
              line-height: 28px;
              border-radius: 15px;
              cursor: pointer;
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }
  }
}
.inline-arrow {
  width: 16px;
  height: 16px;
  margin-left: 5px;
}
.bot-container {
  z-index: 1000;
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 124px;
  right: 50px;
  transition: transform 0.3s ease;
  &:hover {
    transform: scale(1.16);
  }
  img {
    width: 80px;
    height: 91px;
  }
}
.go-top-container {
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 30px;
  right: 55px;

  img {
    width: 70px;
    height: 70px;
  }
}
</style>