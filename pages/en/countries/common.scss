.contries {
  width: 1204px;
  margin: 0 auto;

  .contries-banner {
    min-height: 272px;
    position: relative;
    z-index: 10;
    .banner {
      width: 100%;

      figure,img {
        width: 100%;
        height: 100%;
        display: block;
        border-radius: 8px;
      }
    }

    .flag {
      width: 200px;
      // height: 105px;
      position: absolute;
      padding: 4px;
      left: 24px;
      bottom: -24px;
      border-radius: 8px;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.03);
      background: #fff;
      box-sizing: content-box;

      img {
        width: 100%;
        height: 100%;
        display: block;
        border-radius: 8px;
      }
    }

    .form-box {
      width: 436px;
      // height: 594px;
      background: #FFFFFF;
      box-shadow: 0px 0px 16px 0px rgba(0,0,0,0.06);
      border-radius: 4px;
      border: 1px solid rgba(151,151,151,0.2);
      position: absolute;
      right: 24px;
      bottom: -484px;
      // bottom: -402px;
      
      :deep(.contact-us-form ) {
        .form-title {
          padding-top: 32px;
          margin-top: 0;
          font-size: 24px;
          text-align: left;
          padding-left: 24px;
          margin-bottom: 17px;
        }

        .form-body {
          padding: 0 24px;
        }
      }
    }
  }

  .contries-content {
    padding-top: 44px;
    padding-bottom: 81px;

    h1 {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
      line-height: 38px;
    }
    .desc {
      font-size: 18px;
      color: #222222;
      line-height: 32px;
      letter-spacing: 2px;
      margin-top: 4px;
    }

    h3 {
      margin-top: 40px;
      font-size: 20px;
      font-weight: bold;
      color: #111111;
      line-height: 24px;
    }

    .list {
      margin-top: 24px;

      .list-item {
        font-size: 16px;
        color: #222222;
        line-height: 24px;
        padding-left: 24px;
        position: relative;
        margin-top: 20px;

        &:last-child {
          margin-bottom: 20px;
        }

        &::before {
          content: '';
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #FF8600;
          position: absolute;
          left: 0px;
          top: 8px;
        }
        
        .list-item {
          margin-top: 8px;
          
          &::before {
            background: #fff;
            border: 1px solid #FF8600;
            box-sizing: border-box;
          }
        }
      }

      .wrap {
        width: 640px;
      }
    }
  }
}