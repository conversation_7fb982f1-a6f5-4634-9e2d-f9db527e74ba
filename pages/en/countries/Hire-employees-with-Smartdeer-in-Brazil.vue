<template lang="pug">
.contries-page
  site-header(lang="en" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title Employer Record in Brazil
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="en" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="en" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'Employer Record in Brazil',
  ogDescription: 'Basic information Capital: Brasília Time zone: GMT-5 La […]',
  ogSiteName: 'SmartDeer',
  description: 'Basic information Capital: Brasília Time zone: GMT-5 La […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: 'Employer Record in Brazil'
})
const status = reactive({
  showForm: false
})
const langSimple = 'en';
const pageTitle = 'Employer Record in Brazil';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/th-1.jpeg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/OIP-C-1.jpeg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">Basic information</h2><p>Capital: Brasília</p><p>Time zone: GMT-5</p><p>Languages: Portuguese</p><p>Currency code:BRL</p><h2 class="wp-block-heading has-large-font-size">Human Resources at a Glance&nbsp;</h2><h3 class="wp-block-heading has-large-font-size">Contract Terms</h3><p>Contracts must be in Portuguese or English, and can be bilingual. They must be in writing and signed by both parties.<br>A contract must include:</p><ul><li>Name</li><li>Start date</li><li>Place of work</li><li>Length of the employment</li><li>Job description</li><li>Termination conditions</li></ul><h2 class="wp-block-heading has-large-font-size">Guidelines Regarding Probation Period/Trial Period</h2><p>Probation periods are not mandatory. There is no minimum probation period. The maximum probation period is 90 days (3 months).</p><p>Fixed-term contracts cannot have probation periods.</p><h2 class="wp-block-heading has-large-font-size">Regulations and Rules Regarding Working Hours</h2><p>Standard working hours are 8 hours per day, 40 hours per week. The standard work week is from Monday to Friday.</p><p>Employees can also work for 8 hours per day, 44 hours per week from Monday to Saturday, with 4 hours on Saturdays.</p><h2 class="wp-block-heading has-large-font-size">Overtime is paid according to the following rates</h2><p>All work in excess of the 44-hour weekly standard is subject to overtime pay and is subject to employment contracts/collective agreements, etc. In general, overtime is capped at 2 hours per day and is paid at 150.00% of the standard rate. The salary for holiday work can be up to 200.00% of the regular salary.</p><h2 class="wp-block-heading has-large-font-size">Minimum Wage Requirements</h2><p>National minimum wage R$1,412.00 per month</p><h2 class="wp-block-heading has-large-font-size">Payroll Cost&nbsp;for Employers</h2><p>The employer cost is generally estimated at 36.8% of the employee salary + Meal Voucher</p><ul><li>The Fundo de Garantia do Tempo e Serviço (FGTS) &#8211; 8%*</li><li>Social Security &#8211; 28.8%*</li><li>Meal allowance &#8211; 40 BRL per working day, estimated at BRL 880 monthly</li></ul><p>*These costs are calculated on the employee&#8217;s monthly salary including bonus and allowances</p><p>The amount for the meal voucher is calculated as BRL 40 per working day in the month, therefore the exact amount may vary.</p><p>Annual Costs</p><ul><li>13th Salary &#8211; 1 month salary, calculated as the gross monthly salaries received during the year including other salary payments, such as bonuses, allowances, and divided by 12.</li><li>Union Bonus (profit sharing) &#8211; BRL 300.50 a year (~USD 59)</li><li>Vacation Bonus &#8211; One-third of the employee&#8217;s monthly salary or one-third of the vacation days taken</li></ul><p>Conditional Costs</p><ul><li>Abono Pecuniário &#8211; Employees can choose to receive up to 10 days of their vacation bonus paid out instead of using them as paid time off.</li><li>Childcare allowance &#8211; Female employees with children aged 0-60 months are entitled to a monthly childcare allowance up to 290.98 BRL per child.</li></ul><h2 class="wp-block-heading has-large-font-size">Termination </h2><h3 class="wp-block-heading has-large-font-size">Grounds</h3><p>Terminations must respect complex rules and the rules of an employee’s employment country. The off-boarding is always handled by the Employer with the primary stakeholders. It may include ad-hoc fees as well as required or recommended steps on specific termination cases.<br>Terminations in Brazil can be complex. There is at-will termination in Brazil for employers except for certain employees (pregnant, suffer an accident at work, or are elected president of the internal commission for accident prevention).<br>Compliant terminations in Brazil include:</p><ul><li>By mutual agreement</li><li>Voluntarily by the employee</li><li>Unilaterally by the employer based on justified reasons</li><li>Gross misconduct</li><li>Substance abuse</li><li>Breach of trade secrets</li><li>Disciplinary dismissal</li><li>By the expiration of the contract</li></ul><h2 class="wp-block-heading has-large-font-size">Notice Period</h2><p>The minimum notice period is 1 month and will be increased according to the length of employment.</p><ul><li>Up to 1 year of employment &#8211; 30 days of notice</li><li>Above 1 year of employment &#8211; the notice period is increased by 3 days for each year worked until the maximum notice period of 90 days is reached</li></ul><h2 class="wp-block-heading has-large-font-size">Severance compensation</h2><p>In Brazil, if employees are terminated without cause, employees are entitled to:</p><ul><li>Balance salary</li><li>Unused holiday payouts</li><li>Proportional 13th salary (bonus payment)</li><li>40% of the FGTS paid during employment<br>If employees are terminated with cause, employees are entitled to:</li><li>Balance salary</li><li>Unused holiday payouts</li><li>Proportional 13th salary (bonus payment)</li></ul><h2 class="wp-block-heading has-large-font-size">Payment &amp; leave Compensation&nbsp;&amp; Holidays </h2><h3 class="wp-block-heading has-large-font-size">SalaryPayment</h3><p>Monthly</p><h2 class="wp-block-heading has-large-font-size">Payslip&nbsp;Payroll</h2><p>All payroll transactions must be in Brazilian currency &#8211; BRL.</p><h2 class="wp-block-heading has-large-font-size">Annual Leave</h2><p>Both full-time and part-time employees are entitled to 30 <strong>calendar</strong> days of paid time off (PTO) a year. PTO accrues monthly at 2.5 days per month. Employees are eligible for annual leave after 1 year of work. During the first year of employment, an employee is not allowed to take any leave.</p><p>Please note: Our platform works in working days and the brazil PTO is in calendar days. (20 working days &#8211; 30 calendar days).</p><h2 class="wp-block-heading has-large-font-size">Sick Leave</h2><p>There is no maximum period for employees to take sick leave. This leave is paid at different rates and by different payers depending upon length:</p><figure class="wp-block-table"><table><tbody><tr><td>Period</td><td>Pay (% of salary)</td><td>Payer</td></tr><tr><td>0 &#8211; 15 days</td><td>100%</td><td>Employer</td></tr><tr><td>15 days onwards</td><td>91% (maximum amount paid is BRL 6.433,57)</td><td>Social Security Agency (INSS)</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Maternity &amp; Parental Leave</h2><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Pregnant employees are entitled to 120 days of paid leave. The employee will receive 100% of their average salary during this period, and the employer will be responsible for this pay. However, the government returns this amount in tax deductions in the same month.</span><div data-page-id="DCZsdzp4HomtAOxBn8rc1s4BnCg" data-docx-has-block-data="false"><div class="ace-line ace-line old-record-id-Tu95dxCCZoMzT5x1d5Cc0MCRnHg">The employee can extend leave with a doctor’s note. During this period, the employee will receive 100% of the salary covered by the employer.</div></div></p><p>In Brazil, no law covers parental leave. However, employees may be entitled to maternity leave and paternity leave.<span data-lark-record-data="{&quot;isCut&quot;:false,&quot;rootId&quot;:&quot;DCZsdzp4HomtAOxBn8rc1s4BnCg&quot;,&quot;parentId&quot;:&quot;DCZsdzp4HomtAOxBn8rc1s4BnCg&quot;,&quot;blockIds&quot;:[46,47],&quot;recordIds&quot;:[&quot;OVGDdTv2Eo9qRdxzPFUc5zobnpI&quot;,&quot;Tu95dxCCZoMzT5x1d5Cc0MCRnHg&quot;],&quot;recordMap&quot;:{&quot;OVGDdTv2Eo9qRdxzPFUc5zobnpI&quot;:{&quot;id&quot;:&quot;OVGDdTv2Eo9qRdxzPFUc5zobnpI&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;DCZsdzp4HomtAOxBn8rc1s4BnCg&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7321550560460013571&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;Pregnant employees are entitled to 120 days of paid leave. The employee will receive 100% of their average salary during this period, and the employer will be responsible for this pay. However, the government returns this amount in tax deductions in the same month.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+7d&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7321550560460013571&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;Tu95dxCCZoMzT5x1d5Cc0MCRnHg&quot;:{&quot;id&quot;:&quot;Tu95dxCCZoMzT5x1d5Cc0MCRnHg&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;DCZsdzp4HomtAOxBn8rc1s4BnCg&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7321550560460013571&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;The employee can extend leave with a doctor’s note. During this period, the employee will receive 100% of the salary covered by the employer.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+3x&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7321550560460013571&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;DCZsdzp4HomtAOxBn8rc1s4BnCg&quot;:{&quot;id&quot;:&quot;DCZsdzp4HomtAOxBn8rc1s4BnCg&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;page&quot;,&quot;parent_id&quot;:&quot;&quot;,&quot;comments&quot;:[],&quot;revisions&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7085899834829848578&quot;,&quot;children&quot;:[&quot;doxcn2ugn0ixsatsMQuFdMFW9mb&quot;,&quot;doxcnGOrY5bcWH8Zqn4fW701G1c&quot;,&quot;doxcnmKMMx5V68ORGWbOl1EZXJe&quot;,&quot;doxcn4eRJ1uNCII3TUS14hD4Blc&quot;,&quot;doxcn2sGOrDqx0K686FUXbQRT3d&quot;,&quot;doxcndfQSFoXLZNdAwxdC4BB63f&quot;,&quot;doxcndOFAHGYWJsgPZMuPdncZRf&quot;,&quot;doxcnPply6zznsdVead2gKxUjvh&quot;,&quot;D0hLdIiwhoyt6FxckcecgVI9nPf&quot;,&quot;DmBVdiplcoZ9t5xWsXJc7TILn7I&quot;,&quot;NFOcdyFgJoxyUfxd6h8ch34wn7l&quot;,&quot;YdBtdigwAoYaDpx4Ks5c9plznTf&quot;,&quot;VtlkdPTqToaCkLxnExpcu21Fnpe&quot;,&quot;U93xdoIOLoef3UxtMzQcRDHgnfc&quot;,&quot;doxcnDS6YGXTi5lmqcxq1dT9hEb&quot;,&quot;R18Td36hZom36IxOmmlcoWOYnMd&quot;,&quot;Xq8UdqujNoJQB3xwT42cH9Rnng0&quot;,&quot;FTtSdSDj5otIrpxaWOBcs2sKnYd&quot;,&quot;GtuvdgAeHoOmYfxrV5CcHdfYnTd&quot;,&quot;Jokid5ng4oetedx12GZcKdJfnCh&quot;,&quot;J7TXdSSWmo3sfbxAfQqcRUHYnre&quot;,&quot;PTjedGNTkoaSGNxdx3KcJQqznde&quot;,&quot;OrTfdnMJsowyTdxn5bqc3RA9n6U&quot;,&quot;By8zdzpkWoUoptxQpE8cVE77nUb&quot;,&quot;EiRQdvzEJogE1lxgBp7c7tmenNc&quot;,&quot;Zsokd1ECroay3axVL3Wcizmbn47&quot;,&quot;UEqMd19Mgo0XKix56f0cYZHNnwf&quot;,&quot;NVRadTlrmo5HaGxZuo4cHr1kn3g&quot;,&quot;YEvEdPzlfotHKPx815vcTzkDn8d&quot;,&quot;FIPsdJyMDoyVkhxDNDbcaNe0n5d&quot;,&quot;ZafndYikooFL7exxVencJSQnnPe&quot;,&quot;EC7LdjQQsoak7FxMvxQcRFM7n8d&quot;,&quot;AvFAd4kLioEu0bxwSircON9NnrD&quot;,&quot;Icz8dtNDAoqRzlxYhkpcUjP4nnh&quot;,&quot;AavrdnfySoqIiSxgfyKccA69nYf&quot;,&quot;AeSddqlftoC4OUxMaSKcp1WBnyW&quot;,&quot;G9DTdSvxboK7DfxdoOMclpdAnff&quot;,&quot;WFTbd5N9qoaRQqx5nZUcycR5nvg&quot;,&quot;CpQNdsk7xo4SDMx74GGck6I1n8f&quot;,&quot;HVeyd6HPFo3NRKxFQBmcaVTpnWe&quot;,&quot;G9e3drpyaosWCDxtpF6cXl0mnWd&quot;,&quot;XamsdkeaSoez7bxBjbCcTXsJnFh&quot;,&quot;doxcn5mpLY3oCAGFVR3inZyFV4f&quot;,&quot;MbUPdEJvbo2LyBxsPBicuGKcnmL&quot;,&quot;OVGDdTv2Eo9qRdxzPFUc5zobnpI&quot;,&quot;Tu95dxCCZoMzT5x1d5Cc0MCRnHg&quot;,&quot;IlhsdYWhhorUQAxs8v5cwlGFnff&quot;,&quot;AkBAdygSvok7ZYxaBYCcsgw6nOe&quot;,&quot;BMNxdNTj7omUezxPxiZc2BOhnUg&quot;,&quot;V3HXd9mwGo5krzxm8F0cUODXnUb&quot;,&quot;EotcdNJMboDbyKxDYPBcGhcenPK&quot;,&quot;ZQD8dhuL4okIspxe3wvcBRqFnMg&quot;,&quot;PJJodPkrxosH9ixKvpjc4iPunJQ&quot;,&quot;UIGDds8VcolaYTx2MBDchSzhn5g&quot;,&quot;doxcnbLX0zRfwcGRjyM4alP7scd&quot;,&quot;UzDkdauGMo9a6mxvEjhc82vPn3d&quot;,&quot;Gcs9dBKmfov6CCxE6MTcM5wpnEe&quot;,&quot;Xxc8d5UeEo0ehixNCOrcSxpqnJf&quot;,&quot;KRkqdG3zQoDmyAxm63xcPn3Gn2b&quot;,&quot;VHE4dxtqMoprgGx6hfjcBGNpnAh&quot;,&quot;IbjudORoXoeNIQxaVmrc4z5EnKe&quot;,&quot;Q1xwdXQjgobipHx2ziwc0WJjnJh&quot;,&quot;Nuc5dDqswoxGcyxzn1tcGBdpnLb&quot;,&quot;KmUCdVad6owBcwxANjacLtKWnQh&quot;,&quot;LWDDdzf0Zo7ac4xcMcLcJ0L3nNg&quot;,&quot;MjqvdJvPvo2wcDx1KRKcVVZdnxh&quot;,&quot;XN9ndSmWqoIBkAxLKUNc1rTFnae&quot;,&quot;TIVJdNZAaopmwtxC6vCc0Izjnfc&quot;,&quot;QdDOdM7bMoO90AxocaUcKRbenSh&quot;,&quot;V2yyd113voarmLxyym7cDJWmnzf&quot;,&quot;OtW1dWecJoAm8qxpWPlce6TWn5b&quot;,&quot;IdmLd5ZeUoWr29xJxX8c1gE2nbg&quot;,&quot;STsYd35bMoV4UwxsTl0cLFHgnAd&quot;,&quot;DIEZdeLzYoT4ZUxNZKQc9WtenAJ&quot;,&quot;Pk6udHcmvogqnrx9oZocRmDzn4g&quot;,&quot;EKoqdm6ihoOp2NxYf4ncwJdnnVd&quot;,&quot;MKI6dBS8oocf4XxwMfdcLrMynpg&quot;,&quot;ENdTdCHtJoZHyFxWL4Tc4PI5nqf&quot;,&quot;doxcn91kl4WwLOx8HbImaH6NMoh&quot;,&quot;EhA2dIoUjoR43dxi2IqcegHOnHg&quot;,&quot;IONJdy6YXoEwpexLK2hcbhdCnhf&quot;,&quot;WbsIdR1fxoP1kzx8fIPc6DUrnmd&quot;,&quot;U3aqdAUEAoIfVqxojSBcyiVtnMf&quot;,&quot;PngHdofejoczjsxRIApc3EbVnjf&quot;,&quot;LJA9dXPPYosJr7xMbZJcIoUGnT7&quot;,&quot;CBJddyeggohT3xxvLkfctxmFn9f&quot;,&quot;CP3CdRZAGozLXSxJk5Cc7VYhnnT&quot;,&quot;ZyCzdY3ysoAH7kx15AZcTboynHe&quot;,&quot;TZr3dD0R6o4RpTxcIgEciXlinAg&quot;,&quot;SzGudg5bKoeOgIxiR7QcvWnznTg&quot;,&quot;OZfsdXUPooBCujxbfpkcwY9Fnte&quot;,&quot;QGutdKDkZogeOfxgR4EceLafn0g&quot;,&quot;MVPKdztCXoEh0Jxnu4JcEOX1nJg&quot;,&quot;YDf1dKWXCoaXy0xCZwAcS7HbnTc&quot;,&quot;D3dtdHB80o85okxOG9ac87ZmnZg&quot;,&quot;BhYwdzUmVorAsnxLbB6cjZyCn6e&quot;,&quot;MLpUdxrMCow4CDxyZqgcXGknnhd&quot;,&quot;Urf1dh0o8oGhgTx00kyc9uQAnJb&quot;,&quot;JY4ddLukuoYkfexcOYccjct2ncR&quot;,&quot;ZWCAdsDVUofsfDxobEkcySWQnwc&quot;,&quot;RVhmdxTAXoWjiCxKOEJc2nDonRd&quot;,&quot;doxcnU2f2Kiwke8KFwnhrjTC9Qb&quot;,&quot;CjOZd83UkoSXNox0H72cW9Z5nog&quot;,&quot;EId6dWXY7oUFJSxGXHQcHiGOnFc&quot;,&quot;doxcnsrljTZ2KchwMTnjKCOKnwd&quot;,&quot;JwSmdtncfoYOyJxdbQMcLgxBn4e&quot;,&quot;CzJBd5wOYoSfj4xeQqdc7bTbnyb&quot;,&quot;YWVEdF5oCoVl6PxuWaTcXNITnsd&quot;,&quot;HDwjdM5Z3oKsSzxRAkNcflMRnWh&quot;,&quot;G74aduCQ5ofz5uxjzIgczuOXnqe&quot;,&quot;KQCedNrgdoblpAxvUbpc0UFenNh&quot;,&quot;UsbIdJurAoNfc1xFOVGcpfjqnAf&quot;,&quot;X9Wfd2ROPoJPrQxDZjUcyE5dndg&quot;,&quot;EaeadlPPBonzIbxXoqMcvaapnZe&quot;,&quot;DmlydxcGgoN0PvxI1ZBcxGtwnke&quot;,&quot;QqoKd8v1LoObjjx706acC379nUd&quot;,&quot;JHvfd7NDGoXZsyxqdq3cxzF5ngd&quot;,&quot;IcFldXtCgobP1LxTnFHclSIbnce&quot;,&quot;POocdfrhmo421Qxvy84cAyMAnVh&quot;,&quot;ZfRjd4TvAosiyXxm65LccuK6nQg&quot;,&quot;H6DfdUpG8olOX4xetUOcvUS2neh&quot;,&quot;VzjHdMKvooeqlvxl8GecmOmfnib&quot;,&quot;PMQRdtqt0oPttLx5ohgcEFsanee&quot;,&quot;W7k0di8gXo7PhixwM8FctmPenPh&quot;,&quot;YEGudXrvCoPVP7xEJ27ceQ07n4H&quot;,&quot;DA9tdS344oBEWAx3QPgckZHWnhA&quot;,&quot;JPB4dvf35oyxc1xzwx9cqpdtnre&quot;,&quot;COSud8aUbodcNFxk3U7cTaTCnfb&quot;,&quot;BKwsdeaHdoaqYexx34MccVKSnMf&quot;,&quot;LOY5d1xcBoBOcIxDjztcbA9onPd&quot;,&quot;Kegrdt4UcoBXGixO9WtcAIjqndd&quot;,&quot;FtFpd481soILqZxPp55cGI90nrd&quot;,&quot;RapydSlBdoL4CGxRLiqciDbMnTf&quot;,&quot;BPMjdQyI2oRaCUxDpw4cZWW3n6b&quot;,&quot;HraydXGUxou5CTxESmocXzfbntb&quot;,&quot;CtCYdSjw6oziRaxA0F7cblcinEc&quot;,&quot;J9KMduNrGoAKFdxkRvYc1ufnndd&quot;,&quot;EBPXdOAm8oWba2xg4inciNUjnNY&quot;,&quot;DkOhdY4mHox0dRxnGzGcbsXYnTc&quot;,&quot;WPoGdv44Fo6WJqxsokLcXXFXnBo&quot;,&quot;AEYtd9GJso7RgYx6PcscPKSonsf&quot;,&quot;L7DzdsAZNoRDqvxlSQUcrp0vnhh&quot;,&quot;M6IPdaXpwo8nFYxgiiBc09m2nhg&quot;,&quot;AM8kd3wYdo9dqjxp0bOcIh5cnJg&quot;,&quot;PjQBd7iMxoZ733x8qUmcXieHnRc&quot;,&quot;LPXQd7o3loWUS4xJQNxcALjKnzg&quot;,&quot;FIPidtBVkogd3BxJl85csfibnte&quot;,&quot;DHFpd8qDKoRccJx6L2zc0VwNncg&quot;,&quot;LeoqdQcHJoY8QaxynvOcdvBqnzb&quot;,&quot;HAHkdOD98oPlJdx39vRc2bLEnNh&quot;,&quot;Nb7CdyedtokUytxiQQ8ck8o3nFh&quot;,&quot;AKrMdF90xo7PN5x5k6gcz98Qnlh&quot;,&quot;CMwRdV4F6oXc9oxLgqKcS6KdnXh&quot;,&quot;Rr72dcezGoV4sXxXHJfcm2sdn6f&quot;,&quot;YhngdtsfroyeXFxFEifcVqsjnFb&quot;,&quot;LSArdVjuBoFXPNxSDH5cy0UEn2g&quot;,&quot;CAbadbaodoDPeyxzHb7cjIYpnSh&quot;,&quot;KWiMdhtjXoB2N0xJ4GTccrZ6ngd&quot;,&quot;I8zmdTUsPo595NxCNWzcW34QnId&quot;,&quot;BM4advjwaoIMDmxFXKPcypXPnnd&quot;,&quot;Q42od70qoohNgyxMuUHcTBu9nTe&quot;,&quot;QhMsdNQIvocSbyxGGlxcLQa0ngd&quot;,&quot;HVKSdC9IIos9Q5xLPWLcm1F6nxb&quot;,&quot;RBFOd76WNo9Rc2xIfusc2HMOn4c&quot;,&quot;TEHpduqFro0ey1xDlKxcaXmYnvc&quot;,&quot;A6U6dQ2sxoI70ExCcNQcxiEnn7N&quot;,&quot;CL3sdLjCEoJ2p6xHYojcZnppnDb&quot;,&quot;EtxPdE3dHokrGxxQgMBcYRO8nfc&quot;,&quot;WOyjdGwllon2aRxioiPcU4Rqntb&quot;,&quot;YXvfdUyIUoMLL0xEPQHc5UGSnKP&quot;,&quot;OzGTdRunyouWxqxevXpc1mzgn9g&quot;,&quot;BFBSdq4sroV5Zlx1zADc4kpgnAd&quot;,&quot;L8QxdJcHGoZYd1xTQiLcnllPnhb&quot;,&quot;VuZpd6FfiopcKbxlMjYcYn93neg&quot;,&quot;E20BdgVz3oru8nxj0YmcRQMhnPc&quot;,&quot;RjukdScbooyAf4xsMn0cipx5nrb&quot;,&quot;EwTPdF75aoTOApxCQbgcUk5Anob&quot;,&quot;Xy2ndVEWto1yxhxUCHYco04hn1c&quot;,&quot;DwmgddZ3IoWYZ9xCCE3cN9Dbntd&quot;,&quot;N5X6dWNOso7tWkxh88Wc5tF3n5f&quot;,&quot;IH3AdxREXofPCOxKPJWcCL4hnvb&quot;,&quot;MR2mdduMJofeyaxFp94cdPzxn2g&quot;,&quot;OSsrdJoUZos9iuxF6itcY6P1n8W&quot;,&quot;UXvKdOVrnooB5txuivUc7Pexn6c&quot;,&quot;RifKdNk0golXF1xMmigcLFQ8n2e&quot;,&quot;JnFkdiS85omfoMxrX6SchwPEnWf&quot;,&quot;RVm7dpr5ToqrasxgKobcDXxnndd&quot;,&quot;XR4wdeyn0oef9mx1QFTc8ed0nRb&quot;,&quot;KvfBdTtpooQiMfxuwQgcodpon5g&quot;,&quot;EmK7dlttjouCH6xx6lccS97AnWG&quot;,&quot;W2AVdygu2o2vhMxZUzEczzeNnTf&quot;,&quot;GRnndOmA8oZ5Epxi5sSchZgDnFh&quot;,&quot;JjGodaYn8o8aUpxqezscQAlln6d&quot;,&quot;CYqbdAzhKowM6BxuLvccrdIznde&quot;,&quot;SUSOduofzoao22xNYJHcir5xnzf&quot;,&quot;CW0tdBNOSoHBe0x80SccRxNynVg&quot;,&quot;AAaWdLDfwosC0Qx9pYXcteHpnCc&quot;,&quot;MqPfdjDAHoPcSvxlph4c9eXCnkh&quot;,&quot;BHnBdPhXeoAsXTxd25LcoAUtnSg&quot;,&quot;DTLWdzW5koVELHx53hgcG5gEngh&quot;,&quot;XAKmdbo6ZoSN3Hxjkdwcdxx3nxh&quot;,&quot;KmJXdYLHzoghE5x0TqIcqm9QnKb&quot;,&quot;DlF0d8bo3oHfsPxprwgcQFmxnJd&quot;,&quot;PhjbdLw9zo9mPtxRoWYc3CAZnjg&quot;,&quot;U21BdN4ILoNP92xp0hGcp4WInJb&quot;,&quot;Kw3dde49OonvjixzdOocqfginUb&quot;,&quot;HsJDdLF3eouA2vxLloDcMMYLnoe&quot;,&quot;A98KdQT3ko3INmx5BEoc9rWwnag&quot;,&quot;KlUSdZr5wo9kLUxxr6vciJxRnFh&quot;,&quot;Uhk0dZSbqoBAvyxT3mDca7Hhnbc&quot;,&quot;UOptdorL5oZQUmxjPoycny2unmg&quot;,&quot;Oz5tdL4dpoMqHaxjMBKcna7snkp&quot;,&quot;GXc2dcNWMo4MnvxBK1kcxOJpnLg&quot;,&quot;H1j3dfJvxovCCmxC0SUcUD9Nn6e&quot;,&quot;Mj9wdjVoloMT8VxcHzGc0Jken2d&quot;,&quot;IDhvddvU4o2Y0RxAqppcst9Fnxf&quot;,&quot;XLIxdLdx8oaCEkxQxk3cgbGqnsg&quot;,&quot;Ek4odNPScon4ngxGzLFcLNetntg&quot;,&quot;Dq9UdHcsaoMXibxZh55cJMvtnJh&quot;,&quot;DSkPdwK4UoKen1xZ0aCcOBsHnvc&quot;,&quot;VAYRdoI2JoVYYgxyeFIcwJICnxd&quot;,&quot;ZUbOd4FRmoUneqxMYuwcWjIJnLg&quot;,&quot;FFyWdFEm8o7hbjxV57WcQ8YGnke&quot;,&quot;CRFld2AHFo3PpDxFhz9cL8npnrE&quot;,&quot;OrI6dq3E1oHZF1xOnCgcZGRKn0g&quot;,&quot;WpeWdBNyxo0TkRxd7SDcpkOMn6c&quot;,&quot;UfxfdNQ3yohBMix32XBcSkFTnrb&quot;,&quot;QLv2dJRrLoORuQxd4BoctYBWndb&quot;,&quot;JlvtdcEF7oPMx8xnqf5cPL4Rnh5&quot;,&quot;GQPsdniWroG1r4xdbuYccx8Rnte&quot;,&quot;JRMed36hToXZZrxvytccqn5GnOz&quot;,&quot;SssEdm6u5oygrgxWf91cqAv1ncf&quot;,&quot;I6VPdjebIoAHflxnhRGcxtt7npg&quot;],&quot;text&quot;:{&quot;apool&quot;:{&quot;nextNum&quot;:1,&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7085899834829848578&quot;]}},&quot;initialAttributedTexts&quot;:{&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+9&quot;},&quot;text&quot;:{&quot;0&quot;:&quot;巴西 Brazil&quot;}}},&quot;align&quot;:&quot;&quot;,&quot;doc_info&quot;:{&quot;editors&quot;:[&quot;7085899834829848578&quot;,&quot;7321550560460013571&quot;,&quot;7341208408357748740&quot;,&quot;7365790118063964164&quot;],&quot;options&quot;:[&quot;editors&quot;,&quot;create_time&quot;],&quot;deleted_editors&quot;:[],&quot;option_modified&quot;:null}}}},&quot;payloadMap&quot;:{&quot;OVGDdTv2Eo9qRdxzPFUc5zobnpI&quot;:{&quot;level&quot;:1},&quot;Tu95dxCCZoMzT5x1d5Cc0MCRnHg&quot;:{&quot;level&quot;:1}},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:46,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:265},&quot;recordId&quot;:&quot;OVGDdTv2Eo9qRdxzPFUc5zobnpI&quot;},{&quot;id&quot;:47,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:141},&quot;recordId&quot;:&quot;Tu95dxCCZoMzT5x1d5Cc0MCRnHg&quot;}],&quot;pasteFlag&quot;:&quot;59489304-d968-48d0-aab5-3f70ca1e432b&quot;}" data-lark-record-format="docx/record" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Tax and Social Security Information</h2><h3 class="wp-block-heading has-large-font-size">Personal Income Tax</h3><p>The individual income tax ranges from 0% to 27.5%. Income tax is calculated according to progressive rates. Multiple additional factors may impact overall rates such as the marital status and the level of income.</p><figure class="wp-block-table"><table><tbody><tr><td>Gross Annual Income (BRL)</td><td>Tax Rate (%)</td></tr><tr><td>Less than 1,903.98</td><td>0%</td></tr><tr><td>1,903.99 &#8211; 2,826.65</td><td>7.50%</td></tr><tr><td>2,826.66 &#8211; 3,751.05</td><td>15.00%</td></tr><tr><td>3,751.06 &#8211; 4,664.68</td><td>22.50%</td></tr><tr><td>Above 4,664.68</td><td>27.50%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Social security Public Holidays 2024</h2><figure class="wp-block-table"><table><tbody><tr><td>Holidays</td><td>Date</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Carnival</td><td>2.12-2.13</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Tiradentes Day</td><td>4.21</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Corpus Christi</td><td>5.30</td></tr><tr><td>Independence Day</td><td>9.7</td></tr><tr><td>Our Lady of Aparecida</td><td>10.12</td></tr><tr><td>All Souls&#8217; Day</td><td>11.2</td></tr><tr><td>Republic Day</td><td>11.15</td></tr><tr><td>Black Consciousness Day</td><td>11.20</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>