<template lang="pug">
.contries-page
  site-header(lang="en" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title Employer of Record in Greek
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="en" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="en" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'Employer of Record in Greek',
  ogDescription: 'Basic information Capital:Athens Time Zone:GMT+2 Langua […]',
  ogSiteName: 'SmartDeer',
  description: 'Basic information Capital:Athens Time Zone:GMT+2 Langua […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: 'Employer of Record in Greek'
})
const status = reactive({
  showForm: false
})
const langSimple = 'en';
const pageTitle = 'Employer of Record in Greek';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/R-C-1-6.jpeg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/R-C-2-2.jpeg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">Basic information</h2><p>Capital:Athens</p><p>Time Zone:GMT+2</p><p>Language: <span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Greek</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;Greek&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+5&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:7,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:5,&quot;end&quot;:10},&quot;recordId&quot;:&quot;doxcn6j6F5b3GJc7EGeu9n71jde&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><p>Currency Code:EUR</p><h2 class="wp-block-heading has-large-font-size">Human Resources at a Glance&nbsp;</h2><h3 class="wp-block-heading has-large-font-size">Contract Terms</h3><p>  Contracts must be in Greek and can be bilingual. They must be in writing and signed by both parties.<br>  A contract must include:<br>  &#8211; name<br>  &#8211; start date<br>  &#8211; length of the employment<br>  &#8211; job description<br>  &#8211; termination conditions</p><h2 class="wp-block-heading has-large-font-size">Guidelines Regarding Probation Period/Trial Period</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Probation periods are not mandatory. The minimum probation period is 30 days and the maximum probation period is 6 months. For fixed-term contracts, the probation period must be limited to one-quarter of the duration of the contract term and cannot be longer than 6 months.</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;Probation periods are not mandatory. The minimum probation period is 30 days and the maximum probation period is 6 months. For fixed-term contracts, the probation period must be limited to one-quarter of the duration of the contract term and cannot be longer than 6 months.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+7l&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:73,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:273},&quot;recordId&quot;:&quot;A39mdGTrhoy3usx6qJJcrFxdn0M&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Regulations and Rules Regarding Working Hours</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Standard working hours are 8 hours per day, 40 hours per week. The standard workweek is from Monday to Friday. However, if both parties agree the standard working hours can be 6,4 and the working days from Monday to Saturday. </span><span data-lark-record-data="{&quot;rootId&quot;:&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;Standard working hours are 8 hours per day, 40 hours per week. The standard workweek is from Monday to Friday. However, if both parties agree the standard working hours can be 6,4 and the working days from Monday to Saturday. &quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+6a&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:32,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:226},&quot;recordId&quot;:&quot;NXCldtdvhojtCwx5P4hcyKkDnlh&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Overtime is paid according to the following rates</h2><p>Standard working hours are 8 hours per day, 40 hours per week. The standard workweek is from Monday to Friday. However, if both parties agree the standard working hours can be 6,4 and the working days from Monday to Saturday.</p><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">In Greece, there are two types of overtime: &#8220;iperergasia&#8221; and &#8220;iperoria.&#8221; It is required by law to pay overtime to employees, and the rates vary depending on the type of overtime.</span></p><p>Overtime: Iperergasia</p><ul><li>For employees working 40 hours a week for 6 days a week, the first 8 hours of overtime (1 hour and 20 minutes per day) fall under iperergasia.</li><li>For employees working 40 hours a week for 5 days a week, the first 5 hours of overtime (1 hour per day) fall under iperergasia.</li><li>Iperergasia is paid an increase 20% of the employee&#8217;s regular hourly rate.</li><li>There is no need to inform the authorities about this type of overtime.<br>Overtime: Iperoria</li><li>Any overtime worked after the hours of iperergasia is considered iperoria.</li><li>The authorities must be informed in advance about iperoria, and there should be a valid reason for it. Iperoria cannot exceed 3 hours per day (excluding iperergasia) or 150 hours per year (excluding iperergasia).</li><li>Iperoria is paid with a 40% increase of the employee&#8217;s regular hourly rate until reaching the maximum of 150 hours per year.</li><li>To work more than 150 hours of overtime in a year, the employer needs special permission from the Ministry of Labor and must pay an increase od 60% of their regular hourly rate.<span data-lark-record-data="{&quot;isCut&quot;:false,&quot;rootId&quot;:&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;,&quot;parentId&quot;:&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;,&quot;blockIds&quot;:[33,34],&quot;recordIds&quot;:[&quot;WjNWde74Vo33ywxkV3RcXorHn6d&quot;,&quot;Us6tdhsuRosIcZxKONacQmFOnAg&quot;],&quot;recordMap&quot;:{&quot;WjNWde74Vo33ywxkV3RcXorHn6d&quot;:{&quot;id&quot;:&quot;WjNWde74Vo33ywxkV3RcXorHn6d&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7301936627151208452&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;In Greece, there are two types of overtime: \&quot;iperergasia\&quot; and \&quot;iperoria.\&quot; It is required by law to pay overtime to employees, and the rates vary depending on the type of overtime.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+4z&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false,&quot;text_indent&quot;:1}},&quot;Us6tdhsuRosIcZxKONacQmFOnAg&quot;:{&quot;id&quot;:&quot;Us6tdhsuRosIcZxKONacQmFOnAg&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7301936627151208452&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;There are limitations in place: employees cannot work more than an average of 48 hours per week over a period of up to 4 months, including both types of overtime. Additionally, it is not allowed to include overtime in the employment agreement.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+6r&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false,&quot;text_indent&quot;:1}},&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;:{&quot;id&quot;:&quot;MqhddAUv9oWJNUxHW5fcHaH3nXX&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;page&quot;,&quot;parent_id&quot;:&quot;&quot;,&quot;comments&quot;:[],&quot;revisions&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7129372845536198657&quot;,&quot;children&quot;:[&quot;doxcnluxEii2aNaUoQUG9VISi3Y&quot;,&quot;doxcnzLQ6krJPxC1jacjalEHuWg&quot;,&quot;doxcntjyest0k3pZnWhSlxg6UVg&quot;,&quot;doxcnMc6TgHyEXKoguxQdJ4XBud&quot;,&quot;doxcnktFm9WZsM4QqnBhDK0qRmf&quot;,&quot;doxcn6j6F5b3GJc7EGeu9n71jde&quot;,&quot;doxcnm0T8hOqc08y4PlkbxM51xf&quot;,&quot;doxcnt4GHgvXYSjOG5cFZ5ZvpLc&quot;,&quot;VSpJdQ8f8o1m5fx1kHScE5nvnAh&quot;,&quot;X7e8d9JLDoNcpSx00bqcURoxnpm&quot;,&quot;SwzTdfRa5oKiwexzWKkcd4Wgn9e&quot;,&quot;Z60ldWbVZoa6OYxiUqbct3W4n0d&quot;,&quot;AII4dIUZ1opEyux31AncNmvPnJf&quot;,&quot;HkYvd9rCqo1wLNxzu4AcHrMcnnh&quot;,&quot;doxcn9SxYbXVwsB2dBQqCbqqUb9&quot;,&quot;RjK8d2nQRoqvpexsf7dcEbfin2g&quot;,&quot;CtD8dZspjoBflrxMuVtclvAUned&quot;,&quot;MaFed9YK9osDPjxqkh7cAnO0nsc&quot;,&quot;MpQadtSC3oizIIxK6RvcMaRrnjc&quot;,&quot;NXXYdiEscohlgSxV4S4cefLhn6d&quot;,&quot;NUQRdlJBIoMUIBxiECmcpxfsngc&quot;,&quot;UotZd5TIboMFcqx4ML7cSPVmnvh&quot;,&quot;NXCldtdvhojtCwx5P4hcyKkDnlh&quot;,&quot;WjNWde74Vo33ywxkV3RcXorHn6d&quot;,&quot;Us6tdhsuRosIcZxKONacQmFOnAg&quot;,&quot;WhX1dW6UPoONCoxFL9CcaPqunqf&quot;,&quot;GVJydqvIyokviQxAqs7ch90nnvj&quot;,&quot;doxcn96gNf7aYWr5JZJZPFILuUg&quot;,&quot;Cx1kdDaJBooIw6xPVAPcEcCgned&quot;,&quot;Y6d0dXQzsooqJXxPN1Hc2FXRnte&quot;,&quot;Doq2dZ4EKoVSYcxX2QwcKnGZnAi&quot;,&quot;Qx1tdSrdhoupTJxpKXEckD84nJd&quot;,&quot;KqLcdz9h9owZCAxwG9HcCxatnkk&quot;,&quot;ZibndNil2oAL84x0jKkc4rsbnLe&quot;,&quot;PLoJdqPCyozFLuxu8VecQPsZneM&quot;,&quot;FKcKdj4ProDDewxDfT9cm1lhnPg&quot;,&quot;XR0wdokFnoQjrjxfhh2ckXbPn6b&quot;,&quot;FSRudgZ0vobL9LxPWqncFtwBnnb&quot;,&quot;ZnI5d3D8qowTpaxRpD8czhR1nLd&quot;,&quot;JDQQdFjwRol7EFxTYWcc3ePsnJd&quot;,&quot;DKawd0Cf2oVYCExIJUYcXnGun0g&quot;,&quot;doxcnzF0q9ADbh5qtWvXr9rdeZf&quot;,&quot;IkxAd76lLohIlQxNqwFcA8uJnLe&quot;,&quot;AZbPdwaQ4ohlsoxNBC8c54rFnXg&quot;,&quot;Pm6NdUPqnoFJYxxy1jQcMQhrngg&quot;,&quot;JtfMdS6UCocqEwx46j0cskMcnvc&quot;,&quot;Fos7dDOCyoaD9Tx8RNbcS3kenjq&quot;,&quot;A56CdcCDjoL3o7xrNEwc5LFEnNc&quot;,&quot;WQ7AdNeTnoRgOZxd5t2cXh8Gngg&quot;,&quot;Q3ISd60GFoWUvlxODnNc9J1tnmf&quot;,&quot;QdjydXhN5oEKKExcPTvcAhBjnrd&quot;,&quot;PiXqdTFSCohCkyxRj4scdBbHnPe&quot;,&quot;T8cNdK3bfoDDYExCHU1coEd0nSd&quot;,&quot;doxcnGfQMA3RAN1brTZNPM6HeB2&quot;,&quot;Guj7daVvMooQw3xWKDdcMzOhnjF&quot;,&quot;SPJaddRWto8NSNxr5ULc6Jc0nDb&quot;,&quot;PC58dnMvSoGaG7xcF5nchoHUnhc&quot;,&quot;doxcnyCDWu7EXbmyEvReBcRyEnb&quot;,&quot;C49gdjrhxoyTTFxd5VXc1fEWnkd&quot;,&quot;JroddBj3toK0sLxufYKcVDonnUb&quot;,&quot;doxcnRJZpXPxycn3DPI0My9WD0b&quot;,&quot;YGNQdmN3KoRLkqxAz9tcq6v3nkT&quot;,&quot;R4tEdeJrvo8KGrxdWapcT3IHnkf&quot;,&quot;A39mdGTrhoy3usx6qJJcrFxdn0M&quot;,&quot;JlGidMJwmoI7NaxMvGrcvYhInBe&quot;,&quot;BYWcd3umUoYRxKxUkipczBI6n8d&quot;,&quot;FW2IdZBs0oRwDxxuFdccf5O0nob&quot;,&quot;AAbjd8oMZo1FHbxFzTqcWK8unIe&quot;,&quot;H4Oxd9QEaoRTnSxvgJrcMY3FnId&quot;,&quot;DiODdbEn0oWeUnxcF56ckOX5nvh&quot;,&quot;ZN9bd75NAoZLPaxfL9mcbFaOnOc&quot;,&quot;DANedxR1IoR4B8xaezzcoK9jnNz&quot;,&quot;Ll2Id3QvOoMvxTxLkNcc1sQxnuh&quot;,&quot;FU3Fd4TbDoT5qexTJGYcUMnvnYg&quot;,&quot;OpTId9v9coc6wlxxU52c62vhnBf&quot;,&quot;E7AzdEXG5oiZ5yxc9eacpIRGnpd&quot;,&quot;WYnpdxUd3oTooSxsqXZcbd0TnTd&quot;,&quot;Po7sdPUIroAr5Cx6At4cXnPpnee&quot;,&quot;W36ydc7oiop64dxBhDCcgug2n5g&quot;,&quot;NXQodPhqvoT41ExWtZ3cHQy8nDh&quot;,&quot;RCsWdiA0YovEzCxSrDUcjFAhnPh&quot;,&quot;NXSCdVvykoOVqkx9Iz2cRoDKnHb&quot;,&quot;OX88d9zUdoPxlvxLyewcBLTHnlc&quot;,&quot;VG7HdfXPwo1DPSxC8jFcCUm1n4c&quot;,&quot;BoTidxRHToFdubxkfqhcQq96nyl&quot;,&quot;GjLudDQH0oGlyExQINxc3qBTnkf&quot;,&quot;NjoadtX9no77q2xU7mIciZmmnkg&quot;,&quot;ENsHdAgPfogg6cxlkADcq4Iinrc&quot;,&quot;RudXd8NVKoFN09x2nJqcHb7nnpf&quot;,&quot;ZkR0dLq3DogNvtxAtgnc9icXnAg&quot;,&quot;LPUxdTIVhoQoVNxnqzUcEHoAnvh&quot;,&quot;AdNjdO8vgoKAD2xNVSmcnmLsnAe&quot;,&quot;PTINdZu1Wom9nXxv3uUcMvAfnld&quot;,&quot;ZZq5dmWFzozSIwxt7REcIj8ZnAh&quot;,&quot;VXhWdZH8UodwXoxne1KcSNljnaf&quot;,&quot;Jl9LdTd7Mo0p7Wxcfoncyk8AnVc&quot;,&quot;YKuLdlX2HoFGGtxDgSMcdmmMnzR&quot;,&quot;PyLRdxtEWoROwKxmce9c8p9gnwH&quot;,&quot;OyOgdmd8ioBaolxAGvPctjARn6d&quot;,&quot;E2mXdpaj4o0tibxll5uc8wPonCf&quot;,&quot;DCS6duDNroIwNfx82rEcslWTn0e&quot;,&quot;DypidrSofotv5yxyWPXcEEYznqg&quot;,&quot;W5oMdzbkJoFyQzxPCrgc9tMTn8d&quot;,&quot;Qpd4dIzgmobcADxjQPLca0f9nWe&quot;,&quot;N6WZdshKhojYDMxv3gVcobjSnBb&quot;,&quot;EJxldudJGoJgAKxzO4qcE6donsd&quot;,&quot;JvhSdTmnZoJkfxxcO9Zc4dHxnwd&quot;,&quot;CnICdcQTGojBkdx4FeccPOA6n3c&quot;,&quot;VyaOd28JEobl95xxBOYc0fmJntd&quot;,&quot;VheUdbL2uoi3Ohx27RQcaxUAn9c&quot;,&quot;IkeZdhySVoRztyx1HN0c97V0nlh&quot;,&quot;NUA1dR1r0oicZGxt7INcWn14n3g&quot;,&quot;JmqRdqkPCongL9xYuxAcDc5MnJf&quot;,&quot;QVS6dwy6IoWIBux36nhczNTKnKf&quot;,&quot;OBWldRWDmoAsD7xopSfcvu2OnWg&quot;,&quot;LAmmdUqvEocECExf3wEcHsKCn4c&quot;,&quot;HZfcd9Q2Co4GJkxjrkUceNcVn2g&quot;,&quot;EpMUdhWLvoa32OxE9MMcNbWNnDc&quot;,&quot;OGHAdRJLOoHBlrxvWrAc3eAonqf&quot;,&quot;IYQTdYffQocY7txuyntcKKXanOe&quot;,&quot;IZrNduaF3oXc9XxzFCGca81gnFn&quot;,&quot;QQSydzbhJoI9HLxLnDMcczINn3g&quot;,&quot;DB20dJVLYoQg0nxvdTuctRYUnHe&quot;,&quot;B1vrdkkOxoVCJnxUXvvc5FOLnOd&quot;,&quot;RTGKdFciooYsLNxxPpSchxrQn7g&quot;,&quot;Kfq9d9pOxoU1vax3v02cCjEPnph&quot;,&quot;Q5GadZsHHoupaFxro8icVxiPnub&quot;,&quot;Xdk3dCYRyotRdBxWD4HcnIxrn0d&quot;,&quot;LMYvdwldyoLuzmxhOX1cclQWnOe&quot;,&quot;UCzHdaBxcoyZxkxULurcYEybn2d&quot;,&quot;OXmsdSMyIoTtyvxUWQDcqta4nqS&quot;,&quot;LhjJdYjDHouoA2xB7WccO5lan9d&quot;,&quot;FjCrdLRfOo98ZoxigJxcsHCmn9e&quot;,&quot;ZB9OdXMbqoSg5qxae5ycvrOknVe&quot;,&quot;YtPKdCel6oLMcLxUyS6cY60TnTg&quot;,&quot;IUGeda4tKoDmztxmyu3cQ3Eunwe&quot;,&quot;HT63d5i9JoVQT4xwtclcX5Cvnvd&quot;,&quot;DesEdgq6JogCarxv5q4cqT9ynlb&quot;,&quot;U9zTdkyXwo473yxu3XdcpYCHnob&quot;,&quot;A73Id6tAeonyMrxaKLocyAUrnMh&quot;,&quot;Hu1Xd5YCoojhvHxiRF9ct6Ugnvg&quot;,&quot;ENwYdEsKCoflmzxKMP2cOSJsnHf&quot;,&quot;U1SgdpXvQorQbtxHnhqcDdeHnud&quot;,&quot;G8sgds1ktoO51HxksoCc5bo3nuf&quot;,&quot;JGUrdVAuSo7EcMxDB8JcrBWTnYd&quot;,&quot;YSj8d3RopoUwRExMWiCcnU1yn5p&quot;,&quot;F526dMyk4oxF0wxSVXTcaWV1nee&quot;,&quot;Qclwd3deYozRH4xHOOec5A5gnJh&quot;,&quot;GIj1dgHYSoHDuCxRB8ScnbwWngc&quot;,&quot;Pq6zdqCd8oaYoQxbRo6cws3bn7c&quot;,&quot;DkpTdRm19ou1pJxtOMYcE36rnPd&quot;,&quot;XSjIdhSnOoCxCixlWYzcvsAdnOe&quot;,&quot;IAs4dAUZTonJY4xvkjKcpGzxncc&quot;,&quot;GjzqdIt1nomBGux7EoKcZUC8ntb&quot;,&quot;V3y0dDwTpoqOgvxC9YVc5aH0nec&quot;,&quot;Zg8hdGsEcovW4vxjYTIcgv2onUh&quot;,&quot;PsaOddDy4oeMhKxzG9LcYhI3nxc&quot;,&quot;MOs2dbZrYo0U5Bxot0Jcfx2ingh&quot;],&quot;text&quot;:{&quot;apool&quot;:{&quot;nextNum&quot;:2,&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7129372845536198657&quot;],&quot;1&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]}},&quot;initialAttributedTexts&quot;:{&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+2*1+7&quot;},&quot;text&quot;:{&quot;0&quot;:&quot;希腊 Greece&quot;}}},&quot;align&quot;:&quot;&quot;,&quot;doc_info&quot;:{&quot;editors&quot;:[&quot;7129372845536198657&quot;,&quot;7301936627151208452&quot;],&quot;options&quot;:[&quot;editors&quot;,&quot;create_time&quot;],&quot;deleted_editors&quot;:[],&quot;option_modified&quot;:null}}}},&quot;payloadMap&quot;:{&quot;WjNWde74Vo33ywxkV3RcXorHn6d&quot;:{&quot;level&quot;:1},&quot;Us6tdhsuRosIcZxKONacQmFOnAg&quot;:{&quot;level&quot;:1}},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:33,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:179},&quot;recordId&quot;:&quot;WjNWde74Vo33ywxkV3RcXorHn6d&quot;},{&quot;id&quot;:34,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:243},&quot;recordId&quot;:&quot;Us6tdhsuRosIcZxKONacQmFOnAg&quot;}],&quot;pasteFlag&quot;:&quot;822a68ba-c853-4745-af26-bcdc2695f3fa&quot;}" data-lark-record-format="docx/record" class="lark-record-clipboard"></span></li></ul><h2 class="wp-block-heading has-large-font-size">Minimum Wage Requirements</h2><p>The minimum wage is EUR 780 per month. However, the minimum wage will vary according to the seniority and civil status of the employee:</p><figure class="wp-block-table"><table><tbody><tr><td>Years of service</td><td>Minimum wage</td><td>Marriage/family allowance</td></tr><tr><td>Up to 3 years</td><td>EUR 780</td><td>EUR 858</td></tr><tr><td>3 &#8211; 6 years</td><td>EUR 858</td><td>EUR 944</td></tr><tr><td>6 &#8211; 9 years</td><td>EUR 936</td><td>EUR 1038</td></tr><tr><td>Over 9 years</td><td>EUR 1014</td><td>EUR 1142</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Payroll Cost&nbsp;for Employers</h2><p>The employer cost is generally estimated at 22,29% of the employee salary. The maximum gross monthly base salary for social security contributions for 2023 is EUR 7,126.94 per month.</p><ul><li>Pension Fund &#8211; 13.33%</li><li>Supplementary Pension -3.00%</li><li>Health Insurance &#8211; 4.30%</li><li>Supplementary Health Insurance 0.25%</li><li>Additional Contributions &#8211; Unemployment &#8211; 1.41%</li><li>Teleworking Expense &#8211; EUR 28 a month</li></ul><h2 class="wp-block-heading has-large-font-size">Termination </h2><h3 class="wp-block-heading has-large-font-size">Grounds</h3><p>Terminations must respect complex rules and the rules of an employee’s employment country. The off-boarding is always handled by the Employer with the primary stakeholders. It may include ad-hoc fees as well as required or recommended steps on specific termination cases.<br>Terminations in Greece can be complex. The conditions will depend on the term of the contract, length of service, and the employee&#8217;s salary. The authorities must be notified within 4 working days (i.e. Monday to Friday) for all terminations.<br>For indefinite term contracts, compliant terminations include:</p><ul><li>Voluntarily by the employee</li><li>By mutual agreement (only in cases of retirement)</li><li>Unilaterally by the employer based on:<ul><li>Probation period</li><li>Objective grounds</li><li>Disciplinary dismissal: absent from work without an excuse for more than seven days, or in case of improper behavior</li><li>Without cause, upon prior notification<br>For fixed-term contracts, compliant terminations include:</li></ul></li><li>Unilaterally by the employer based on:<ul><li>Objective grounds</li><li>Probation period</li><li>Objective grounds</li><li>Disciplinary dismissal: absent from work without an excuse for more than seven days, or in case of improper behavior</li></ul></li><li>By the expiration of the contract<br>Terminations and resignations must take place on Fridays. They cannot take place on other weekdays, weekends or public holidays.</li></ul><h2 class="wp-block-heading has-large-font-size">Notice Period</h2><p>Notice will depend on the contract term, what is agreed in the employment agreement, the salary of the employee, and the reason for the termination.<br>Fixed-term contracts</p><ul><li>By the expiration of the contract: no notice is required</li><li>Termination by the employer:<ul><li>No notice up to one year of employment</li><li>30 days over one year of employment<br>Indefinite-term contracts</li></ul></li><li>Unilaterally by the employer<ul><li>No notice is required</li></ul></li><li>Voluntarily by the employee<ul><li>15 days of notice</li></ul></li><li>By mutual agreement</li></ul><h2 class="wp-block-heading has-large-font-size">Severance compensation</h2><p>In Greece, employees who are terminated after one year of employment are entitled to severance pay. Severance pay will depend if the employee was terminated without cause or with cause, and primarily the warning period. <br>The minimum severance compensation for terminations without warning is 2 months and it can raise up to 12 months after 16 years of employment. <br>The minimum severance compensation for terminations with warning is 1 month and can be raised up to 6 months after 16 years of employment. <br>In Greece, a 1-month compensation calculation represents the monthly gross salary, divided by 12 and multiplied by 14 (mandatory bonuses).<br></p><h2 class="wp-block-heading has-large-font-size">Payment &amp; leave Compensation&nbsp;&amp; Holidays </h2><h3 class="wp-block-heading has-large-font-size">SalaryPayment</h3><p>Monthly</p><h2 class="wp-block-heading has-large-font-size">Payslip&nbsp;Payroll</h2><p>No explicit provision.</p><h2 class="wp-block-heading has-large-font-size">Annual Leave</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Employees in Greece are entitled to a certain amount of paid vacation days depending on how long they have worked for their employer and how many days per week they work:</span></p><figure class="wp-block-table"><table><tbody><tr><td>Calendar year</td><td>Days worked per week</td><td>Leave amount</td></tr><tr><td>1</td><td>5</td><td>20</td></tr><tr><td>1</td><td>6</td><td>24</td></tr><tr><td>2</td><td>5</td><td>20 until contract anniversary, then 21</td></tr><tr><td>2</td><td>6</td><td>24 until contract anniversary, then 25</td></tr><tr><td>3月9日</td><td>5</td><td>22</td></tr><tr><td>3月9日</td><td>6</td><td>26</td></tr><tr><td>10 (or after 12 years of service at any employer)</td><td>5</td><td>25</td></tr><tr><td>10 (or after 12 years of service at any employer)</td><td>6</td><td>30</td></tr><tr><td>After 25 years of service at any employer</td><td>5</td><td>26</td></tr><tr><td>After 25 years of service at any employer</td><td>6</td><td>31</td></tr></tbody></table></figure><p>  PTO accrues at a rate of:<br>  &#8211;  First calendar year<br>    &#8211; Works 5 days per week: 1.67 days/month<br>    &#8211; Works 6 days per week: 2 days/month<br>  &#8211; Second calendar year<br>    &#8211; Works 5 days per week: 1.75 days/month<br>    &#8211; Works 6 days per week: 2.08 days/month<br>  &#8211; Third and subsequent years<br>    &#8211; Full allowance available on January 1 of each year<br>  Employees are eligible for annual leave after 1 month of work.</p><h2 class="wp-block-heading has-large-font-size">Sick Leave</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Employees are entitled to paid sick leave for 13 days in their first year of employment and 25 days in all subsequent years. This leave is paid at different rates and by different payers depending upon length: </span></p><figure class="wp-block-table"><table><tbody><tr><td>Period</td><td>Length in the company</td><td>Pay</td><td>1st Payer</td><td>Pay</td><td>2nd Payer</td></tr><tr><td>0 &#8211; 3 days</td><td>&#8211;</td><td>50%</td><td>Employer</td><td></td><td>&#8211;</td></tr><tr><td>4 &#8211; 13 days</td><td>Less than 1 year</td><td>50% of assumed daily salary of the insurance class in which the employee is classified, based on the average of their wages in the past 30 working days</td><td>Social Security</td><td>Compensation up to 50% of the salary</td><td>Employer</td></tr><tr><td>4 &#8211; 25 days</td><td>More than 1 year</td><td>50% of assumed daily salary of the insurance class in which the employee is classified, based on the average of their wages in the past 30 working days</td><td>Social Security</td><td>Compensation up to 100% of the salary</td><td>Employer</td></tr></tbody></table></figure><p>Payment may vary based on a range of factors, and as such a true indication of benefit amount cannot be determined until social security has performed their calculations.</p><p></p><h2 class="wp-block-heading has-large-font-size">Maternity &amp; Parental Leave</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Pregnant employees who have worked for 10 consecutive days are entitled to 17 weeks of paid leave, taken in 2 parts. Employees who have a child via surrogacy are also entitled to the 9 weeks of maternity leave.</span></p><p>This is paid by e-EFKA at 50% of the employee’s regular salary corresponding to the insurance contribution class of the insured person. The child benefit rate is also paid for each child, at 10% of the rate mentioned previously. At a minimum, the employee will receive ⅔ of their regular salary for this time.</p><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Employees are entitled to 14 days of paid paternity leave. The employee will receive 100% of the salary during this period, and the employer will be responsible for this pay. </span></p><p>The employee can&#8217;t extend leave but can request paid time off upon agreement with the employer.</p><p>Employees are entitled to 30 months of reduced working hours as parental leave from the end of maternity leave or special maternity protection benefit. Employees must have worked for their employer for a minimum of 1 year to be eligible.<br>Both parents can decide how to distribute the time off, which can be taken until the child reaches the age of 8 and either parent can take parental leave. This leave is considered PTO, and is paid the same way as vacation is paid<br>Parental leave can’t be extended.</p><h2 class="wp-block-heading has-large-font-size">Tax and Social Security Information</h2><h3 class="wp-block-heading has-large-font-size">Personal Income Tax</h3><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">The individual income tax ranges from 9% to 44%. Income tax is calculated according to progressive rates. Multiple additional factors may impact overall rates such as the household status and the number of children.</span></p><figure class="wp-block-table"><table><tbody><tr><td>Gross Annual Income</td><td>Tax Rate (%)</td></tr><tr><td>Up to EUR 10,000</td><td>9%</td></tr><tr><td>Up to EUR 20,000</td><td>22%</td></tr><tr><td>Up to EUR 30,000</td><td>28%</td></tr><tr><td>Up to EUR 40,000</td><td>36%</td></tr><tr><td>Over EUR 40,000</td><td>44%</td></tr></tbody></table></figure><p><byte-sheet-html-origin data-id="1716517983587" data-version="4" data-is-embed="true" data-grid-line-hidden="false" data-copy-type="col" style="color: rgb(0, 0, 0); font-size: medium; white-space-collapse: collapse;"></byte-sheet-html-origin></p><h2 class="wp-block-heading has-large-font-size">Social security</h2><ul><li>Pension Insurance</li><li>Public Health Insurance</li><li>Social Security</li><li>Unemployment Insurance</li><li>Private Healthcare &#8211; Unisure (optional)</li><li>Private Healthcare &#8211; Allianz (optional)</li></ul><h2 class="wp-block-heading has-large-font-size">Public Holidays 2024</h2><figure class="wp-block-table"><table><tbody><tr><td>Holidays</td><td>Date</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Epiphany</td><td>1.6</td></tr><tr><td>Independence Day</td><td>3.25</td></tr><tr><td>Orthodox Good Friday</td><td>5.3</td></tr><tr><td>Orthodox Easter Sunday</td><td>5.5</td></tr><tr><td>Orthodox Easter Monday</td><td>5.6</td></tr><tr><td>Labour Day Holiday</td><td>5.7</td></tr><tr><td>Orthodox Whit Sunday</td><td>6.23</td></tr><tr><td>Orthodox Whit Monday</td><td>6.24</td></tr><tr><td>Assumption Day</td><td>8.15</td></tr><tr><td>Ochi Day</td><td>10.28</td></tr><tr><td>Christmas Day</td><td>12.25-12.26</td></tr></tbody></table></figure><p></p>';
function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>