<template lang="pug">
.contries-page
  site-header(lang="en" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title Employer Record in Colombia
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="en" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="en" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'Employer Record in Colombia',
  ogDescription: 'Basic information Capital:Bogotá Time zone: GMT-5 Langu […]',
  ogSiteName: 'SmartDeer',
  description: 'Basic information Capital:Bogotá Time zone: GMT-5 Langu […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: 'Employer Record in Colombia'
})
const status = reactive({
  showForm: false
})
const langSimple = 'en';
const pageTitle = 'Employer Record in Colombia';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/R-C-3.jpeg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/R-C-2-1.jpeg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">Basic information</h2><p>Capital:Bogotá</p><p>Time zone: GMT-5</p><p>Languages: Spanish</p><p>Currency code:COP</p><h2 class="wp-block-heading has-large-font-size">Human Resources at a Glance </h2><h3 class="wp-block-heading has-large-font-size">Contract Terms</h3><p>Contracts must be in Spanish and can be bilingual. They must be in writing and signed by both parties.<br>A contract must include:</p><ul><li>name</li><li>start date</li><li>length of the employment</li><li>job description</li><li>termination conditions</li><li>type of contract: integral or ordinary</li></ul><p></p><h2 class="wp-block-heading has-large-font-size">Guidelines Regarding Probation Period/Trial Period</h2><p>Probation periods are not mandatory. For indefinite term contracts, the maximum probation period is 60 days and for fixed-term contracts, the maximum is 1/5 of the initial term agreed, up to a maximum of 60 days.</p><h2 class="wp-block-heading has-large-font-size">Regulations and Rules Regarding Working Hours</h2><p>Standard working hours are eight hours per day, 40 hours per week. The standard workweek is from Monday to Friday. As per Law 2101 of 2021, the maximum weekly working hours for employees in Colombia must be progressively reduced from 48 to 42 hours in the next few years.<br>This change does not affect employees’ salaries and benefits.<br>The maximum weekly working hours will be reduced every year on July 15 from 2023 to 2026:</p><ul><li>2023: From 48 to 47 hours.</li><li>2024: From 47 to 46 hours.</li><li>2025: From 46 to 44 hours.</li><li>2026: From 44 to 42 hours.<br>The reduction in the maximum weekly working hours will stop on July 15, 2026.</li></ul><h2 class="wp-block-heading has-large-font-size">Overtime is paid according to the following rates</h2><p>Overtime payment is mandatory for employees earning below COP 13,000,000 per month. Hours outside of standard work hours are considered overtime. For additional hours, employees are paid an additional:<br>&#8211; 25% of the hourly rate on a standard workweek<br>&#8211; 75% of the hourly rate for night shifts, Sundays, and holidays<br>&#8211; 150% of the hourly rate for night shifts on Sundays and holidays<br>Employees who are entitled to overtime pay can work a maximum of two hours of overtime per day and a maximum of 48 total hours per week.</p><h2 class="wp-block-heading has-large-font-size">Minimum Wage Requirements</h2><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">The minimum wage is COP 1,300,000 per month, and the minimum Integral salary is COP 16,900,000.</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;The minimum wage is COP 1,300,000 per month, and the minimum Integral salary is COP 16,900,000.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+2n&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7321550560460013571&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:19,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:95},&quot;recordId&quot;:&quot;VGlcdwV2foaFqlxura3cTFtZnJu&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Payroll Cost for Employers</h2><p>The employer cost is generally estimated between 30.5% &#8211; 51.85% of the employee salary.<br>&#8211; Severance Fund contribution- 8.33%<br>  &#8211; Interest on Severance &#8211; 1%<br>&#8211; Vacation &#8211; 4.17%<br>&#8211; Service Bonus &#8211; 8.33%<br>&#8211; Pension fund &#8211; 12%<br>&#8211; Labor Risk &#8211; 0.52%<br>&#8211; Family fund management (CCF) &#8211; 4%<br>&#8211; Social Security, Public Health &#8211; 8.5%<br>&#8211; Family Fund &#8220;ICBF” &#8211; 3%<br>&#8211; Training funds &#8220;SENA” &#8211; 2%</p><h2 class="wp-block-heading has-large-font-size">Termination </h2><h3 class="wp-block-heading has-large-font-size">Grounds</h3><p>Generally, there are 5 ways to terminate an employee compliantly in Colombia.</p><ul><li>Termination With Cause</li><li>Termination for Redundancy or Other Business Reasons</li><li>Resignation</li><li>Mutual termination</li><li>Termination during the probation period</li></ul><h2 class="wp-block-heading has-large-font-size">Notice Period</h2><p>&#8211; Termination With Cause: 10 calendar days<br>&#8211; Termination for Redundancy or Other Business Reasons: 10 calendar days<br>&#8211; Resignation: None<br>&#8211; Mutual termination: None<br>&#8211; Termination during the probation period: As stated in Employment Agreement</p><h2 class="wp-block-heading has-large-font-size">Severance compensation</h2><p>Termination With Cause: No legal requirements<br>Termination for Redundancy or Other Business Reasons:<br>For employees on indefinite contracts, based on years of service:<br>≤ 1 year of service: 30 days&#8217; pay<br>For each additional year of service: 20 days&#8217; pay<br>For employees on fixed-term contracts:<br>All payments owed for the rest of the contract, including monthly salary, 13th and 14th salaries and all other statutory benefits which would be owed during the term of the contract.<br>*Note: Additional payment may be required to secure release from potential future claims.<br>Resignation: No legal requirements<br>Mutual termination: No legal requirements. However, it is recommended to offer a severance to reduce the risk of the mutual agreement being contested in court.<br>Termination during probation period: No legal requirements</p><h2 class="wp-block-heading has-large-font-size">Payment &amp; leave Compensation &amp; Holidays </h2><h3 class="wp-block-heading has-large-font-size">SalaryPayment</h3><p>Monthly</p><h2 class="wp-block-heading has-large-font-size">Payslip Payroll</h2><p>Establish a sound payroll management system, including payroll production, storage, inquiry and filing.</p><h2 class="wp-block-heading has-large-font-size">Annual Leave</h2><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Both full-time and part-time employees are entitled to 15 working days of paid time off (PTO) a year. PTO accrues monthly at 1.25 days per month. Employees are eligible for annual after completing 1 year of service.</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;Both full-time and part-time employees are entitled to 15 working days of paid time off (PTO) a year. PTO accrues monthly at 1.25 days per month. Employees are eligible for annual after completing 1 year of service.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+5z&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7321550560460013571&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:90,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:215},&quot;recordId&quot;:&quot;VCJLdi5sBoDcoJxWushcASjinze&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Sick Leave</h2><p>Employees are entitled to paid sick leave for up to 540 days. This leave is paid at different rates and by different payers depending upon the length:</p><figure class="wp-block-table"><table><tbody><tr><td>Period</td><td>Pay</td><td>Payer</td></tr><tr><td>0 &#8211; 2 days</td><td>66.6% of base salary</td><td>Employer</td></tr><tr><td>3 &#8211; 90 days</td><td>66.6% of base salary</td><td>Employer &#8211; refunded by Social Security</td></tr><tr><td>91 &#8211; 180 days</td><td>50% of the base salary</td><td>Employer &#8211; refunded by Social Security</td></tr><tr><td>181 &#8211; 540 days</td><td>50% of the base salary</td><td>Employer &#8211; refunded by Social Security</td></tr></tbody></table></figure><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">If the sick leave is originated by an accident at work, the employee will receive 100% of the base salary during leave and the insurer will be responsible for the payment.</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;If the sick leave is originated by an accident at work, the employee will receive 100% of the base salary during leave and the insurer will be responsible for the payment.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+4r&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7321550560460013571&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:61,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:171},&quot;recordId&quot;:&quot;YDvvdbiGxoF6TOxLvMhcfeFHnud&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Maternity &amp; Parental Leave</h2><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Employees are entitled to 126 days of paid leave. Seven days must be taken before the child&#8217;s birth. The employee will receive 100% of their average salary during this period, paid by the employer and reimbursed by Social Security.</span><div data-page-id="TOJ7dStK9o3y0Jxmb18cIveVnhf" data-docx-has-block-data="false"><div class="ace-line ace-line old-record-id-NPoYduO5XoV1wXx7Uidcn0p5ndb">The employee can’t extend leave.</div></div></p><p>In Colombia, no law covers parental leave. However, employees may be entitled to maternity leave and paternity leave.<span data-lark-record-data="{&quot;isCut&quot;:false,&quot;rootId&quot;:&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;,&quot;parentId&quot;:&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;,&quot;blockIds&quot;:[50,51],&quot;recordIds&quot;:[&quot;GB0DdbH6zoII9XxrSDXcS725nub&quot;,&quot;NPoYduO5XoV1wXx7Uidcn0p5ndb&quot;],&quot;recordMap&quot;:{&quot;GB0DdbH6zoII9XxrSDXcS725nub&quot;:{&quot;id&quot;:&quot;GB0DdbH6zoII9XxrSDXcS725nub&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7321550560460013571&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;Employees are entitled to 126 days of paid leave. Seven days must be taken before the child\'s birth. The employee will receive 100% of their average salary during this period, paid by the employer and reimbursed by Social Security.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+6f&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7321550560460013571&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;NPoYduO5XoV1wXx7Uidcn0p5ndb&quot;:{&quot;id&quot;:&quot;NPoYduO5XoV1wXx7Uidcn0p5ndb&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7321550560460013571&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;The employee can’t extend leave.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+w&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7321550560460013571&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;:{&quot;id&quot;:&quot;TOJ7dStK9o3y0Jxmb18cIveVnhf&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;page&quot;,&quot;parent_id&quot;:&quot;&quot;,&quot;comments&quot;:[],&quot;revisions&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7129372845536198657&quot;,&quot;children&quot;:[&quot;doxcnaw5gSDPNVOIFqKgzSQIBEd&quot;,&quot;doxcngLHGpZDgbVtDd7KL1FzK5g&quot;,&quot;doxcnIQsmYpCfQkWTyB1f5ODoEc&quot;,&quot;doxcnrM7RwH1zkIZlUKFIVH0WKh&quot;,&quot;doxcnf47t5i2JJK98TnTlGPx6Vk&quot;,&quot;doxcnRzcvbHWsgsZ1EFVFFxQuwd&quot;,&quot;doxcnInLdcwFmI7tUonewWPTtFl&quot;,&quot;doxcnod9xESNMU3z5inU27QDaRc&quot;,&quot;TNp1dJmpAomknRxDw4XcrTDOn7c&quot;,&quot;OHrQd5bSWoKSgjx97h2cE9Plnbg&quot;,&quot;VVeIdLvshoYFJ3xhToAcuKirnsd&quot;,&quot;QXWGdo5TTox4X5xvUQhcwpGMnfb&quot;,&quot;A2H5dz5lFoQJmwxSisjcd5PPnBe&quot;,&quot;ZCbbdQ5uIoUWdSxqJIpckuOHnag&quot;,&quot;O8bDdbyY0oGG4ixLkbncwSj9n7f&quot;,&quot;doxcnQKv6yLFpkfWwiQtlGpqMOh&quot;,&quot;FT1SdHKeCoaAAyxQ2TGcMJyZnVh&quot;,&quot;VGlcdwV2foaFqlxura3cTFtZnJu&quot;,&quot;O9DFdV85joDSvCx0bePcULfanaf&quot;,&quot;MCB0diYhdoNJfxxKZYgcYjbcn0b&quot;,&quot;Bfyodo5GOo3ZNcxjlifceRLkndd&quot;,&quot;SzHLdXiTJo7bijxt2pictbU9nZf&quot;,&quot;G1fidnQqmoI1Bbx27WBcBqzvnlB&quot;,&quot;QkRNdqVw9o8jrWxKrbRcQloAnjM&quot;,&quot;GJmDd7RO1omDJXxTyiEcIIENnub&quot;,&quot;KBz1dUJm9oaP2Oxye7PcGEwBnCA&quot;,&quot;EvICd5CYgouetqxLazFc42GRn0s&quot;,&quot;JzhhdiE73oEwYJxXaNucX8M0nHg&quot;,&quot;NlTWdnYuIoxS93xnHAoceM8AnnW&quot;,&quot;FflxdRjH6oJPN5xlxZxcuLbSnhb&quot;,&quot;QOP1dwoEKot3XVxOMK8cdecfndf&quot;,&quot;GO0ad8wdwosIkQxsNE3c3T92nEd&quot;,&quot;K2Afd0pT4o6e6jxv7ZWcnovInkc&quot;,&quot;JlI6dmBm4oUZ3PxVhJac5brUnHg&quot;,&quot;OvpUdwC14ofwK1xNY8kc4EfWnKd&quot;,&quot;QdzKdeQKXo43yYxYGMAcnU5Anhf&quot;,&quot;NkOld4khIos5Rvx1olMc2yYknch&quot;,&quot;CBFUdUdydoYbl9xVWk9ccqiCnVf&quot;,&quot;SzKJd9Zbpoft1Qxd0Nac5KTmnpf&quot;,&quot;WtfBdxZdjonmFgxqnpRcj7chnMb&quot;,&quot;W7HLdHV8QooWaoxSfwJcO0wjnhj&quot;,&quot;VfSrdxhBxoI6mfxvUfKc5NGzn0b&quot;,&quot;ING6d0Ni1oVn5Vx71Nrc6h7Znah&quot;,&quot;LzbwddN9BoULvcx01C9cBYSbnEg&quot;,&quot;MEumdu4cHo3QJaxGwOkcJ3WQnTe&quot;,&quot;MXwsdVWjVoYrZZxbzu3cKhSGnMd&quot;,&quot;doxcnQqtTTtTngisvjq52z9PPac&quot;,&quot;S2jmdJaoJogzv6xWmevcI6N1n4e&quot;,&quot;GB0DdbH6zoII9XxrSDXcS725nub&quot;,&quot;NPoYduO5XoV1wXx7Uidcn0p5ndb&quot;,&quot;ZauWd7M3Jol4HDxNtCbcU2eAngf&quot;,&quot;HQmud3SpSo7G9LxtUxscpgHznud&quot;,&quot;T3zhdT4Syof2HSxJiv8c2eEgn3b&quot;,&quot;OXDkddSJNoPizRxvHo9cHdrnnad&quot;,&quot;NhEmdNjTgoPUkgxMavvce3zknsc&quot;,&quot;IC2Yd9d6QoLn1hxO2U0cAHYrn8e&quot;,&quot;MVKDdUYYdokwSRxTymrcUaIonpW&quot;,&quot;UxwFd5GbfoRMxOxQRK6cdeq3nXp&quot;,&quot;KWdIdPucQotw2Gx5iaicYTmhneb&quot;,&quot;YDvvdbiGxoF6TOxLvMhcfeFHnud&quot;,&quot;doxcnQViK25Da2QJEyFiEDWGWJb&quot;,&quot;YcXWdQH9uomGDExyMAkcIAwOnfh&quot;,&quot;OQe4dGnCho7XZyxZO7BcssIAnlh&quot;,&quot;LDSxdn04BoeH6txNcRPc9tnRnPg&quot;,&quot;VRXHdAiM6oEOABxJfa8cOoTInpK&quot;,&quot;YmFCdFckMoGWiWxRiQ7c9TdUnAI&quot;,&quot;CkykdLQ1TogcHRxjoObcnDX3nVh&quot;,&quot;PQ9BdBAdqoBqi3xoZWVc0PbDn5c&quot;,&quot;O3GUdaAgDoSehox3cvWcWvb1nzb&quot;,&quot;AKH2dyz0zox66SxH6a1cXu1zn7f&quot;,&quot;VXcNdMoidoqj6ixQhawc16xxnIb&quot;,&quot;SQ5ndIeQoo4vc1x9Sl1c8zvWnig&quot;,&quot;Dt0HdWXEmosq3KxWjeVcQSc7nth&quot;,&quot;R5BCdLztWooil2x7oXXcwTKcnId&quot;,&quot;GmVpdH6IpoEDG2xnI3Hc1G6Nn5g&quot;,&quot;AbZGdNir1oibGxxItHYckCXvnWb&quot;,&quot;JCvedNGT8oChiyxiBUDcjXSSnof&quot;,&quot;HHO3dK5DboSsQYxDSuRckRi7neg&quot;,&quot;PsnfdMON3o89Kix5mU7c8T0unsd&quot;,&quot;AoTOdzz1YoTIq0xoWvHcIQfQnLA&quot;,&quot;Ji6QdD3Gvo5arExNZNTcFzJMnwf&quot;,&quot;K99cdxj4Soq2zSxVzQfcwPmBnVc&quot;,&quot;AwS0d0WmxoTIHxxGjXPcNApanXb&quot;,&quot;KMpRdyC6KoyHQ5xQAaXckHannnb&quot;,&quot;DgOZdDJG0otrdUx8N4BcjJvcnth&quot;,&quot;VejJdZCTvonjFtxfTweckbRcn5f&quot;,&quot;doxcnlGcbUN9iL0QIQjAjlO53ew&quot;,&quot;ZeD1dzpfQoFrt5x5X7lc7oCbnMC&quot;,&quot;VCJLdi5sBoDcoJxWushcASjinze&quot;,&quot;Eaznd2AC5oilcOxOJLMc9g2unGc&quot;,&quot;Q6TBdf9NxoojzJxH7ezce2xUnog&quot;,&quot;LqJWdd3AOogIScxnfkMcgFHgnbd&quot;,&quot;OFojdxbKWoYvVkx1ZSDcMjB8ndb&quot;,&quot;Z7w5dzeJBoF4mTx0WJBc7e8dnbg&quot;,&quot;EMs1do6bSoMtmzxqT7RclT6znSh&quot;,&quot;XZ9EdMMzoofALtxngtlcJ8iAn3a&quot;,&quot;V1VZdfKj9oYDrNxnHuucZLVDnNe&quot;,&quot;B1GRdAaknoBmX9x4rDxcYEY7nPh&quot;,&quot;RnkUd3es1oIwm9xDve6c7pMMn3T&quot;,&quot;WInkdIRB3o4REMxIwyvcyJVFnVd&quot;,&quot;ZNiydBU2HolFqUxnYgMc0R3xnsc&quot;,&quot;Ekb1dMqzWoAx9uxgyJ2cp7M1nuf&quot;,&quot;KDjAdaIU0oDNC3xt4Kfcrz1Fnbh&quot;,&quot;WF7od9vvXomDHZxPPx0cQLr8nQb&quot;,&quot;WEdSdDlAKoWgo3xpXGwcQE66nHb&quot;,&quot;ZcsjdSQ6zo0xn1xfoZ3cNkwVnBb&quot;,&quot;OKagd3hs4o3jF2xV1RJcvA7Cnrd&quot;,&quot;OWJWdo1PXoZGOGxO0BocsNGRnFc&quot;,&quot;AnHwdQ2Qvo65wUxWT29c0qdPnSd&quot;,&quot;doxcnDktjGcYc01rlGYKNXLSDBe&quot;,&quot;CKZHdrbvmoypPoxZcXYcQeLgnhc&quot;,&quot;D1STdMH9noBqbbx7roTcoc2Xnec&quot;,&quot;doxcnvn8aKrAbvbhnGpZnBNm7Tb&quot;,&quot;JEBWdJ1SRo6LVVxpfDic0rsEnsc&quot;,&quot;RUZhduYWIozEo8xmsWbcSNCRnCb&quot;,&quot;GVzed36kXo4BuTxdZ5XcA60unjg&quot;,&quot;Dc3ydmNyjoRcf4xP3qHch5PznMg&quot;,&quot;OjUldi8bEoRUflxlV2zcg7KinEe&quot;,&quot;AmV1dFJGHoVbOcxxur5ciZwhnwd&quot;,&quot;J7ZTdRQUCo5M44xL98dctVt2nRb&quot;,&quot;ACJBdjE4hoymJ9xSIwTcMgUpneh&quot;,&quot;ZWYSd1mYGomtvEx75yzcQldWnof&quot;,&quot;LEwidgJKpoFvfZxnw4Yc34yYnyf&quot;,&quot;WBhEdJntwoUlvlxuJkBcrTxwnHt&quot;,&quot;LnEDdVZs1otRbIxWCy7cNqRmnUb&quot;,&quot;E2g6d6Njzo4O2kxIL7fcPZhRnEh&quot;,&quot;TuS7dRzsGotm51xSlVBcM9Q4nve&quot;,&quot;IkJjdibk4o24iZxqe3scFKHanJf&quot;,&quot;QuRxdoMqZoGsi7xioSucM6FOn4z&quot;,&quot;AYSYdux5po1wFJxhhTUcLAKinQd&quot;,&quot;OorOd6SmhoP5EAxYpS1ch2TPnrb&quot;,&quot;R34edLW7powt2fxi7FGc6M4jnqe&quot;,&quot;AioCdV7uloDBI6xlCZBccmNInUe&quot;,&quot;AfsRd6L0uoJTYdxM24ucIKWqn1f&quot;,&quot;Z4Y5dI4POoTPQUxcwT6cjYOPnHe&quot;,&quot;G4uLde4tFofgZCxZNOGcuCtanVg&quot;,&quot;ZY2Wd9FD2okNhrx9BT7cxGK8nuf&quot;,&quot;OixFdhdQnodSlgxpuegcZ3gXnLg&quot;,&quot;MzXrdNd8govvY5xUJavcbNj4nkf&quot;,&quot;Jf4hdD4btoPmpMxfN10chiLjnBg&quot;,&quot;I6nSdTa7KoLwVixakcCcMAEFnYf&quot;,&quot;JM7xdKmXGoPvHYxNWZichd7ZnRb&quot;,&quot;HPCPdxkXuonZ7bxSYQZcYr4unpX&quot;,&quot;ZOgWdLs69oHlKxx1BjxcY1hdnXb&quot;,&quot;XQxHdtMNAoBWxzxSAjFcTi4hn4d&quot;,&quot;NInjdhoCUo6sCaxgALAco0FnnZd&quot;,&quot;VVA5dwk65oglxqxy2mwcOYg2nMc&quot;,&quot;HyLZdPvtXoSxVmx58tQcoXuUnWd&quot;,&quot;MG4gdnLeXo4iTAxWcnIcZ1kZnVb&quot;,&quot;BC9ad5A5JoJyOVxyaZyc0ztsnPc&quot;,&quot;RDZudDZi2oKYK8xKzo3ceaZZnIh&quot;,&quot;OKJ6dEm6loRiylxbAgIcgl3NnVd&quot;,&quot;XhaqdSEp1oGy60xQZ0dcM9K7nkd&quot;,&quot;Py26dlDiHoFfpZx8kEHcyRLOnPe&quot;,&quot;DIOdd7a0oojs6Oxu0iWc0Rw3nIb&quot;,&quot;HLX4dTIWdo7PKdxbgyfc84bRnzc&quot;,&quot;NjaBdPMigoXVQWx8YB3ckXMInOc&quot;,&quot;KbOHdnuYKoyP6pxDXzrc1XFYnqd&quot;,&quot;XmepdAF7sofB7GxG4nrcRIxLnxb&quot;,&quot;PHOrdNslHoeXn5xcSB3c9Mhjnff&quot;,&quot;XTdzdDNcDohHBsxgp9Pc7G2qnEe&quot;,&quot;PAV7dft9QouwZPxDETjcXHwLnUb&quot;,&quot;RH7rd5XWSotYfVxOtyLczjBknYx&quot;,&quot;SkqxdOrupoeXRtxGzaOcP1uunhc&quot;,&quot;FVoudDX5Oo1haxx1f4zcTChtngd&quot;,&quot;GKCodiZCoomgKgx7y89cl9z1n5d&quot;,&quot;JAwTdxkVNoYtFvx0fdacjRESnce&quot;,&quot;FKIbdhKEUoIzomxsEj5cICSaneE&quot;,&quot;IKffdeFiboFVk5xeNdQcVrs4nCe&quot;,&quot;JlvqdIkTNoR7QQxgopIcXMMcnWd&quot;,&quot;Cn68dnQRioKQJkxdsJjcvQbKnyc&quot;,&quot;VFCjdEKEIotgTDxGQVhcULk2nsh&quot;,&quot;QKlbdNeAooqrSZxjxVXcDZOjndg&quot;,&quot;Uk2bdvO5wop8N2xB6XPcfKMln2f&quot;,&quot;BOIRdFLqEokcXZxVu0DcHtg9nw5&quot;,&quot;Kax8dmYyComGTmxju2NcjLngnIc&quot;,&quot;PLIJdVBgdowUYQxZp41cy3JVnLd&quot;,&quot;FZOddXla1orUFTxGj99caBHOnbf&quot;,&quot;ELMTdQtXuoO7KqxxZiUcmQm2nfd&quot;,&quot;KAXVdv95roBmrexcdvqcSpBrnTb&quot;,&quot;SgP2dziVnoIRusxJtTpcsZMynhd&quot;,&quot;EosudXxhvozL47x6UblcxprFnwh&quot;,&quot;BOozdubWZodBEnxMHEycvBL0nNe&quot;,&quot;L5X8drTVloUQsEx3CBPcBjLsnOf&quot;,&quot;AxaCdexRJolcMHxuuoVchRqNnqf&quot;,&quot;UW5pdiSjvoIB5xxm4iKcej1inke&quot;,&quot;B1zhdXWaOooytVxv6eZckdT9nyc&quot;,&quot;BZmEdlcwvo5azyxaLTjcOXtMnDc&quot;,&quot;NPdZdLk5ronETIxhTYncJFkOnUq&quot;,&quot;DvNBdluRXoCSapxC9eUcfpNCnuc&quot;,&quot;GPiEdmXCGoqsJnxcjJLclWPrntM&quot;,&quot;YwHadvTS4ooa9gx6ormcVAlMnGd&quot;,&quot;HVqJdYJHToZG1WxARaTczw6Anxb&quot;,&quot;WYhydFyEdoTwWcxVBpycJ3lonCe&quot;,&quot;MfVHdYuJBoYtjyxB0t5ctHpQnpd&quot;,&quot;M6MVdXrPgoLiwWxcQW5cJyk2nLg&quot;,&quot;XxlzdMPQhovYe3xf0JdcriH2nwh&quot;,&quot;TnDUdgsTzoG3xXx8fJMcd2fonLb&quot;,&quot;VQIhdyrldo4nfXxgDFrcyb7bn0c&quot;,&quot;PMxVdvxqpoFH7Ix72mkcGoqBnEb&quot;,&quot;DrXHdC4RKoA73NxNysAcy1t9nH2&quot;,&quot;QmmXdSGAGohdNmxhvjPcNWkmnnd&quot;,&quot;XZ30df3QOoPwgkxNSs7cmHW3nBb&quot;,&quot;Vv8ldAxxKo0FHhx0lfocuEQbnCh&quot;,&quot;L0b8dfs3YoXQ1VxW6URcvZjHn4f&quot;,&quot;C8kGdHyx3o1tjWxP7iacNWa9nHd&quot;,&quot;IjDhddwKronThrxf36oc1QTZnag&quot;,&quot;LIbWdy0S6o03j6xESIWcDPcenVd&quot;,&quot;EUe6dvrXuosC0zxdgyFcm1lpnbg&quot;,&quot;Z2uzdvQFtoUw1HxHQ1pcQladnBb&quot;,&quot;G5IidznQioPbW3xaxMncnygInwc&quot;,&quot;UztMdEpSUoblvRxISZ6c2qnOnLh&quot;,&quot;ODcfdOW2yoiMtAxcdjZcxUeMn0W&quot;,&quot;D5U1dc0DJoXPVBxBYy7cRiprnde&quot;,&quot;S0BNdJi7JoWgnCxk2h8cZhGEnIc&quot;,&quot;EgzkdK39dokpjCxzFcGchbKVnNf&quot;,&quot;OF4NdzXGlowT1PxXHJBcZxg3nYb&quot;,&quot;Dag0dy0SYoGAzhxqb8DcJUT8n4f&quot;,&quot;JcgndJmCnodNxPxgy0hcOD5mnOe&quot;,&quot;J9Zmd5otXo3LM4xsVJ9cBBfBnMh&quot;,&quot;Ky1gdQIXbop2I7x42YBcWbL7nJh&quot;,&quot;GcshdS95Bodismx1ivmc61A6ncb&quot;,&quot;YBIid1Z9hol8GMxZ0xMcBmmYnFb&quot;,&quot;TV91dpt84obeAKxD0PMcaHbZn6F&quot;,&quot;UIyedR2a5o034dxBSl9cG0ZknJf&quot;,&quot;DKD1dP9ngoDlkAxpEQtce9pcntb&quot;,&quot;JsumdSmiYorwGJxeocMcG62rn5d&quot;,&quot;RNrsd1EPXoHblSxB2i0cnCYDnyg&quot;,&quot;KYMDdGhGro9BduxDjARcwcUEnYf&quot;,&quot;J5CTd52NboNgtKxUtv6cL4DRnZc&quot;,&quot;HMBpdfoQ9oThrUxosfxcPMb3nfh&quot;,&quot;X2cNd9F0AoJRZSxRJX6casoNnhb&quot;,&quot;Qjcgdjstqoeue9x9RYsc4JEQnq0&quot;,&quot;AH4rdzdAKo67QsxwUOvc6HPGnQd&quot;,&quot;F5KMdZ2K6oZUDwxi5BvcbumFn8e&quot;,&quot;CiFmduLcZotQ5UxIgIjcpsPjn4e&quot;,&quot;HfQcdxBWxo7pXVxdT4PctPVUnfd&quot;,&quot;FgbwdIwd3oaPN8xhthfcfUJIn0c&quot;,&quot;E502dfwHpo3MTsxDbVvcdUcYnNd&quot;,&quot;LEGtd1aCroForFxD7Jrc2JLdn6f&quot;,&quot;Jf8adFhqQosZqwxfeM0cw0E2njg&quot;,&quot;DOW6dRpQ5o7jx1xLE19cxzLun0c&quot;,&quot;J1WGdl18Zo975PxWSUzcsGAun3b&quot;,&quot;IrDAdCFjtowPVyxswIzcQs3xnpb&quot;,&quot;Z3yodG5Hlout8OxEhtZcwtaun8d&quot;,&quot;VLgydnCiPoL1e0xhYEDcPFbIn3g&quot;],&quot;text&quot;:{&quot;apool&quot;:{&quot;nextNum&quot;:1,&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7129372845536198657&quot;]}},&quot;initialAttributedTexts&quot;:{&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+d&quot;},&quot;text&quot;:{&quot;0&quot;:&quot;哥伦比亚 Colombia&quot;}}},&quot;align&quot;:&quot;&quot;,&quot;doc_info&quot;:{&quot;editors&quot;:[&quot;7129372845536198657&quot;,&quot;7321550560460013571&quot;,&quot;7341208408357748740&quot;],&quot;options&quot;:[&quot;editors&quot;,&quot;create_time&quot;],&quot;deleted_editors&quot;:[],&quot;option_modified&quot;:null}}}},&quot;payloadMap&quot;:{&quot;GB0DdbH6zoII9XxrSDXcS725nub&quot;:{&quot;level&quot;:1},&quot;NPoYduO5XoV1wXx7Uidcn0p5ndb&quot;:{&quot;level&quot;:1}},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:50,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:231},&quot;recordId&quot;:&quot;GB0DdbH6zoII9XxrSDXcS725nub&quot;},{&quot;id&quot;:51,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:32},&quot;recordId&quot;:&quot;NPoYduO5XoV1wXx7Uidcn0p5ndb&quot;}],&quot;pasteFlag&quot;:&quot;8e355244-6bdf-4d3e-9ae4-5319f72dbb45&quot;}" data-lark-record-format="docx/record" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Tax and Social Security Information</h2><h3 class="wp-block-heading has-large-font-size">Personal Income Tax</h3><p>The individual income tax ranges from 0% to 39%. Income tax is calculated according to progressive rates. Multiple additional factors may impact overall rates such as the household status and the number of children.</p><figure class="wp-block-table"><table><tbody><tr><td>Gross Annual Income</td><td>Tax Rate (%)</td></tr><tr><td>Up to COP 38,812,000</td><td>0%</td></tr><tr><td>Up to COP 60,532,000</td><td>19%</td></tr><tr><td>Up to COP 145,989,000</td><td>28%</td></tr><tr><td>Up to COP 308,713,000</td><td>33%</td></tr><tr><td>Up to COP 675,465,000</td><td>35%</td></tr><tr><td>Up to COP 1,103,817,000</td><td>37%</td></tr><tr><td>Over COP 1,103,817,000</td><td>39%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Social security</h2><p>&#8211; Pension Fund<br>&#8211; Social Security<br>&#8211; Unemployment Insurance<br>&#8211; Workers Compensation Insurance<br>&#8211; Private Healthcare &#8211; Unisure (optional)<br>&#8211; Private Healthcare &#8211; AXA Colpatria (optional)<br>&#8211; Private Healthcare &#8211; Allianz (optional)</p><h2 class="wp-block-heading has-large-font-size">Public Holidays 2024</h2><figure class="wp-block-table"><table><tbody><tr><td>Holidays</td><td>Date</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Epiphany</td><td>1.8</td></tr><tr><td>Saint Joseph&#8217;s Day</td><td>3.25</td></tr><tr><td>Maundy Thursday</td><td>3.28</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Ascension Day</td><td>5.13</td></tr><tr><td>Corpus Christi</td><td>6.3</td></tr><tr><td>Sacred Heart</td><td>6.10</td></tr><tr><td>Feast of Saint Peter and Saint Paul</td><td>7.1</td></tr><tr><td>Independence Day</td><td>7.20</td></tr><tr><td>Battle of Boyaca Day</td><td>8.7</td></tr><tr><td>Assumption of Mary</td><td>8.19</td></tr><tr><td>Columbus Day</td><td>10.14</td></tr><tr><td>All Saints&#8217; Day</td><td>11.4</td></tr><tr><td>Independence of Cartagena</td><td>11.11</td></tr><tr><td>Feast of the Immaculate Conception</td><td>12.8</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>