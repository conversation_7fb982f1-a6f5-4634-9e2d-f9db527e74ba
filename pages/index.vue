<template lang="pug">
.index-page
  img.loading(src="~/assets/images/loading-min.svg")
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import langTool from '~/assets/utils/lang'
import {useSessionStorage} from "@vueuse/core";
definePageMeta({ layout: 'blank' })

// Basic SEO for the redirect page
useHead({
  htmlAttrs: { lang: 'en' },
  title: 'SmartDeer - Global Employment & Recruitment Solutions',
  meta: [
    { name: 'description', content: 'SmartDeer provides global employment and recruitment solutions for companies expanding overseas. Redirecting to your preferred language...' },
    { name: 'robots', content: 'noindex, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },

    // Canonical to English version as default
    { name: 'canonical', content: 'https://smartdeer.work/en/' }
  ],
  link: [
    { rel: 'canonical', href: 'https://smartdeer.work/en/' },

    // Hreflang for multilingual SEO
    { rel: 'alternate', hreflang: 'en', href: 'https://smartdeer.work/en/' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://smartdeer.work/zh/' },
    { rel: 'alternate', hreflang: 'ja-JP', href: 'https://smartdeer.work/ja/' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://smartdeer.work/en/' }
  ]
})

const USER_REF_KEY = 'USER_REF'

onMounted(() => {
  const userRef = useSessionStorage(USER_REF_KEY, '')
  if (!userRef.value && document.referrer !== '') {
    userRef.value = document.referrer
  }
  langTool.indexGuide()
})

</script>

<style lang="scss" scoped>
.index-page {
  display: flex;
  min-height: 100vh;
  justify-content: center;
  align-items: center;
}

.loading {
  animation: rotate .5s steps(1) infinite;
  width: 40px;
  opacity: .3;
}


@keyframes rotate {
  0% {
    transform: rotate(0deg)
  }

  12.5% {
    transform: rotate(45deg)
  }

  25% {
    transform: rotate(90deg)
  }

  37.5% {
    transform: rotate(135deg)
  }

  50% {
    transform: rotate(180deg)
  }

  62.5% {
    transform: rotate(225deg)
  }

  75% {
    transform: rotate(270deg)
  }

  87.5% {
    transform: rotate(315deg)
  }
}
</style>