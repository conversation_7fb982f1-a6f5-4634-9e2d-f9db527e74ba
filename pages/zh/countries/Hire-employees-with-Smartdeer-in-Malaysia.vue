<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在马来西亚雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在马来西亚雇佣',
  ogDescription: '基础信息 首都：吉隆坡 Kuala Lumpur 时区：GMT+8 语言：马来语 货币代码：MYR 人力资源制 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：吉隆坡 Kuala Lumpur 时区：GMT+8 语言：马来语 货币代码：MYR 人力资源制 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在马来西亚雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在马来西亚雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/04/马来西亚Banner.png';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/04/马来西亚国旗.webp1_.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：吉隆坡 Kuala Lumpur</p><p>时区：GMT+8</p><p>语言：马来语</p><p>货币代码：MYR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>&nbsp;&nbsp;合同必须用英文书写。必须书面并由双方签署。</p><p>&nbsp;&nbsp;合同必须包括：</p><p>&nbsp;&nbsp;&#8211; 提供的职位</p><p>&nbsp;&nbsp;&#8211; 合同期限</p><p>&nbsp;&nbsp;&#8211; 工作地点</p><p>&nbsp;&nbsp;&#8211; 基本工资和支付日期</p><p>&nbsp;&nbsp;&#8211; 工作时间和工作日</p><p>&nbsp;&nbsp;&#8211; 其他福利（如果有）</p><p>&nbsp;&nbsp;&#8211; 加班费（如果有）</p><p>&nbsp;&nbsp;&#8211; 年假</p><p>&nbsp;&nbsp;&#8211; 终止条款</p><p>&nbsp;&nbsp;&#8211; 病假和住院假期</p><p>&nbsp;&nbsp;&#8211; 休息日</p><p>&nbsp;&nbsp;&#8211; 试用期</p><p>&nbsp;&nbsp;&#8211; 通知期</p><p>&nbsp;&nbsp;&#8211; 职责和责任</p><p>&nbsp;&nbsp;&#8211; 终止条款</p><p>&nbsp;&nbsp;&#8211; 法定扣除和所得税</p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>马来西亚没有法律要求在雇佣前让员工试用期。三到六个月的试用期是常见的。对于非行政和非管理类员工，建议采用三个月的试用期。对于行政和管理级别的员工，试用期应为六个月。</p><p>试用期员工享有与确认员工相同的权利。因此，试用期员工的服务不得无正当或无故解除。。如果《就业法令》规定员工有试用条款，则必须发放确认信或延期信。试用期员工在试用期结束时，如果其服务既没有被终止也没有被确认，则仍然是试用期员工</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工时为每天8小时，每周40小时。标准工作周从星期一到星期五。</p><p>加班费是强制性的，如果员工每月收入低于4,000马币，可以包含在工资中。如果每月收入超过4,000马币的员工，加班费不是必须的，由雇主和员工协商达成一致。员工每月最多可加班104小时。</p><p>加班费按照如下标准支付：</p><p>&#8211; 普通工作日：加班工资为时薪的1.5倍。</p><p>&#8211; 休息日：如果持续时间不超过正常工作时间的一半，员工将按照普通工资的半天工资进行补偿。如果持续时间超过一半但不超过正常工作时间，他们有权获得一天普通工资的工资。如果时间超过正常工作时间，则加班工资为时薪的2倍。</p><p>&#8211; 公共假日：在这些日期进行的工作将以时薪的2倍进行补偿，前八小时为时薪的3倍。超过八小时后，加班工资将提高至时薪的3倍。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月MYR1500。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本占员工工资的13% &#8211; 14%：</p><p>&#8211; 员工公积金（EPF）：</p><p>&nbsp;&nbsp;&#8211; 低于5,000马币的工资 &#8211; 雇主缴纳13%，雇员缴纳11%</p><p>&nbsp;&nbsp;&#8211; 高于5,000马币的工资 &#8211; 雇主缴纳12%，雇员缴纳11%</p><p>&#8211; 员工社会保障（SOCSO）- 根据员工工资从23.65马币到86.65马币不等</p><p>&#8211; 就业保险系统（EIS）：从2.70马币到9.90马币</p><p>&#8211; 人力资源发展基金（HRDF）- 1.00%</p><p>可选成本</p><p>13个月薪金：雇主可以决定向马来西亚员工支付13个月薪金。这是一个可选的奖金，通常相当于一个月的工资，通常在一月或十二月支付。</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p>&nbsp;&nbsp;合规的解雇包括：</p><p>&nbsp;&nbsp;&#8211; 员工自愿解雇</p><p>&nbsp;&nbsp;&#8211; 双方同意解雇</p><p>&nbsp;&nbsp;&#8211; 合同到期</p><p>&nbsp;&nbsp;&#8211; 雇主单方面基于以下情况解雇：</p><p>&nbsp;&nbsp;&nbsp;&nbsp;&#8211; 试用期</p><p>&nbsp;&nbsp;&nbsp;&nbsp;&#8211; 客观理由</p><p>&nbsp;&nbsp;&nbsp;&nbsp;&#8211; 纪律解雇</p><p>&#8211; 由于不适合工作而导致的表现</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>通知期取决于员工的服务年限：</p><p>少于两年：四周</p><p>两到五年：六周</p><p>五年或以上：八周</p><p>最好提前两个月以上的通知。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>工作2年以下的员工, 每一年可以领取10天工资。1年遣散费=1*10</p><p>工作2年至5年以下的员工, 每一年可以领取15天工资。2年遣散费=2*15</p><p>凡受雇为期5年以上的员工， 每一年可以领取20天工资。5年遣散费=5*20</p><p>任何不满一年者，以最接近的月份比率计算。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付 </h3><p>一月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>工资单通常以线上形式或PDF形式提供。</p><p>工资单所需信息包括但不限于以下内容：</p><p>公司名称和地址</p><p>工资单月份</p><p>员工个人信息（例如姓名、雇佣ID、职位/称号、部门、入职日期、法定会员编号）</p><p>月度收入、津贴和扣除项</p><p>年初至今的收入、扣除项和净收入</p><p>月度雇主的法定缴款</p><p>年初至今的雇主的法定缴款</p><p>支付细节（例如员工的银行账户信息）</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>年假取决于服务年限：</p><p>少于两年：每12个月8天</p><p>超过两年但少于五年：每12个月12天</p><p>超过五年：每12个月16天</p><p>公司可以按照法律规定提供更多的年假。</p><p>雇主只有在员工离职或辞职时才需要支付未使用的年假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>如果不需要住院治疗，病假的允许时间取决于员工的服务年限：</p><p>少于两年：14天</p><p>超过两年但少于五年：18天</p><p>超过五年：22天</p><p>员工需要向雇主提供医疗证明。</p><h2 class="wp-block-heading has-large-font-size">育儿假</h2><p>所有女性员工，有权获得98天连续的带薪产假，用于最多五个儿童的出生。</p><p>已婚男性员工有权获得每次出生7天的带薪陪产假，最多可达5次（不论配偶数量）。男性员工在开始陪产假之前必须在同一家公司工作至少12个月。他必须在妻子预产期前至少30天或婴儿出生后尽快告知雇主妻子的怀孕情况。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>居民个人所得税：个人将享有从0%到30%的税率，个人减免额取决于在该国的实际居住时间是否超过182天。</p><figure class="wp-block-table"><table><tbody><tr><td><strong>Taxable income(MYR) 应纳税款</strong><strong></strong></td><td><strong>Rate of tax 税率</strong><strong></strong></td></tr><tr><td>0 &#8211; 5000</td><td>0%</td></tr><tr><td>5,001 &#8211; 20,000</td><td>1%</td></tr><tr><td>20,001 &#8211; 35,000</td><td>3%</td></tr><tr><td>35,001 &#8211; 50,000</td><td>6%</td></tr><tr><td>50,001 &#8211; 70,000</td><td>11%</td></tr><tr><td>70,001 &#8211; 100,000</td><td>19%</td></tr><tr><td>100,001 &#8211; 400,000</td><td>25%</td></tr><tr><td>400,001 &#8211; 600,000</td><td>26%</td></tr><tr><td>600,001 &#8211; 2,000,000</td><td>28%</td></tr><tr><td>超过2,000,000</td><td>30%</td></tr></tbody></table></figure><p>非居民个人所得税：个人将以统一的30%税率征税。这适用于在一年内在马来西亚逗留时间少于182天的人。</p><h3 class="wp-block-heading">社会保险</h3><figure class="wp-block-table"><table><tbody><tr><td><strong>Social Security System社会保障体系</strong><strong></strong></td><td><strong>上限</strong><strong></strong></td><td><strong>雇主部分</strong><strong></strong></td><td><strong>雇员部分</strong><strong></strong></td></tr><tr><td>Employment Provident Fund（EPF）-Local only本地人</td><td>无</td><td>工资＜RM5,000:13% ； 工资＞RM5,001:12%</td><td>11%</td></tr><tr><td>Social Security Organization（SOSCO）</td><td>5,000</td><td>1.75%</td><td>0.50%</td></tr><tr><td>Employment Insurance System（EIS）</td><td>5,000</td><td>0.20%</td><td>0.20%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">2024年法定假期<img loading="lazy" decoding="async" width="12" height="10" src="https://weboffice.feishu-3rd-party-services.com/api/v3/office/copy/VjZGYU1NS1d0SU5GYUt0YzhUMFd6NnhxMk1SUEN4aExXQ3JlaTNFdG9JNVVHNEZ6Wk1oajlLZVd0dnJ3L3EyTWxzcFdxQ3U5bW40TTZCSktEWktUdGtkZTdVTS9WcXhSZjlsZDh2bGsvbExpQVowV2ZlYXRiall4ejA3M0RqdUM4Y2QxV1ZmejhaTzhXQVErejNXNWJsRDNabzIzYzh2QS9SSDE4N2VVZlJKaXpiOXBQdkJQTCsyT3JKS0UxcmlOTkJiY0owNEFIVFhkUDBIYWhvNkxNQzJjRkh0UUxVWDhBL3dnTVlyMHdGUHFrL1ZhOWN6Z2lIVVRhSnF5VmtmaXlJK1YvZk0xQVhybk5Lc04rczRLbHZ5Ukg3S2ZMLzQ9/attach/object/5a84e9de05fe0ca56c5e95ad2c153c197bfd022c?"></h2><figure class="wp-block-table"><table><tbody><tr><td><strong> 假期</strong></td><td><strong>日期</strong></td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>Thaipusam</td><td>1.25</td></tr><tr><td>Federal Territory Day</td><td>2.1</td></tr><tr><td>Chinese New Year</td><td>2.10-2.11</td></tr><tr><td>Hari Raya Puasa</td><td>4.10-4.11</td></tr><tr><td>Labor Day</td><td>5.1</td></tr><tr><td>Vesak Day</td><td>5.22</td></tr><tr><td>Agong’s &nbsp;Birthday</td><td>6.3</td></tr><tr><td>Hari Raya Haji</td><td>6.17</td></tr><tr><td>Awal Muharram</td><td>7.7</td></tr><tr><td>Merdeka Day</td><td>8.31</td></tr><tr><td>Malaysia Day</td><td>9.16</td></tr><tr><td>Prophet Muhammad’s Birth</td><td>9.16</td></tr><tr><td>Deepavali</td><td>10.31</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>