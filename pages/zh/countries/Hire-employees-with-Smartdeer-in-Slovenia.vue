<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在斯洛文尼亚雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在斯洛文尼亚雇佣',
  ogDescription: '基础信息 首都：卢布尔雅那 Ljubljana 时区：GMT+1 语言：斯洛文尼亚语 货币代码：EUR 人力资 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：卢布尔雅那 Ljubljana 时区：GMT+1 语言：斯洛文尼亚语 货币代码：EUR 人力资 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在斯洛文尼亚雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在斯洛文尼亚雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/194596302_1582760277848.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/R-C-1-3.jpeg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：卢布尔雅那 Ljubljana</p><p>时区：GMT+1</p><p>语言：斯洛文尼亚语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">合同必须用斯洛文尼亚语书写，也可以是双语的。合同应包括工资、工作时间、试用期、终止日期或未定义、保密等信息。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;合同必须用斯洛文尼亚语书写，也可以是双语的。合同应包括工资、工作时间、试用期、终止日期或未定义、保密等信息。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+1i&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:130,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:54},&quot;recordId&quot;:&quot;Tl2wdZuzEozSiQxDmIFcJ6gxngg&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">试用期不是强制性的。最短试用期为30天，最长试用期为180天（6个月）。</span><span data-lark-record-data="{&quot;isCut&quot;:false,&quot;rootId&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;parentId&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;blockIds&quot;:[132],&quot;recordIds&quot;:[&quot;GxIWdni6worl0LxOltbc110Sn5e&quot;],&quot;recordMap&quot;:{&quot;GxIWdni6worl0LxOltbc110Sn5e&quot;:{&quot;id&quot;:&quot;GxIWdni6worl0LxOltbc110Sn5e&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7301936627151208452&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;试用期不是强制性的。最短试用期为30天，最长试用期为180天（6个月）。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+10&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false,&quot;text_indent&quot;:1}},&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;:{&quot;id&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;page&quot;,&quot;parent_id&quot;:&quot;&quot;,&quot;comments&quot;:[],&quot;revisions&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7129372845536198657&quot;,&quot;children&quot;:[&quot;doxcnrlIQafv5oqgyitiH0gpOAc&quot;,&quot;doxcnIVT2bWo2JSf7VBNNcgW0mf&quot;,&quot;doxcnQQm9wJcgNJ3a37JGfuZkZc&quot;,&quot;doxcn1EhzSzQuYIWzOlOWp3l7ue&quot;,&quot;doxcnFAPfrx8NxVP9J8g6MQ3C6c&quot;,&quot;doxcnNc0lnA8DasJGNB6b8O0Oif&quot;,&quot;doxcnhtSBusiXcpww5lMjPIq3Nh&quot;,&quot;doxcnzjkA4JFJcZUV4swT8uZu0c&quot;,&quot;Wm9edigrDoDEroxZQzocPlLNnPe&quot;,&quot;KFIodNwizo1F8QxtvtncubjQnmM&quot;,&quot;RiWrdthuGoX0mWxwj3CcKqJEnUh&quot;,&quot;LulKdZ7ESoujjKx1UpEc61YEndb&quot;,&quot;NQWydRZARo0r6SxFK7EcnrJBnKh&quot;,&quot;FJi3di9PxoaJZxxd9EvcOUHKn3b&quot;,&quot;Jdi3dMHwqoCdeKxRMNUcRFCXnrd&quot;,&quot;V3bvdZdXVoX7bmxnEIWcA0Ccnfh&quot;,&quot;doxcnDBNcdzgautcIjqkKa10Pff&quot;,&quot;LRXMd2jzCoUGQQxOkPWcIrwtnFd&quot;,&quot;C4lCdUtNaorGkyxASX1cGPdanpc&quot;,&quot;FXjidN8XloeklBxgOytcquRNntc&quot;,&quot;UzemdIhqIoS4VkxnS6xcTVR7nDc&quot;,&quot;UqBsdu8MToEqsbxxklmceO7vn6b&quot;,&quot;EaGLdyxtYo5orexJR70cWz8innf&quot;,&quot;RQ1rdNrbNorCPKxH40scDO3ynnW&quot;,&quot;UNaAdOZv7oRUQMxz9J1cLICgnjd&quot;,&quot;doxcn2U5jomoDTDJhlqlXTOafId&quot;,&quot;BiEDdZlMmoHfUEx9tv4c7iyKnVb&quot;,&quot;TVJydqjIworRkJxvbHkcyxsJnEf&quot;,&quot;PgdRdIJOko9X9rx59YdcoSNLngc&quot;,&quot;EaK8daVNXogsCIxfOq0csFoTnUd&quot;,&quot;OJSldSfIqoncApxYL7eczaJdnlf&quot;,&quot;S6PydcItYoiqB8xosEtchmIfnud&quot;,&quot;Sqnbdg2uaoUlW8xJiL0cmznWnad&quot;,&quot;UulUdBjRjo8SYix6FG7cyaeEn90&quot;,&quot;AGgidbxMtooJdMxZnnxccLTxnEf&quot;,&quot;Yha1dkf6ToLBB3xtAvccRU6InqM&quot;,&quot;doxcnKhEIL89pSCi3A0xvEu3exe&quot;,&quot;Yilpdk1ZRoHoLXx94ZncTPsPnPh&quot;,&quot;CS4jdKAvRofZenxT2o4cGhyJnFg&quot;,&quot;Qy1Aduf5woFWXTxOoQtct47bncb&quot;,&quot;DzVqdx2doo8hbpxaZf0cWwSpn7d&quot;,&quot;LhL8dFOYUo1OwDxka42cdPDanQm&quot;,&quot;PPIBdW7wgo5FZ4x94Poc174Unqc&quot;,&quot;QiRJdrXwbozOQuxhRiHcnLAbnrc&quot;,&quot;YXABdFBjfoRo71xqCK8csf3inNd&quot;,&quot;doxcn9WdQliNBV33k6YDLCeX8Uc&quot;,&quot;LaZddP3NaoL9cBxU7m0cGYxEn0f&quot;,&quot;GBphdIEMooe7YOxIHtGc8ubinXd&quot;,&quot;ANEAdXZn2oA9KmxxwDEcIsRznLd&quot;,&quot;Gwvhdl7FzoY78TxL5kpcbdw3n1e&quot;,&quot;CSi2d5rpYo6ymgx53RIc9ibVnAg&quot;,&quot;doxcnFsm2zwN8UKCuiI5fSQ3eOf&quot;,&quot;QAzzdVv1gopWtUxpKMuc5hI9nCf&quot;,&quot;CrCydxq1toyzxUx9FxWcaZ5wnsd&quot;,&quot;doxcnSGXVV4uBGg5uUwHlgNFjDe&quot;,&quot;FzdzdZIwcoEOLfxFkOmccjyYnzd&quot;,&quot;Zj7JdDxW7o7aGkxPNPFcdwwAngb&quot;,&quot;MfBldTDdkogh7exIG31cr57Knfe&quot;,&quot;X1MUd7x8ToBjTOxdICEcTrWenUf&quot;,&quot;P9hsd9O9eoUFFzxkURbcnj2Mnmh&quot;,&quot;CkE6d2vZVoyYcQxKQ3ccxSiinTY&quot;,&quot;JQujdnwySo01JIxpzPZcpnhhnGe&quot;,&quot;X0PVdcsQgoqojixddrlcjS86nZe&quot;,&quot;UPZQdDTtVocMfjxg457cxvRWnob&quot;,&quot;RqcOd32Wvo8VdzxiSLnc0OYknkf&quot;,&quot;YoUcdoHZAoLSkfxfl7dcT7fqnTh&quot;,&quot;LxMzdvnqUo7e28xLVX8cgU71nLh&quot;,&quot;Yga1dTmaEoEhV0xf09Uc8UZznjd&quot;,&quot;VEZtdQpCEoKqtLxCnIjcOYG0nFd&quot;,&quot;QJfJd1gCAoJlEOxjxhNcEKA1n0b&quot;,&quot;XdrfdzEiPoTulkxIuSIczLLBn2g&quot;,&quot;GwCadDgI6oU5QEx5glNc6a0yndg&quot;,&quot;Vf94dWm9LoFvHqxDeszcDV28ncc&quot;,&quot;WysmdabDtoXNtLxqPXxcgGCcntg&quot;,&quot;Vtx7dcjREoK1SWxn9SxcUyf9nad&quot;,&quot;PqjxdKiFtoL1KXxzdFacWyzyn9g&quot;,&quot;OW7VdX1QxoK71NxQxaQcWPe5nVe&quot;,&quot;JRnGdNFcuogqKIxWJJwcD6sanXf&quot;,&quot;AzybdY35HoN7p0xlpDqcW8L6nob&quot;,&quot;YCXVduytOooRaXxQvGGcdOu8nCd&quot;,&quot;Cm74dq6wco7TVIxjibCctSLFnTf&quot;,&quot;I0NndBKedoIFr0xoanuchnf7n3f&quot;,&quot;ZQJTdBSQ5o0qnzxtox7ckCm7n2b&quot;,&quot;XOfFdNTrsoCAHcxYsiZcwVXnn7e&quot;,&quot;IJnydGmnvov3ynxwidhc0MhBn5e&quot;,&quot;Qk7Pd4fRDowsruxsYV1cbmjFnze&quot;,&quot;RsINdVVLDoOXJUx4r0Gc2Qwen9b&quot;,&quot;JuDod0noFowgoEx4K7bct2xUn8e&quot;,&quot;ONo0dDwO9oYvjfxv9pbchnNjnMg&quot;,&quot;HQtPdFIKVo97tExRGg2cJdjSn2b&quot;,&quot;IdU5dUsIMoVMmpxrT3lcMwcEnxK&quot;,&quot;Y9iZdS7aqoOolQxWrdDcCmn8nnh&quot;,&quot;RBSGdiVvAo4zKDxjVefcpq9Mn5g&quot;,&quot;LmnbdSOy6oIRRyxYyqAchaiwnnf&quot;,&quot;JaccdQqvAoavInxYDpicvaqznOf&quot;,&quot;FbOxd3YQHo3VsWx5eNfcy7Genkc&quot;,&quot;EDTfdw1uxoisOhxIvKQcRbR0n8c&quot;,&quot;OGO7dRB3VouXmrxQiNZcK5jPnPb&quot;,&quot;KwwxdOlxPo7LayxzXSkcHaSNnph&quot;,&quot;X8qTdoR2yoQUnRxn1sScktvWntg&quot;,&quot;ElXJdO3tDoAJvkx7MgFcUT7Xn3d&quot;,&quot;GuwUdBYJToPm0CxoUY5c3bt1nbb&quot;,&quot;PaLwdeBRLoqNzyxl9TKcYEhFnrh&quot;,&quot;GOKadg4A3o57q9xRzh1cQNYpntc&quot;,&quot;OFJNdeDpToDnWFxdVB2cfENKnBf&quot;,&quot;Ewjrd8MILo9sRkxtPi3cwFcenQf&quot;,&quot;B1Q7dohdtoulRsxsUONcKNJBnTc&quot;,&quot;ZbKbdghNyofvlpxCW7ic4dYonyg&quot;,&quot;EFDRde8S3ooXryxU9iZc3q97nDf&quot;,&quot;KLnMd6usoo7CLrx6pwEcaqMBn5d&quot;,&quot;WLkLd14BdoY1FaxCPLDc6M1hn3e&quot;,&quot;BgFndFfkBo6u9Bxl7stcEHubnkb&quot;,&quot;WDVAdrsago3oydxGtuVce4ovnGb&quot;,&quot;DnfLdWtnPoKDLQxkYCQc44BVn7g&quot;,&quot;BfI8dXzgMociYrxvWF7cX2Vhn4k&quot;,&quot;NkQEdmFLgoMCj4xQLvOcW6tJnOh&quot;,&quot;FvV5dSZDLo5Il3xiv99cN3GCnMg&quot;,&quot;Tl2wdZuzEozSiQxDmIFcJ6gxngg&quot;,&quot;VjnodXkLioBxHxxabsrc4C4lnch&quot;,&quot;GxIWdni6worl0LxOltbc110Sn5e&quot;],&quot;text&quot;:{&quot;apool&quot;:{&quot;nextNum&quot;:2,&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7129372845536198657&quot;],&quot;1&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]}},&quot;initialAttributedTexts&quot;:{&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+5*1+9&quot;},&quot;text&quot;:{&quot;0&quot;:&quot;斯洛文尼亚 Slovenia&quot;}}},&quot;align&quot;:&quot;&quot;,&quot;doc_info&quot;:{&quot;editors&quot;:[&quot;7129372845536198657&quot;,&quot;7301936627151208452&quot;],&quot;options&quot;:[&quot;editors&quot;,&quot;create_time&quot;],&quot;deleted_editors&quot;:[],&quot;option_modified&quot;:null}}}},&quot;payloadMap&quot;:{&quot;GxIWdni6worl0LxOltbc110Sn5e&quot;:{&quot;level&quot;:1}},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:132,&quot;type&quot;:&quot;block&quot;,&quot;recordId&quot;:&quot;GxIWdni6worl0LxOltbc110Sn5e&quot;}],&quot;pasteFlag&quot;:&quot;1857c87f-aa03-4f79-8240-3315a8fb4482&quot;}" data-lark-record-format="docx/record" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">标准工时为每天8小时，每周40小时。标准工作周从星期一到星期五。</span><div data-page-id="JNrDdeJYCoVQzNxX8ZBcyHnCnk2" data-docx-has-block-data="false"><div style="text-indent:2em" class="ace-line text-indent ace-line old-record-id-Qk7Pd4fRDowsruxsYV1cbmjFnze">加班费并非强制性要求。超出标准工作时间的小时可以视为加班。加班工资应在双方协商后达成一致。</div></div><span data-lark-record-data="{&quot;isCut&quot;:false,&quot;rootId&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;parentId&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;blockIds&quot;:[97,98],&quot;recordIds&quot;:[&quot;IJnydGmnvov3ynxwidhc0MhBn5e&quot;,&quot;Qk7Pd4fRDowsruxsYV1cbmjFnze&quot;],&quot;recordMap&quot;:{&quot;IJnydGmnvov3ynxwidhc0MhBn5e&quot;:{&quot;id&quot;:&quot;IJnydGmnvov3ynxwidhc0MhBn5e&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7301936627151208452&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;标准工时为每天8小时，每周40小时。标准工作周从星期一到星期五。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+w&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false,&quot;text_indent&quot;:1}},&quot;Qk7Pd4fRDowsruxsYV1cbmjFnze&quot;:{&quot;id&quot;:&quot;Qk7Pd4fRDowsruxsYV1cbmjFnze&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7301936627151208452&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;加班费并非强制性要求。超出标准工作时间的小时可以视为加班。加班工资应在双方协商后达成一致。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+19&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false,&quot;text_indent&quot;:1}},&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;:{&quot;id&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;page&quot;,&quot;parent_id&quot;:&quot;&quot;,&quot;comments&quot;:[],&quot;revisions&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7129372845536198657&quot;,&quot;children&quot;:[&quot;doxcnrlIQafv5oqgyitiH0gpOAc&quot;,&quot;doxcnIVT2bWo2JSf7VBNNcgW0mf&quot;,&quot;doxcnQQm9wJcgNJ3a37JGfuZkZc&quot;,&quot;doxcn1EhzSzQuYIWzOlOWp3l7ue&quot;,&quot;doxcnFAPfrx8NxVP9J8g6MQ3C6c&quot;,&quot;doxcnNc0lnA8DasJGNB6b8O0Oif&quot;,&quot;doxcnhtSBusiXcpww5lMjPIq3Nh&quot;,&quot;doxcnzjkA4JFJcZUV4swT8uZu0c&quot;,&quot;Wm9edigrDoDEroxZQzocPlLNnPe&quot;,&quot;KFIodNwizo1F8QxtvtncubjQnmM&quot;,&quot;RiWrdthuGoX0mWxwj3CcKqJEnUh&quot;,&quot;LulKdZ7ESoujjKx1UpEc61YEndb&quot;,&quot;NQWydRZARo0r6SxFK7EcnrJBnKh&quot;,&quot;FJi3di9PxoaJZxxd9EvcOUHKn3b&quot;,&quot;Jdi3dMHwqoCdeKxRMNUcRFCXnrd&quot;,&quot;V3bvdZdXVoX7bmxnEIWcA0Ccnfh&quot;,&quot;doxcnDBNcdzgautcIjqkKa10Pff&quot;,&quot;LRXMd2jzCoUGQQxOkPWcIrwtnFd&quot;,&quot;C4lCdUtNaorGkyxASX1cGPdanpc&quot;,&quot;FXjidN8XloeklBxgOytcquRNntc&quot;,&quot;UzemdIhqIoS4VkxnS6xcTVR7nDc&quot;,&quot;UqBsdu8MToEqsbxxklmceO7vn6b&quot;,&quot;EaGLdyxtYo5orexJR70cWz8innf&quot;,&quot;RQ1rdNrbNorCPKxH40scDO3ynnW&quot;,&quot;UNaAdOZv7oRUQMxz9J1cLICgnjd&quot;,&quot;doxcn2U5jomoDTDJhlqlXTOafId&quot;,&quot;BiEDdZlMmoHfUEx9tv4c7iyKnVb&quot;,&quot;TVJydqjIworRkJxvbHkcyxsJnEf&quot;,&quot;PgdRdIJOko9X9rx59YdcoSNLngc&quot;,&quot;EaK8daVNXogsCIxfOq0csFoTnUd&quot;,&quot;OJSldSfIqoncApxYL7eczaJdnlf&quot;,&quot;S6PydcItYoiqB8xosEtchmIfnud&quot;,&quot;Sqnbdg2uaoUlW8xJiL0cmznWnad&quot;,&quot;UulUdBjRjo8SYix6FG7cyaeEn90&quot;,&quot;AGgidbxMtooJdMxZnnxccLTxnEf&quot;,&quot;Yha1dkf6ToLBB3xtAvccRU6InqM&quot;,&quot;doxcnKhEIL89pSCi3A0xvEu3exe&quot;,&quot;Yilpdk1ZRoHoLXx94ZncTPsPnPh&quot;,&quot;CS4jdKAvRofZenxT2o4cGhyJnFg&quot;,&quot;Qy1Aduf5woFWXTxOoQtct47bncb&quot;,&quot;DzVqdx2doo8hbpxaZf0cWwSpn7d&quot;,&quot;LhL8dFOYUo1OwDxka42cdPDanQm&quot;,&quot;PPIBdW7wgo5FZ4x94Poc174Unqc&quot;,&quot;QiRJdrXwbozOQuxhRiHcnLAbnrc&quot;,&quot;YXABdFBjfoRo71xqCK8csf3inNd&quot;,&quot;doxcn9WdQliNBV33k6YDLCeX8Uc&quot;,&quot;LaZddP3NaoL9cBxU7m0cGYxEn0f&quot;,&quot;GBphdIEMooe7YOxIHtGc8ubinXd&quot;,&quot;ANEAdXZn2oA9KmxxwDEcIsRznLd&quot;,&quot;Gwvhdl7FzoY78TxL5kpcbdw3n1e&quot;,&quot;CSi2d5rpYo6ymgx53RIc9ibVnAg&quot;,&quot;doxcnFsm2zwN8UKCuiI5fSQ3eOf&quot;,&quot;QAzzdVv1gopWtUxpKMuc5hI9nCf&quot;,&quot;CrCydxq1toyzxUx9FxWcaZ5wnsd&quot;,&quot;doxcnSGXVV4uBGg5uUwHlgNFjDe&quot;,&quot;FzdzdZIwcoEOLfxFkOmccjyYnzd&quot;,&quot;Zj7JdDxW7o7aGkxPNPFcdwwAngb&quot;,&quot;MfBldTDdkogh7exIG31cr57Knfe&quot;,&quot;X1MUd7x8ToBjTOxdICEcTrWenUf&quot;,&quot;P9hsd9O9eoUFFzxkURbcnj2Mnmh&quot;,&quot;CkE6d2vZVoyYcQxKQ3ccxSiinTY&quot;,&quot;JQujdnwySo01JIxpzPZcpnhhnGe&quot;,&quot;X0PVdcsQgoqojixddrlcjS86nZe&quot;,&quot;UPZQdDTtVocMfjxg457cxvRWnob&quot;,&quot;RqcOd32Wvo8VdzxiSLnc0OYknkf&quot;,&quot;YoUcdoHZAoLSkfxfl7dcT7fqnTh&quot;,&quot;LxMzdvnqUo7e28xLVX8cgU71nLh&quot;,&quot;Yga1dTmaEoEhV0xf09Uc8UZznjd&quot;,&quot;VEZtdQpCEoKqtLxCnIjcOYG0nFd&quot;,&quot;QJfJd1gCAoJlEOxjxhNcEKA1n0b&quot;,&quot;XdrfdzEiPoTulkxIuSIczLLBn2g&quot;,&quot;GwCadDgI6oU5QEx5glNc6a0yndg&quot;,&quot;Vf94dWm9LoFvHqxDeszcDV28ncc&quot;,&quot;WysmdabDtoXNtLxqPXxcgGCcntg&quot;,&quot;Vtx7dcjREoK1SWxn9SxcUyf9nad&quot;,&quot;PqjxdKiFtoL1KXxzdFacWyzyn9g&quot;,&quot;OW7VdX1QxoK71NxQxaQcWPe5nVe&quot;,&quot;JRnGdNFcuogqKIxWJJwcD6sanXf&quot;,&quot;AzybdY35HoN7p0xlpDqcW8L6nob&quot;,&quot;YCXVduytOooRaXxQvGGcdOu8nCd&quot;,&quot;Cm74dq6wco7TVIxjibCctSLFnTf&quot;,&quot;I0NndBKedoIFr0xoanuchnf7n3f&quot;,&quot;ZQJTdBSQ5o0qnzxtox7ckCm7n2b&quot;,&quot;XOfFdNTrsoCAHcxYsiZcwVXnn7e&quot;,&quot;IJnydGmnvov3ynxwidhc0MhBn5e&quot;,&quot;Qk7Pd4fRDowsruxsYV1cbmjFnze&quot;,&quot;RsINdVVLDoOXJUx4r0Gc2Qwen9b&quot;,&quot;JuDod0noFowgoEx4K7bct2xUn8e&quot;,&quot;ONo0dDwO9oYvjfxv9pbchnNjnMg&quot;,&quot;HQtPdFIKVo97tExRGg2cJdjSn2b&quot;,&quot;IdU5dUsIMoVMmpxrT3lcMwcEnxK&quot;,&quot;Y9iZdS7aqoOolQxWrdDcCmn8nnh&quot;,&quot;RBSGdiVvAo4zKDxjVefcpq9Mn5g&quot;,&quot;LmnbdSOy6oIRRyxYyqAchaiwnnf&quot;,&quot;JaccdQqvAoavInxYDpicvaqznOf&quot;,&quot;FbOxd3YQHo3VsWx5eNfcy7Genkc&quot;,&quot;EDTfdw1uxoisOhxIvKQcRbR0n8c&quot;,&quot;OGO7dRB3VouXmrxQiNZcK5jPnPb&quot;,&quot;KwwxdOlxPo7LayxzXSkcHaSNnph&quot;,&quot;X8qTdoR2yoQUnRxn1sScktvWntg&quot;,&quot;ElXJdO3tDoAJvkx7MgFcUT7Xn3d&quot;,&quot;GuwUdBYJToPm0CxoUY5c3bt1nbb&quot;,&quot;PaLwdeBRLoqNzyxl9TKcYEhFnrh&quot;,&quot;GOKadg4A3o57q9xRzh1cQNYpntc&quot;,&quot;OFJNdeDpToDnWFxdVB2cfENKnBf&quot;,&quot;Ewjrd8MILo9sRkxtPi3cwFcenQf&quot;,&quot;B1Q7dohdtoulRsxsUONcKNJBnTc&quot;,&quot;ZbKbdghNyofvlpxCW7ic4dYonyg&quot;,&quot;EFDRde8S3ooXryxU9iZc3q97nDf&quot;,&quot;KLnMd6usoo7CLrx6pwEcaqMBn5d&quot;,&quot;WLkLd14BdoY1FaxCPLDc6M1hn3e&quot;,&quot;BgFndFfkBo6u9Bxl7stcEHubnkb&quot;,&quot;WDVAdrsago3oydxGtuVce4ovnGb&quot;,&quot;DnfLdWtnPoKDLQxkYCQc44BVn7g&quot;,&quot;BfI8dXzgMociYrxvWF7cX2Vhn4k&quot;,&quot;NkQEdmFLgoMCj4xQLvOcW6tJnOh&quot;,&quot;FvV5dSZDLo5Il3xiv99cN3GCnMg&quot;,&quot;Tl2wdZuzEozSiQxDmIFcJ6gxngg&quot;,&quot;VjnodXkLioBxHxxabsrc4C4lnch&quot;,&quot;GxIWdni6worl0LxOltbc110Sn5e&quot;],&quot;text&quot;:{&quot;apool&quot;:{&quot;nextNum&quot;:2,&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7129372845536198657&quot;],&quot;1&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]}},&quot;initialAttributedTexts&quot;:{&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+5*1+9&quot;},&quot;text&quot;:{&quot;0&quot;:&quot;斯洛文尼亚 Slovenia&quot;}}},&quot;align&quot;:&quot;&quot;,&quot;doc_info&quot;:{&quot;editors&quot;:[&quot;7129372845536198657&quot;,&quot;7301936627151208452&quot;],&quot;options&quot;:[&quot;editors&quot;,&quot;create_time&quot;],&quot;deleted_editors&quot;:[],&quot;option_modified&quot;:null}}}},&quot;payloadMap&quot;:{&quot;IJnydGmnvov3ynxwidhc0MhBn5e&quot;:{&quot;level&quot;:1},&quot;Qk7Pd4fRDowsruxsYV1cbmjFnze&quot;:{&quot;level&quot;:1}},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:97,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:32},&quot;recordId&quot;:&quot;IJnydGmnvov3ynxwidhc0MhBn5e&quot;},{&quot;id&quot;:98,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:45},&quot;recordId&quot;:&quot;Qk7Pd4fRDowsruxsYV1cbmjFnze&quot;}],&quot;pasteFlag&quot;:&quot;1857c87f-aa03-4f79-8240-3315a8fb4482&quot;}" data-lark-record-format="docx/record" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">每月最低工资为1203.36欧元，由劳动部门每年更新一次。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;每月最低工资为1203.36欧元，由劳动部门每年更新一次。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+t&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:91,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:29},&quot;recordId&quot;:&quot;AzybdY35HoN7p0xlpDqcW8L6nob&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>  雇主成本占员工工资的16.64%。<br>  每月成本：<br>  &#8211; 养老和残疾保险 &#8211; 8.85%<br>  &#8211; 健康保险费 &#8211; 6.56%<br>  &#8211; 失业保险费 &#8211; 0.60%<br>  &#8211; 工伤保险费 &#8211; 0.53%<br>  &#8211; 父母保护费 &#8211; 0.10%<br>  &#8211; 餐饮和交通津贴 &#8211; 每月175.12欧元*<br>  &#8211; 在家办公津贴 &#8211; 每月50欧元<br>  *按照每天7.96欧元的津贴乘以22个平均工作日估算<br>  年度成本：<br>  假日奖金 &#8211; 每年1203.36欧元</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p>终止必须遵守复杂的规定和员工雇佣国家的规定。解雇总是由雇主与主要利益相关者处理。这可能包括特定解雇案件中的临时费用以及所需或建议的步骤。<br>解雇可以通过双方协议、雇主或员工完成。合同只能以书面形式订立，不接受其他形式的沟通。<br>最常见的解雇类型包括：<br>  &#8211; 常规解雇：在员工过剩、能力不足或不当行为等情况下。员工有权获得解雇补偿。对于服务了一年到十年的员工，每年可获得前三个月的平均工资的1/5。对于服务了超过十年的员工，解雇补偿为1/4；超过20年的员工为1/3。<br>  &#8211; 特殊解雇：当员工犯罪、缺勤、提供虚假数据、拒绝调动等严重违规行为时。解雇必须以书面形式提供，并明确解雇原因，并必须亲自交付。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>通知期将取决于雇佣合同中达成的协议。最长通知期为60天，如果一方要求较短的通知期，则必须遵守法律规定：<br>  &#8211; 在职不足一年，通知期为15天<br>  &#8211; 在职一年后，通知期为30天。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>解雇补偿金额受《公司法》管制。支付金额将取决于解雇类型。因此，由于特定原因提前终止将有资格获得解雇补偿。<br>如果适用解雇补偿，金额将取决于每个案件，包括雇佣期限。以下工资可作为参考：<br>  &#8211; &#8211; 如果员工的雇佣期不超过10年，则每年的解雇补偿将为过去三个月的平均每月总工资的一半。<br>  &#8211; &#8211; 如果员工的雇佣期不超过20年，则每年的解雇补偿将为过去三个月的平均每月总工资的四分之一。<br>  &#8211; &#8211; 如果员工的雇佣期超过20年，则每年的解雇补偿将为过去三个月的平均每月总工资的三分之一。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>无明确规定</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">全职和兼职员工每年享有20个工作日的带薪休假。带薪休假每月累积1.66天。员工在工作X个月后有资格休年假。在雇佣的第一年，员工不得休假超过XX天。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;全职和兼职员工每年享有20个工作日的带薪休假。带薪休假每月累积1.66天。员工在工作X个月后有资格休年假。在雇佣的第一年，员工不得休假超过XX天。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+21&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:122,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:73},&quot;recordId&quot;:&quot;KLnMd6usoo7CLrx6pwEcaqMBn5d&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">病假</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">只要员工出示医疗证明，就没有病假限制。如果疾病或受伤与工作相关，前30天由雇主承担。之后，由社会保障支付。</span></p><p>付金额和支付责任将取决于请假原因。例如，儿童病假和工伤将由医疗保险支付。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">员工有权享受30天的陪产假。在此期间，政府将支付工资，金额将根据过去12个月的平均月工资计算。请假应在孩子出生后开始。</span></p><p>员工每人有权享受130天的育婴假。如果同一时间出生双胞胎或更多孩子，早产儿或需要额外照顾的孩子，将延长这段假期。<br>育婴假的福利金额等于基本工资的100%，不得超过平均月工资的2.5倍。<span data-lark-record-data="{&quot;rootId&quot;:&quot;JNrDdeJYCoVQzNxX8ZBcyHnCnk2&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;员工有权享受30天的陪产假。在此期间，政府将支付工资，金额将根据过去12个月的平均月工资计算。请假应在孩子出生后开始。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+1n&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:103,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:59},&quot;recordId&quot;:&quot;IdU5dUsIMoVMmpxrT3lcMwcEnxK&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p>个人所得税范围从16%到50%不等。个人所得税根据渐进税率计算。多个额外因素可能影响整体税率，如家庭状况和子女人数。</p><figure class="wp-block-table"><table><tbody><tr><td>年总收入</td><td>税率 (%)</td></tr><tr><td>不超过 EUR 8,500</td><td>16%</td></tr><tr><td>不超过 EUR 25,000</td><td>26%</td></tr><tr><td>不超过 EUR 50,000</td><td>33%</td></tr><tr><td>不超过 EUR 72,000</td><td>39%</td></tr><tr><td>超过 EUR 72,000</td><td>50%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">社会保险</h2><ul><li>假日津贴</li><li>餐饮津贴</li><li>养老保险</li><li>公共医疗保险</li><li>在家办公津贴</li><li>工伤保险</li><li>私人医疗保健 &#8211; Unisure（可选）</li><li>私人医疗保健 &#8211; Allianz（可选）</li></ul><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1-1.2</td></tr><tr><td>Preseren Day</td><td>2.8</td></tr><tr><td>Easter Sunday</td><td>3.31</td></tr><tr><td>Easter Monday</td><td>4.1</td></tr><tr><td>Day of Uprising Against Occupation</td><td>4.27</td></tr><tr><td>Labour Day</td><td>5.1-5.2</td></tr><tr><td>Whit Sunday</td><td>5.19</td></tr><tr><td>Statehood Day</td><td>6.25</td></tr><tr><td>Assumption of Mary</td><td>8.15</td></tr><tr><td>Reformation Day</td><td>10.31</td></tr><tr><td>Remenberance Day</td><td>11.1</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr><tr><td>Independence and Unity Day</td><td>12.26</td></tr></tbody></table></figure><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>