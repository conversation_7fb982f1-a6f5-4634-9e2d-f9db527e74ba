<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在中国澳门雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在中国澳门雇佣',
  ogDescription: '基础信息 货币： 首都： 官方语言： 薪资周期： SmartDeer提供的本地福利 薪酬和税务 Pay&amp […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 货币： 首都： 官方语言： 薪资周期： SmartDeer提供的本地福利 薪酬和税务 Pay&amp […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在中国澳门雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在中国澳门雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/04/澳门.jpg';
const flagImage = '';
const htmlContent = '<h2 class="wp-block-heading">基础信息</h2><p>货币：</p><p>首都：</p><p>官方语言：</p><p>薪资周期：</p><h2 class="wp-block-heading">SmartDeer提供的本地福利</h2><h2 class="wp-block-heading">薪酬和税务 Pay&amp;Tax</h2><h2 class="wp-block-heading">请休假政策 Leave policy</h2><h2 class="wp-block-heading">终止规则 Termination</h2><h2 class="wp-block-heading">法定休息时间 Statutory time off</h2><h2 class="wp-block-heading">入职 Onboarding</h2><h2 class="wp-block-heading">附加信息 Additional info</h2>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>