<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在马耳他雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在马耳他雇佣',
  ogDescription: '基础信息 首都：瓦莱塔 时区：GMT+1 语言：马耳他语和英语 货币代码：EUR 人力资源制度概况 雇佣合同 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：瓦莱塔 时区：GMT+1 语言：马耳他语和英语 货币代码：EUR 人力资源制度概况 雇佣合同 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在马耳他雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在马耳他雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/010c6b58ae92c5a801219c77797726.jpg@3000w_1l_2o_100sh.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/t01565547b11c56e5a5.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：瓦莱塔</p><p>时区：GMT+1</p><p>语言：马耳他语和英语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">&nbsp;&nbsp;合同必须用英文书写。必须书面并由双方签字。</span></p><p>&nbsp;&nbsp;合同必须包括：</p><ul><li>姓名</li><li>开始日期</li><li>就业期限</li><li>工作描述</li><li>终止条件</li></ul><ol start="2"><li>试用期</li></ol><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">试用期是强制性的。最短试用期为3个月，最长试用期为6个月。对于具有比年度最低工资高出两倍的年度最低工资的行政、技术、管理或执行员工，试用期可以延长至6个月，并且可以续约6个月（总计1年）。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;试用期是强制性的。最短试用期为3个月，最长试用期为6个月。对于具有比年度最低工资高出两倍的年度最低工资的行政、技术、管理或执行员工，试用期可以延长至6个月，并且可以续约6个月（总计1年）。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+2m&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:131,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:94},&quot;recordId&quot;:&quot;WfOfd997ro9tw0xV1eCco2GanBb&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周从星期一到星期五。最长工作周可延长至48小时。</p><p>兼职雇佣协议覆盖的最长工作时间为48小时，但标准可视为40小时。超过该时段的工作时间将被视为加班。</p><p>加班费是强制性的。在标准工作时间外工作的时间被视为加班。员工每周最多可以工作48小时，除非员工提供书面同意。对于额外的工作时间，员工将获得：</p><ul><li>每小时标准工资的150%，适用于星期一至星期六的工作。</li><li>每小时标准工资的200%，适用于星期日或公共假日的工作</li></ul><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月731.32欧元。最低工资根据员工的年龄而变化。员工有权获得根据马耳他政府规定的年度生活成本调整。对于2023年，全职员工的增幅为514.80欧元，兼职员工为386.10欧元。</p><p>请注意：每年，马耳他政府计算一次生活成本调整。这是年度预算的一部分，根据去年的通货膨胀数据进行计算。2024年的生活成本调整为每周12.81欧元。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计约为员工工资的10.30%。政府奖金（与每周津贴和法定奖金相关）将额外增加到这个雇主成本上。</p><ul><li>社会保障 &#8211; 10%*</li><li>产假信托基金 &#8211; 0.30%</li><li>每半年政府奖金：每周津贴，分别在3月底和9月底支付 &#8211; 121.16欧元</li><li>每半年政府奖金：法定奖金，分别在6月底和12月底支付 &#8211; 135.10欧元</li></ul><p>*最高的社会保障缴纳额是根据员工每周的工资和月份中的周一数目计算的。对于每周收入超过515.99欧元的员工，社会保障缴纳额将在每月四个周一时为最多206.40欧元，在每月五个周一时为最多258欧元。</p><h3 class="wp-block-heading has-large-font-size">终止原因</h3><h2 class="wp-block-heading has-large-font-size">终止规则</h2><p>终止必须遵守复杂的规定和雇佣国家的规则。解雇总是由雇主与主要利益相关者处理。这可能包括特定解雇情况下的特定费用或必要或建议的步骤。</p><p>马耳他的解雇可能会很复杂。在马耳他，雇主在试用期外无权随意解雇，解雇必须有正当理由。</p><p>符合规定的终止包括：</p><ul><li>员工自愿辞职</li><li>双方协议终止</li><li>雇主单方面基于以下理由终止：<ul><li>试用期评估</li><li>客观原因</li><li>纪律解雇</li><li>因工作不适任而表现不佳</li></ul></li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通知期为一周，根据就业期限的长度而增加。</p><ul><li>如果服务时间少于6个月，则为一周</li><li>如果服务时间少于2年，则为2周</li><li>如果服务时间少于4年，则为4周</li><li>如果服务时间少于7年，则为8周</li><li>超过七年，每增加一年或部分年的服务，额外增加1周，最多增加到十二周</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">在马耳他，没有规定员工需要支付解雇费用。在通知期内，员工将收到标准的工资薪酬。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;在马耳他，没有规定员工需要支付解雇费用。在通知期内，员工将收到标准的工资薪酬。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+13&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:116,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:39},&quot;recordId&quot;:&quot;SPuXdnjXqoWY3yxbPkGc7appnWg&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>没有明确规定</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职员工有权享受24个工作日的带薪休假。但如果公共假日落在周末，则员工有权额外休假一天。</p><p>2022年，有4个公共假日落在周末，因此当前年度员工有权享受28天的休假。</p><p>带薪休假按月累积，每月累积2天，员工在累积一个月的工作后即有资格休年假。</p><p>兼职员工有权享受每周平均工作小时数乘以24的带薪休假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p><span style="text-indent: 2em; color: rgb(0, 0, 0); font-size: medium; white-space-collapse: collapse; background-color: var(--wp--preset--color--base); font-family: var(--wp--preset--font-family--body);">员工有权享受最多10天（80小时）的带薪病假。该假期的支付标准和支付者根据其持续时间而异：</span></p><figure class="wp-block-table"><table><tbody><tr><td>时期</td><td>支付</td><td>付款人</td></tr><tr><td>0 &#8211; 3 天</td><td>基本工资的100%</td><td>雇主</td></tr><tr><td>4 &#8211; 10 天</td><td>基本工资的100%</td><td>社会保障</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">员工有权享受4个月的育婴假。父母双方均有权享受此假期，应在孩子年满8岁前休假。父母双方均可选择休育婴假。育婴假期间不发放工资。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;员工有权享受4个月的育婴假。父母双方均有权享受此假期，应在孩子年满8岁前休假。父母双方均可选择休育婴假。育婴假期间不发放工资。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+1r&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:105,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:63},&quot;recordId&quot;:&quot;DoIvdGvq5ogf1nxbboMcDItinOe&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p><span style="text-indent: 2em; color: rgb(0, 0, 0); font-size: medium; white-space-collapse: collapse; background-color: var(--wp--preset--color--base); font-family: var(--wp--preset--font-family--body);">个人所得税的税率为0%至35%不等。个人所得税根据渐进税率计算。许多额外因素可能会影响整体税率，例如家庭状况和子女人数。</span></p><p><span style="color: rgb(0, 0, 0); font-size: medium; white-space-collapse: collapse; background-color: var(--wp--preset--color--base); font-family: var(--wp--preset--font-family--body);">单身：</span></p><figure class="wp-block-table"><table><tbody><tr><td>年总收入</td><td>税率 （％）</td></tr><tr><td>高达 9,100 欧元</td><td>0%</td></tr><tr><td>高达 14,500 欧元</td><td>15%</td></tr><tr><td>高达 19,500 欧元</td><td>25%</td></tr><tr><td>高达 60,000 欧元</td><td>30%</td></tr><tr><td>超过 60,000 欧元</td><td>35%</td></tr></tbody></table></figure><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">已婚：</span></p><figure class="wp-block-table"><table><tbody><tr><td>年总收入</td><td>税率 （％）</td></tr><tr><td>高达 12,700 欧元</td><td>0%</td></tr><tr><td>高达 21,200 欧元</td><td>15%</td></tr><tr><td>高达 28,700 欧元</td><td>25%</td></tr><tr><td>高达 60,000 欧元</td><td>30%</td></tr><tr><td>超过 60,000 欧元</td><td>35%</td></tr></tbody></table></figure><p>父母：</p><figure class="wp-block-table"><table><tbody><tr><td>年总收入</td><td>税率 （％）</td></tr><tr><td>高达 10,500 欧元</td><td>0%</td></tr><tr><td>高达 15,800 欧元</td><td>15%</td></tr><tr><td>高达 21,200 欧元</td><td>25%</td></tr><tr><td>高达 60,000 欧元</td><td>30%</td></tr><tr><td>超过 60,000 欧元</td><td>35%</td></tr></tbody></table></figure><p><div data-page-id="Qo8kdR3Zbo3yv0xg3HVc2BoqnGd" data-docx-has-block-data="false"><div class="ace-line ace-line old-record-id-HbD3dw0xeosjKixwU9YcMJlqnoR"></div></div><span data-lark-record-data="{&quot;rootId&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;已婚：&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+3&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:88,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:3},&quot;recordId&quot;:&quot;HbD3dw0xeosjKixwU9YcMJlqnoR&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">社会保险</h2><p><div data-page-id="Qo8kdR3Zbo3yv0xg3HVc2BoqnGd" data-docx-has-block-data="false"><ul start="1" class="list-bullet1"><li class="ace-line ace-line old-record-id-YkXsdzmS5oDFCCxKr1TclnTQnke" data-list="bullet">社会保障私人医疗保险 &#8211; Unisure（可选）私人医疗保险 &#8211; 安联（可选）</li></ul></div><span data-lark-record-data="{&quot;isCut&quot;:false,&quot;rootId&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;parentId&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;blockIds&quot;:[78,79,80],&quot;recordIds&quot;:[&quot;YkXsdzmS5oDFCCxKr1TclnTQnke&quot;,&quot;Y5Gxd4yqgoWh8axNO2PcKPAYnVg&quot;,&quot;PZsDdgvXKoCulrxl3axcCNNOngc&quot;],&quot;recordMap&quot;:{&quot;YkXsdzmS5oDFCCxKr1TclnTQnke&quot;:{&quot;id&quot;:&quot;YkXsdzmS5oDFCCxKr1TclnTQnke&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;bullet&quot;,&quot;parent_id&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7301936627151208452&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;社会保障&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+4&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;Y5Gxd4yqgoWh8axNO2PcKPAYnVg&quot;:{&quot;id&quot;:&quot;Y5Gxd4yqgoWh8axNO2PcKPAYnVg&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;bullet&quot;,&quot;parent_id&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7301936627151208452&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;私人医疗保险 - Unisure（可选）&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+k&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;PZsDdgvXKoCulrxl3axcCNNOngc&quot;:{&quot;id&quot;:&quot;PZsDdgvXKoCulrxl3axcCNNOngc&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;bullet&quot;,&quot;parent_id&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7301936627151208452&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;私人医疗保险 - 安联（可选）&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+f&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;:{&quot;id&quot;:&quot;Qo8kdR3Zbo3yv0xg3HVc2BoqnGd&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;page&quot;,&quot;parent_id&quot;:&quot;&quot;,&quot;comments&quot;:[],&quot;revisions&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7129372845536198657&quot;,&quot;children&quot;:[&quot;doxcnN0UgzQFJzva9r8HH4clnUc&quot;,&quot;doxcn9hAa56qRzsiTjczaJdNB9b&quot;,&quot;doxcnEgJXq07eNlrbY2WA1FJPad&quot;,&quot;doxcnIRvwVBYt0n5LnmoGUAmB2c&quot;,&quot;doxcnu6HgfPhr23MHycMBGDzJYf&quot;,&quot;doxcn8wSfoZG6Tckebk1UF5xJlc&quot;,&quot;doxcnH6PM0wvQBvKESGssbtKBed&quot;,&quot;doxcn3R2WAHJHAwXcOqYW9Zufub&quot;,&quot;HiKDdaA80ouyoCxUDzdcV9hbnKd&quot;,&quot;MWdddWr7Io3zbGxfGNzc1iQLnqe&quot;,&quot;PlBldTV8moCbrxxasKIce9JWn4f&quot;,&quot;doxcnoFWm8RByMIHu9ikwk8vWlc&quot;,&quot;N242dJeDYob1SoxKuqscwHuZnye&quot;,&quot;W9ktdUifzoijXtxtGuIcnYB1nbh&quot;,&quot;Xmp7dio85o7SpixEYuZcw8Z3n0b&quot;,&quot;V8FsduLu9o1w7Qxqm13cC41An9e&quot;,&quot;Wup5d8RRBobcKAx8lKMcdmGxnsb&quot;,&quot;HjIBdHwZXo6BpOxOAoWc92fRnRg&quot;,&quot;DNmZdutFQo8UnbxeGN8cHSkHnLg&quot;,&quot;ZG62dqlIQoW6m3xmOV2cXnnFnhP&quot;,&quot;ExLzddOYaoqNrAxnK0bccSzjnpd&quot;,&quot;QEl4dZE7No5e7IxAvIkc6bCenSg&quot;,&quot;NQu8d3AOmoaLKOx3LnqcIEEhn5b&quot;,&quot;UNgbdgQAyosHr5xWt05c5cOEnOf&quot;,&quot;OpXVdKYyNoT3GcxqPa8cdyD4nVg&quot;,&quot;QIlTd8RIkoz1HvxocihcDOGxnIh&quot;,&quot;ZIccdAYqpoe6drx9fRucpbUlnCb&quot;,&quot;doxcnCJxVOHnE2mpwxY0RmTo3Fb&quot;,&quot;JtBmdunNTo1iclx4vPfcG7EKnkc&quot;,&quot;FAnIdAVxToVKaVxzu2qcKbyvn9c&quot;,&quot;UemHd1YIboBRetxNPBuchJY7nSd&quot;,&quot;P0ivdPrdUovYqXx8lU5cKzSVnDC&quot;,&quot;Xf7YddCWWoWr2CxNgnrcLWd1nSf&quot;,&quot;L3c2dZMPoomDqVxj4FPcEh5Inth&quot;,&quot;Hbk0dmqNbo3LoqxqzjpcO87In9b&quot;,&quot;PQszdVxGso7dWwxdEZRcmYyunAh&quot;,&quot;BuU3d1VaBojQ4Jx3QIcctrOWnXc&quot;,&quot;ZijqdmEjVotGzNxvFQucDU6nnFc&quot;,&quot;doxcne9SrpQUNPCxaFB7BkH1avb&quot;,&quot;GR6udn0M0oPMiTxXX0CcQ2LwnOf&quot;,&quot;FFj8daRwCohMUoxL4GbclvNRnRd&quot;,&quot;UvDMdBtXpo1kAdxXC46chqc8nwb&quot;,&quot;XSgHdxLK3oYURqxr9qEcMSsynyd&quot;,&quot;QT27dOTKloYU7yxx900cVOJ6njg&quot;,&quot;doxcnxX1UZohFiTjKdgQG3lDJbg&quot;,&quot;ZCgAdntdJo6pApxAy7Tcsslxnfh&quot;,&quot;Ba4wd6DKAoxDfWxlny9cqrM5nwf&quot;,&quot;BTHidfOkqofuvdxp8J4cstn2nsh&quot;,&quot;XKrJd1aRkoneIpx4mOTcJteznlh&quot;,&quot;S6vIdV8paoPxjPxb6sXcR6mYn2g&quot;,&quot;UHhqdnbNSob8q7xi02XclTRCnj0&quot;,&quot;RShBd1HEloFNp4xPSPDcn8EknNe&quot;,&quot;AdC4dhpPLoRKBpxlrJvcrXQKnzd&quot;,&quot;doxcnvDoQMV0EA5NBUtrdRs4ung&quot;,&quot;SyQwdD6SLotlLexKlU7cNhk0nWh&quot;,&quot;ImBIdLe05oIE8pxq4xtcCryTn0e&quot;,&quot;doxcnc6TbIQwYtmYAkYxmlFi3Xg&quot;,&quot;Wy6odFubeoOGraxyHSBcQs6Xn2e&quot;,&quot;LcJydMX2ToEhWJxMFdyc1v6EnXd&quot;,&quot;TXDOd8VOtoSnWJxYv8Tc9FDEnPr&quot;,&quot;ZgSvdHybGo3Wb2xtEeucWsEAnJd&quot;,&quot;U8ifd8mDKo3RtOxfgLdcHnVUn4e&quot;,&quot;IYTPddrLLonajKxaM38cIygxnIf&quot;,&quot;Vg5ldYir8oQMPTxLyhjc2milnUg&quot;,&quot;GtHUdU5FWot87sxUn9ecFNEdnff&quot;,&quot;TuRbdun6pomf7Nxpu7RcH6o1nqb&quot;,&quot;NBq8dgvn2oMQC6xuN3bcnS5ynod&quot;,&quot;Loiydm1Ggojaolx8y9rcJAzInSg&quot;,&quot;DjR4dXa81o36dGxy4MGcgZ59nsb&quot;,&quot;YkXsdzmS5oDFCCxKr1TclnTQnke&quot;,&quot;Y5Gxd4yqgoWh8axNO2PcKPAYnVg&quot;,&quot;PZsDdgvXKoCulrxl3axcCNNOngc&quot;,&quot;ZwxFdpciRog8GMxfhZBcJgHCnzg&quot;,&quot;BAVwdJTjDoW0SvxXS3CcS3Gknpd&quot;,&quot;SONPd86vsoQOSwxDjAacdvqpnvg&quot;,&quot;DK8fdkNYfoXJ99x9xwRct1QWnwb&quot;,&quot;XR7YdCSffo0KY9xdLgHcsZ3FnEf&quot;,&quot;TmxXduuHXofM5yxxKeLc54DOnZu&quot;,&quot;AouKdd0MBon31wxYdbpcRaY9nng&quot;,&quot;HbD3dw0xeosjKixwU9YcMJlqnoR&quot;,&quot;CiKMd4KphotsXTxNuKycQaEQnYc&quot;,&quot;Mlt5dfQaioALfoxx2vncDj1EnCc&quot;,&quot;RytPdl1bSoHnWexPwwxcqyEBn5b&quot;,&quot;YHl5dGTMAod1YLxOxd4chDkznkg&quot;,&quot;Mgjfd3oRtoATrzx1gkXcdebfnW1&quot;,&quot;ZElgdY7Zdo5Ct1xOY8NcGEuJnXe&quot;,&quot;RxGEdq1aIoGw26xoUy1cFwIQntf&quot;,&quot;KTGXdBdiRoz2Njx07DBcjqejnEf&quot;,&quot;NASrdqHu3oquIixkzrIcAzydnqc&quot;,&quot;YlTcd9oT5oTux5xbJ57cCMiPncf&quot;,&quot;DHWOdSnIwo2InVx8yPjcK3twncf&quot;,&quot;AlcDdjnRSo9RAfxDn6wcWBYan5g&quot;,&quot;CkFddxIsfol2q2xZvAzcNIFYnNS&quot;,&quot;MOEKdiY43ohXyrxjLSOcqFdInRg&quot;,&quot;ZHU4dS4ftoA25rx4XcucmzcqnLh&quot;,&quot;JYh3dHrAvoGVt0xjyTtcmnNdn6y&quot;,&quot;DoIvdGvq5ogf1nxbboMcDItinOe&quot;,&quot;X4rDd4YGLoPKXCxYl8ocwavVnCh&quot;,&quot;F349dVa4MoBKyqxPalHcWVEfngf&quot;,&quot;ZfxydlQK4ogwwgxGfnLcQgrFnYd&quot;,&quot;YmwBd3rQkoScKAxcGxEcX0RGnUd&quot;,&quot;MFHnd7OzaouGKsxxQrdcETyOnMb&quot;,&quot;THhWde1xQos9iexDtxMcEkSmn9e&quot;,&quot;FO9od3kNBoHYqyxT5HscWe94nzf&quot;,&quot;AMsmdPW9boAjGRxMsrLcdqYinm3&quot;,&quot;LaBxdnJUAoeI8Pxa2T0c8EHmnyb&quot;,&quot;UqJ5dVWJ9oA6ncxHOOmcxmcXnmb&quot;,&quot;SPuXdnjXqoWY3yxbPkGc7appnWg&quot;,&quot;YtoXdptlHo8PaexMpsacKsj9ncd&quot;,&quot;Xi9BdbrD9oipaBxBCCrcmfeFntf&quot;,&quot;UJ33dkF6toQM1Dx8dxbcrBo2n5d&quot;,&quot;MrWjdGHtzolcWyxFbzYcrU0qnbf&quot;,&quot;Im09dahg6oV1xlx5j3KcyzvpnIf&quot;,&quot;SvQkdIE1vogulwxw1sacW7i5nRf&quot;,&quot;Zxo8drgojo6GNZxwlmmcj3StnEc&quot;,&quot;Qrx0dWtx7oCj6JxZl9LcwDWsndg&quot;,&quot;YkT7dCHg5oqz9vxN6I6cc6yanve&quot;,&quot;A5Azd8qoro45e2xR3Z6cyvXWnRc&quot;,&quot;JevEdY0FDoIFiqxbXvXc0HF5nXb&quot;,&quot;FyvpdgwTio94c5xkUpDcKQt4nJh&quot;,&quot;YIq6dSOXioG13qxSgoUc6rMxnAe&quot;,&quot;I7gmdDB2yombqaxALP0cJoRPn0c&quot;,&quot;WfOfd997ro9tw0xV1eCco2GanBb&quot;],&quot;text&quot;:{&quot;apool&quot;:{&quot;nextNum&quot;:2,&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7129372845536198657&quot;],&quot;1&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]}},&quot;initialAttributedTexts&quot;:{&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+3*1+6&quot;},&quot;text&quot;:{&quot;0&quot;:&quot;马耳他 Malta&quot;}}},&quot;align&quot;:&quot;&quot;,&quot;doc_info&quot;:{&quot;editors&quot;:[&quot;7129372845536198657&quot;,&quot;7301936627151208452&quot;],&quot;options&quot;:[&quot;editors&quot;,&quot;create_time&quot;],&quot;deleted_editors&quot;:[]}}}},&quot;payloadMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:78,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:4},&quot;recordId&quot;:&quot;YkXsdzmS5oDFCCxKr1TclnTQnke&quot;},{&quot;id&quot;:79,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:20},&quot;recordId&quot;:&quot;Y5Gxd4yqgoWh8axNO2PcKPAYnVg&quot;},{&quot;id&quot;:80,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:15},&quot;recordId&quot;:&quot;PZsDdgvXKoCulrxl3axcCNNOngc&quot;}],&quot;pasteFlag&quot;:&quot;ed39513c-05dd-4e9b-856a-018d41946616&quot;}" data-lark-record-format="docx/record" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Feast of St Paul&#8217;s Shipwreck</td><td>2.10</td></tr><tr><td>Feast of St Joseph</td><td>3.19</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Freedom Day</td><td>3.31</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Sette Giugno</td><td>6.7</td></tr><tr><td>Feast of St Peter and St Paul</td><td>6.29</td></tr><tr><td>Feast of the Assumption</td><td>8.15</td></tr><tr><td>Victory Day</td><td>9.8</td></tr><tr><td>Independence Day</td><td>9.21</td></tr><tr><td>Immaculate Conception</td><td>12.8</td></tr><tr><td>Republic Day</td><td>12.13</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>