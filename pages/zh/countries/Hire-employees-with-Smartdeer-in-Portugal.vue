<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在葡萄牙雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在葡萄牙雇佣',
  ogDescription: '基础信息 首都：里斯本 时区：GMT+1 语言：葡萄牙语 货币代码：EUR 人力资源制度概况 雇佣合同 合同必 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：里斯本 时区：GMT+1 语言：葡萄牙语 货币代码：EUR 人力资源制度概况 雇佣合同 合同必 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在葡萄牙雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在葡萄牙雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/699B4A8549726A43572088084014BCB01F962D27_w5040_h3360.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/R-C-1-2.jpeg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：里斯本</p><p>时区：GMT+1</p><p>语言：葡萄牙语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p>合同必须用葡萄牙语撰写，也可以是双语的。它们必须书面并由双方签字。</p><p>合同必须包括：</p><ul><li>姓名</li><li>开始日期</li><li>雇佣期限</li><li>工作描述</li><li>试用期条件</li><li>终止条件</li><li>假期和圣诞奖金的支付条件</li><li>远程工作条款</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期不是强制性的。最长试用期为240天。长度取决于合同类型和员工的资历：</p><p>无固定期限合同：</p><ul><li>标准为90天。</li><li>对于具有高级别责任的职位为180天。</li><li>对于管理职位或高级员工为240天。</li></ul><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周从星期一到星期五。</p><p>加班工资是强制性的，不包括在工资中。标准工作时间外的工作时间被视为加班。员工每天最多可以加班2小时。对于加班时间，员工的支付方式为：</p><ul><li>第一小时的时薪为时薪的125%。</li><li>接下来的小时的时薪为时薪的137.5%。</li><li>周末和法定假日的时薪为时薪的150%。</li></ul><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>葡萄牙雇员的年度最低工资为11,480欧元。这包括强制性的13个和14个月的工资支付。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><ol start="1"><li>雇主成本通常估计为员工工资的26.9%：<ul><li>社会保障* &#8211; 23.75%</li><li>工伤保险* &#8211; 3.15%</li><li>远程办公津贴 &#8211; 50欧元</li><li>第13个月工资 &#8211; 基本工资的1/12</li><li>第14个月工资 &#8211; 基本工资的1/12</li><li>强制性餐费津贴 &#8211; 每个工作日6欧元</li></ul></li></ol><p>&nbsp;&nbsp; 年度成本</p><ul><li>工作场所健康与安全 &#8211; 150欧元</li></ul><p>*社会保障、工伤保险政策和工伤保险费是根据员工的工资+远程办公津贴计算的。请注意，第13和第14个月的工资按月支付给员工，并包含在总年薪中。标准社会保险适用。</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p>终止必须遵守复杂的规则和员工所在国家的规定。解雇始终由雇主与主要利益相关者处理。这可能包括特定解雇案例的临时费用以及必要或建议的步骤。</p><p>在葡萄牙，解雇可能很复杂，并且必须基于绩效或冗余原因（例如，严重原因，不适合担任该角色，角色的消失）。</p><p>符合规定的终止方式包括：</p><ul><li>员工自愿解雇</li><li>协商一致</li><li>雇主根据以下情况单方面解雇：<ul><li>客观原因。</li><li>纪律解雇</li><li>不适合的工作岗位。</li></ul></li><li>合同到期</li></ul><p>&nbsp;&nbsp;员工休育婴假、怀孕或哺乳时不得终止合同。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>通知期将取决于解雇的性质、雇佣期限和合同条款。</p><ul><li>在试用期内<ul><li>如果试用期小于60天，则无需通知。</li><li>如果试用期超过60天但不超过120天，则为7天。</li><li>如果试用期超过120天，则为15天。</li></ul></li><li>因冗余或不适合工作而由雇主解雇：如果服务时间不足一年，则为15天。如果服务时间不足5年，则为30天。如果服务时间不足10年，则为60天。如果服务时间超过10年，则为75天。</li><li>员工自愿解雇：<ul><li>如果服务时间不足两年，则为30天。</li><li>如果服务时间为两年或更长，则为60天。</li></ul></li><li>有原因的终止<ul><li>通知期不是强制性的。</li></ul></li><li>固定期合同的不续签：<ul><li>雇主为15天。员工为8天。</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在葡萄牙，基于冗余或不适合工作而被解雇的员工有权获得解雇补偿。</p><ul><li>对于无固定期限的合同，解雇补偿为每年服务的14天工资，最多为基本工资的12个月。</li><li>对于固定期限合同，解雇补偿为每年服务的24天。</li></ul><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>工资单必须包含有关缴款、扣除额、工资等的全面详细信息。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工均有权每年享受22天的带薪休假（PTO）。员工在工作满6个月后有资格休年假。兼职员工享有与全职员工相同的休假。在就业的第一年，员工不得休假超过20天。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工在过去6个月向葡萄牙社会保障缴纳费用的情况下，有权获得长达1095天的带薪病假。前3天不薪酬并被视为资格期。这种假期的支付金额和支付者根据假期长度不同而不同。</p><figure class="wp-block-table"><table><tbody><tr><td>时期</td><td>支付</td><td>付款人</td></tr><tr><td>0 &#8211; 3 天</td><td>0%</td><td>不适用</td></tr><tr><td>3 &#8211; 30 天</td><td>基本工资的55%</td><td>社会保障</td></tr><tr><td>31 &#8211; 90 天</td><td>基本工资的60%</td><td>社会保障</td></tr><tr><td>91 &#8211; 365 天</td><td>基本工资的70%</td><td>社会保障</td></tr><tr><td>+365 天</td><td>基本工资的75%</td><td>社会保障</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">育儿假</h2><p>员工有权休120个工作日的育婴假。父母双方可以决定如何分配休假，应在产假后的30天内休假。任何一方都可以休育婴假。社会保障将在此期间支付80-100%的工资，具体取决于休假如何在伴侣之间分配。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p>个人所得税从14.5%到48%不等。个人所得税按照累进税率计算。多个附加因素可能影响总税率。</p><figure class="wp-block-table"><table><tbody><tr><td>应纳所得税</td><td>税率</td><td>扣除</td></tr><tr><td>不超过 €7,703</td><td>13.25%</td><td>0</td></tr><tr><td>€7,704 -€11,623</td><td>18.00%</td><td>€365.89</td></tr><tr><td>€11,624-€16.472</td><td>23.00%</td><td>€947.04</td></tr><tr><td>€16,47 -€21,321</td><td>26.00%</td><td>€1,441.14</td></tr><tr><td>€21,322-€27,146</td><td>32.75%</td><td>€2,880.47</td></tr><tr><td>€27.147-€39,791</td><td>37.00%</td><td>€4,034.17</td></tr><tr><td>€39,792-€51,997</td><td>43,50%</td><td>€6,620.43</td></tr><tr><td>€51,998-€81,199</td><td>45.00%</td><td>€7,400.21</td></tr><tr><td>超过 81,200</td><td>48.00%</td><td>€9,836.45</td></tr></tbody></table></figure><p><byte-sheet-html-origin data-id="1715850396339" data-version="4" data-is-embed="true" data-grid-line-hidden="false" data-copy-type="col" style="color: rgb(0, 0, 0); font-size: medium; white-space-collapse: collapse;"></byte-sheet-html-origin></p><h2 class="wp-block-heading has-large-font-size">社会保险</h2><ul><li>Employee Training</li><li>Social Security</li><li>Private Healthcare &#8211; Unisure (optional)</li><li>Private healthcare &#8211; AdvanceCare &amp; Generali</li><li>Compensation Fund</li><li>Labor Accident Insurance</li><li>Private Healthcare &#8211; Allianz (optional)</li></ul><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Easter Sunday</td><td>3.31</td></tr><tr><td>Liberation Day</td><td>4.25</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Corpus Christi</td><td>5.30</td></tr><tr><td>National Day</td><td>6.10</td></tr><tr><td>Assumption Day</td><td>8.15</td></tr><tr><td>Republic Day</td><td>10.5</td></tr><tr><td>All Saints&#8217; Day</td><td>11.1</td></tr><tr><td>Independence Restoration Day</td><td>12.1</td></tr><tr><td>Immaculate Conception</td><td>12.8</td></tr><tr><td>Chritmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>