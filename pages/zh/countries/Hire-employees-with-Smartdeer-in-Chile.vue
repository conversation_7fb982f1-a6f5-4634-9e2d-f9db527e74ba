<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在智利雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在智利雇佣',
  ogDescription: '基础信息 首都：圣地亚哥 Santiago 时区：GMT-4 语言：西班牙语 货币代码：CLP 人力资源制度概 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：圣地亚哥 Santiago 时区：GMT-4 语言：西班牙语 货币代码：CLP 人力资源制度概 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在智利雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在智利雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/R-C-1-4.jpeg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/t01e463ad17f2377e21.png';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：圣地亚哥 Santiago</p><p>时区：GMT-4</p><p>语言：西班牙语</p><p>货币代码：CLP</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p>合同必须用西班牙语撰写，也可以是双语。合同必须书面并由双方签署。合同必须包括：</p><ul><li>姓名</li><li>入职日期</li><li>雇佣期限</li><li>职位描述</li><li>远程工作条件</li><li>断开连接条件</li><li>解雇条件</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>在智利，没有试用期。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天9小时，每周45小时，工作日为周一至周五。</p><p>根据智利劳动法规定，员工每周工作超过45小时的部分需要支付加班费，加班费的计算方式为基本工资的50%。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>智利全职员工的国家最低工资为CLP 440,000。</p><p>法律规定在2023年和2024年逐步增加，将最低工资提高到CLP 500,000。作为这一变化的一部分，2023年9月1日，最低工资将增加到CLP 460,000，最终于2024年7月1日增至CLP 500,000。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计为员工工资的4.91%。</p><ul><li>法定利润分享 &#8211; CLP 182,092</li><li>在家工作津贴 &#8211; CLP 32,000</li><li>人寿保险 &#8211; 1.58%*</li><li>工伤保险 &#8211; 0.93%，最高工资限额为CLP 2,941,704.48，包括法定利润分享</li><li>失业保险 &#8211; 2.4%**<br>*人寿保险计算的最高工资为CLP 2,941,704.48，包括法定利润分享，失业保险计算的最高工资为CLP 4,419,766.78，包括法定利润分享<br>**直至最高工资限额为CLP 4,419,766.78（包括法定利润分享）</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p><em>解雇必须遵守复杂的规定和员工所在国家的规定。解雇始终由雇主与主要利益相关者处理，可能包括特定解雇案例的特定费用或必要的建议步骤。</em></p><p>在智利，解雇可能会很复杂。雇主不能随意解雇员工，必须以公司相关业务需要为依据。</p><p>符合规定的解雇方式包括：</p><ul><li>员工自愿离职</li><li>双方达成协议</li><li>雇主基于以下原因单方面解雇：<ul><li>试用期</li><li>客观原因</li><li>纪律解雇</li></ul></li><li>合同到期</li></ul><p>结束后，雇主必须书面确认不会续签固定期合同。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通知期为30天，雇主也可以提供代替支付。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">在智利，因普遍原因或未提前通知而被解雇的所有员工都有权获得解雇补偿。解雇补偿为服务年限每年30天的工资，最高为330天的基础薪酬。</span><div data-page-id="B3L0djkIIoqJO4xvVL0ccRhenrc" data-docx-has-block-data="false"><div class="ace-line ace-line old-record-id-ZYgSd3aHpoiAvcxyq3OczH4Wn1c">每月的解雇补偿不得超过最高限额90 UF（约为3,500美元），除非员工未经通知被解雇。</div></div><span data-lark-record-data="{&quot;isCut&quot;:false,&quot;rootId&quot;:&quot;B3L0djkIIoqJO4xvVL0ccRhenrc&quot;,&quot;parentId&quot;:&quot;B3L0djkIIoqJO4xvVL0ccRhenrc&quot;,&quot;blockIds&quot;:[185,186],&quot;recordIds&quot;:[&quot;XGnDdyQOuoiGpux9PtEc7CAQnD7&quot;,&quot;ZYgSd3aHpoiAvcxyq3OczH4Wn1c&quot;],&quot;recordMap&quot;:{&quot;XGnDdyQOuoiGpux9PtEc7CAQnD7&quot;:{&quot;id&quot;:&quot;XGnDdyQOuoiGpux9PtEc7CAQnD7&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;B3L0djkIIoqJO4xvVL0ccRhenrc&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7341208408357748740&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;在智利，因普遍原因或未提前通知而被解雇的所有员工都有权获得解雇补偿。解雇补偿为服务年限每年30天的工资，最高为330天的基础薪酬。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+1t&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7341208408357748740&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;ZYgSd3aHpoiAvcxyq3OczH4Wn1c&quot;:{&quot;id&quot;:&quot;ZYgSd3aHpoiAvcxyq3OczH4Wn1c&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;text&quot;,&quot;parent_id&quot;:&quot;B3L0djkIIoqJO4xvVL0ccRhenrc&quot;,&quot;comments&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7341208408357748740&quot;,&quot;children&quot;:[],&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;每月的解雇补偿不得超过最高限额90 UF（约为3,500美元），除非员工未经通知被解雇。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+18&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7341208408357748740&quot;]},&quot;nextNum&quot;:1}},&quot;align&quot;:&quot;&quot;,&quot;folded&quot;:false}},&quot;B3L0djkIIoqJO4xvVL0ccRhenrc&quot;:{&quot;id&quot;:&quot;B3L0djkIIoqJO4xvVL0ccRhenrc&quot;,&quot;snapshot&quot;:{&quot;type&quot;:&quot;page&quot;,&quot;parent_id&quot;:&quot;&quot;,&quot;comments&quot;:[],&quot;revisions&quot;:[],&quot;locked&quot;:false,&quot;hidden&quot;:false,&quot;author&quot;:&quot;7129372845536198657&quot;,&quot;children&quot;:[&quot;doxcn8EkoSh3REvDMzv1QgGXRCc&quot;,&quot;doxcnlMvQbez9dOiaOaMOaxWr5c&quot;,&quot;doxcnU2CxFpwVT3A1pszV0ohrwb&quot;,&quot;doxcnHtIeNb9Som3EB7iEs53eSe&quot;,&quot;doxcns9cyjiAY6c41nx4TMSlMlf&quot;,&quot;doxcnismIZelWuBkq2zm3lWLSzf&quot;,&quot;doxcnHLRPr0Le2etmyNNYvDBZob&quot;,&quot;doxcnqRnxnbW89h2uKbx80q8Xiq&quot;,&quot;VyWZdbaK7ob19jx7jjrcQel4nvc&quot;,&quot;PuEbd0F9uo61xmxLZXIcGVzknnb&quot;,&quot;W4McdiBWronIEuxxF8dc39Gcnfg&quot;,&quot;ZlpDdlrubowNEuxgnVGcytk8nJg&quot;,&quot;Lojjd8C8noOKBRxe0fRcunjpnD3&quot;,&quot;MnHvdTDytoqlirxHkrHcTVxdnZd&quot;,&quot;GAMjdfkzrobFgBxntqrc772Cntn&quot;,&quot;G7H0d8MxtoWdWSxht1UcsUuon7b&quot;,&quot;doxcnc5TwOF1EO90QZOgBBcyhYc&quot;,&quot;HEZ1dnwLeo1AjUxz3kccjXkVnRf&quot;,&quot;J7icdLcQQoi4MFx15vScQNhmnzX&quot;,&quot;CJW8dLtbWoOWoaxd2aUcRWFCnvb&quot;,&quot;Mo2rdYxqPo3uK0xAstTcelFNnne&quot;,&quot;AHpBdBWKUoDoTtxhbeOc99oPn2g&quot;,&quot;AYYWdcVZvoIfuKxZXXGcL580nBg&quot;,&quot;Dxtnddl8voqTwExBZz5cvjnynzh&quot;,&quot;J3iTdKcxioZR7mxKIyHczl3snNg&quot;,&quot;J2PMdnUh0oBtenxQB4nc0tVgnoa&quot;,&quot;ZjxSdTOEaoOl0AxUQJnc2btOnHb&quot;,&quot;H83PdQDztobPOaxJVT0chrCCnqd&quot;,&quot;NA27d5s5soyjYIxbmGscpZjNnLg&quot;,&quot;EZ5JdG3gPo6D4Dxjo7BcXOpZngb&quot;,&quot;H8Q9d5R55og0XaxGwHOcJNHdnov&quot;,&quot;E5RXdophRodsh6x3dQCcZU5HnFX&quot;,&quot;Lhshd4yrqoGRrTxXKuBcEFVnn2b&quot;,&quot;KS4FdTUtJoIovux67vlcrlhInZc&quot;,&quot;doxcncd5E9zuym3s7Jiw14uZjMf&quot;,&quot;AtZLdg1b1oA0caxUI7ZckNhJnso&quot;,&quot;V0XSdzZKYodoo8xSGg2cadAonXz&quot;,&quot;PBLTdj4XtoiuuHxPKGScaNhFn5e&quot;,&quot;F8o5dfhWJoJNDAxDJLfcODcKnRb&quot;,&quot;Kqizd9Cijoz68Ox5iEFckyhUnCg&quot;,&quot;TUgjdZXXgoJEMwxXZgQc79o9nfc&quot;,&quot;XSwgdTToiozh85xnPR9cqjuhnYe&quot;,&quot;M7m0dmI2doUPbQxAvHQcGpmjnYc&quot;,&quot;ELO9dfHfloXxUHx0krVci8gjnJf&quot;,&quot;TFwsdyqx7ohah4xu3a5cwHfZnpb&quot;,&quot;YA0mdZ8HNoNBV9xZq49c73B7nee&quot;,&quot;LUdtd3gfbo28IOxaxUocpEz5nxc&quot;,&quot;FmLLdL2KaoX6Axx2P9ncQmUynBh&quot;,&quot;XIsydMlHuo5vxnxmStmcJ43mnkg&quot;,&quot;UmTZdVp3Vo4R2rxQmGTcrfBinmg&quot;,&quot;AMHkd6AODor9ghxNojmcdogFnXf&quot;,&quot;JycudgQkDoZHjYxRACCcUjzOnAL&quot;,&quot;MQL6d9y4ro8Ad0xRZs7c1nXNnmb&quot;,&quot;D7ovdKWB0o1glqxEQpocNwivnGt&quot;,&quot;SNzNd2ULhow2uVxHRSGc9kT3nCK&quot;,&quot;BthwdbV5RomFl3xLxKbcgMtxnXd&quot;,&quot;GY3tddZMjoqfWUxOHP9cFu0nn1d&quot;,&quot;F15mdB7Z7oT3sBx85vucXhNnnBh&quot;,&quot;doxcn50lMOiIg02l8tJFAp9DxSd&quot;,&quot;SZANdZLRToF2KrxYpDUcUhiJnrd&quot;,&quot;SlgAdwG5Fo4H6Jx6B1JcrZEonhh&quot;,&quot;ROkedD18woi63ZxOS9ucO86eneg&quot;,&quot;Jac2dskWdo3PYVxT3JecpCBMn8f&quot;,&quot;P9KhdhBZioCRX2xLIJZc3ETnneg&quot;,&quot;P6FRdo6RDoWLIIx24StcdWdfnub&quot;,&quot;S60rdqhbQoBvFixmbpIco6WCnXb&quot;,&quot;LjRcdlqptoFKJxxH42bcnHiGnJf&quot;,&quot;FMI6dquXioKtouxYnpucx0g6nxb&quot;,&quot;EPMIdXxQfordLhxyNuWcxGXInOe&quot;,&quot;S9HHdst1ho5mMkx8sLvccYnrnxd&quot;,&quot;SBnjdC33xolvY5xLjTNciSncniw&quot;,&quot;Cu8hdnIH2oap6gxv4Fvckq3vnih&quot;,&quot;CV9hdFc2Lo4vTrx3Wgvcyg7nns1&quot;,&quot;doxcniJh9YpkyoB1zT36Os91Foc&quot;,&quot;JOQqdvQJLoNIFDxSwIxcb7sLn5v&quot;,&quot;CGgwdkolnojGKJxli37cS5D4nEg&quot;,&quot;Fvtyd1x2FowkL2xjDOBctASZnMe&quot;,&quot;JCXBdtOMCoLsEHxQOR2ckWy3nhc&quot;,&quot;UlKtdepiLoBdPCxxx3kc7qIUnYg&quot;,&quot;PCCVdHB2poG4xHxfzatcdQFEniz&quot;,&quot;CEH1drNUXoSYETxdRYgcPykLnXf&quot;,&quot;HD0qd42hvo4Zi4xLgezcbRW5nob&quot;,&quot;WeA9dNAdJoJKrMxDHkFcX3uqnCg&quot;,&quot;ZWXVdcxA0o9TzDxX7EIcGdK8nRf&quot;,&quot;AFPtd3GXwoIt5uxwQJRcjf33ntf&quot;,&quot;WzjJdG4dLoUNLdx4pP9cviFWnFc&quot;,&quot;BYCnd3RPDoOU7rxwr6lc1glMnzi&quot;,&quot;PiEjdZ9MLoF0W2xMdbecBwp7nYe&quot;,&quot;JJFVdjV0coIUGUxHbHUcL2mnnUg&quot;,&quot;LtiZdov3woIznYxXpsYcAVhQnZe&quot;,&quot;PHcMdXgTSoyYxnxqERVcAxOVn9b&quot;,&quot;TQZndvvMPoFFZCxrXXhcFFU7nxd&quot;,&quot;AuhZdX5ctoYEFUxtq4vc9Ut4nEb&quot;,&quot;Du3PdVl6KoasQmxelcQcHP0Unpf&quot;,&quot;KfZOdTyUwoTwl5xop66cN2yxnue&quot;,&quot;doxcngzhT713D7JNxIfgtRvmGNf&quot;,&quot;ZxlsdZjODoTTg9xsJHrcbD8jnzh&quot;,&quot;AfiAdWTM3oWywtx9Oxacp2gJnFh&quot;,&quot;doxcn0YXvkuyLW9TxeqzdDjZ4fC&quot;,&quot;XSZMdmYAGobHx4xNPEycNxSvnIs&quot;,&quot;Z2nndQgqKoT75wxvNjKcQXs4ngb&quot;,&quot;BG5WdaisCoOgcixiGJsco1f4n7l&quot;,&quot;HFMCdiv2Kou1dtxGkLAcLOTgnQe&quot;,&quot;JfzRdmbKEorFoGxjLArcCZcwnld&quot;,&quot;UZZvdMvauowYxaxXhfJcnDhcngd&quot;,&quot;IYVWdmSTNoMuM0xD3yUcbxHfnnd&quot;,&quot;RGTgd3gR2oXhlvxVDgWc8Gmgnxd&quot;,&quot;HWEcdMMFdo0y9wxqex8cylbUnUf&quot;,&quot;O9nXdKK4MotFYlxXLnccvrEZnFE&quot;,&quot;RDRldNa0LoRMqGxXJHAc1ikWntd&quot;,&quot;USvOdBtBwoGVK0xZX0XcWIgSnve&quot;,&quot;FNXTd3Qn1oCIgNxbq9Lcjgo5npf&quot;,&quot;HM8xdiBUMoxKj4xQmrncylZhnod&quot;,&quot;WdPgdDWo1oifdLxlP4qcUYUNn2e&quot;,&quot;NlsydvoQpoPGS4x2N40clZ8snDd&quot;,&quot;G2tOdK9oeoSvkqxoitEclP3inSf&quot;,&quot;ExnBdsAJuoldoXxerUFcDxQbnMf&quot;,&quot;ONkhd2QSYo5UYBx312lcsvcenme&quot;,&quot;YkEmdhqfxoW6kAxsViIcK7zXnWe&quot;,&quot;WW7ZddPDNo2DIMxLphZcjCpEnnb&quot;,&quot;NX7wdkJHuof4h4xOlFUcGVTLnse&quot;,&quot;XBJXd2v54oRLkOxqkPGcgGugnwf&quot;,&quot;WX8edq99GomNHTx0HSacbJ8rnwg&quot;,&quot;JOQLdiW9Ho2Vo1xH9OLcRUyfnRh&quot;,&quot;Dy3Vd8Ib9oPw6extiUMcIGPZnhb&quot;,&quot;QyVudfefeoYrbcxuuNhceG8qn6e&quot;,&quot;MWH4db5bQoBIpdxTjsBcPFM5nrg&quot;,&quot;ScfNdEX4XoIzeLxuBTlcIJWbnFg&quot;,&quot;EGCVd8GcloCPz5x8W6hc2w3Rnmf&quot;,&quot;Nc1vdlNO3oYXlTxvtyhcivh5nQf&quot;,&quot;Rw5adERahoZNd6xfaYbcda47npd&quot;,&quot;HmtVdBVP1oaew2x7vhKcd7Jqn9c&quot;,&quot;PjjAdD3gNoH67qxR5ufcHOYAniT&quot;,&quot;NrCUdMhyHog8qOxVcMGcngcJnLg&quot;,&quot;PvBOdj8CLoTYVjxzo6YcQ241njh&quot;,&quot;Pp86dDWxyoNqJIx3VRrcX2xqnCb&quot;,&quot;VEYydTHHEokh9cxDGircbmTunph&quot;,&quot;Sd8BdAn5AoIRVHxgwlmcHz3fnjd&quot;,&quot;LvsAdt7YZoRZIOxJULmc2hCmnfS&quot;,&quot;Ip4pd48AgonwaJxn2MEcZpHEnzg&quot;,&quot;ENZsd9aUVoIh5RxG9wmcghv0nOb&quot;,&quot;Mx0edaFoWorVZOxB6Zdc2qdBnEc&quot;,&quot;DprBdrkBRohAKbxLQrucabvInjf&quot;,&quot;LBXudDSRnouP74xmWX6c2meQnnh&quot;,&quot;AerodbyneowMhzxLKphcFkoLnIf&quot;,&quot;Fa8yd5eX8oSePQxlyx7csMnLnBd&quot;,&quot;U8DxdU5b0oKR9GxyedMc4JMUnee&quot;,&quot;LS5edFgPLoeMlbxOi3YcYEbznFc&quot;,&quot;TVwhdwocTovJPvxcJzGcM8QEnRb&quot;,&quot;IeT7dSMutofDzGx5pl9capuUnkd&quot;,&quot;QTondZki9o1mW5x4yYVc245fnUh&quot;,&quot;KKNudkdWuoRq1mxtbukci7aZnsf&quot;,&quot;TGnmd1LHGovPqCxcP6XcumRPnSe&quot;,&quot;QLZ2d6SHxoHDvgxXsHTcSVy6npg&quot;,&quot;YLOBdJroaoX1i7x7KAcch4gBnNe&quot;,&quot;IpWvdZ9vmoLQirxYxAccj5eDnsc&quot;,&quot;QufgdEUsHoI92nxtPnqcQYk7nTh&quot;,&quot;Vg6vdkA8aoPF9hx9q7ucm5lqnQf&quot;,&quot;FnkjdNFtloZ3DsxrTOxcuEgXnKg&quot;,&quot;TPi7dT0IKoRo02xTdhXcKEWrnLc&quot;,&quot;RzL4d91Jfo4055xJm1lcBu68nSg&quot;,&quot;GzEJd6g5AoXSFXxJihVcEbCbnAg&quot;,&quot;FGOGdWbx1oISWJxUFXActxCCnwh&quot;,&quot;B6tVdOSotoeANWxZO0OcXTdGnwc&quot;,&quot;KgKudnuH1o8jU3xtEnfcdJoQnHb&quot;,&quot;E2EHd8WV6obzTjxpdg4cYIyznae&quot;,&quot;Ddq2dJAmRopYhHx40xUcQSg0ngb&quot;,&quot;LIOtdpeY4ogxPex4Kj7cFljInec&quot;,&quot;QLO7dXWsaoW3nyxvlrecEJlUnRg&quot;,&quot;PXpEdgYbAoge32xXAtUcrrm4nsh&quot;,&quot;Nr4mdiRnPoVYIcxUKWqcm7OOnxb&quot;,&quot;LQjFd7oVJoDu5rxNa9NcNltQnyd&quot;,&quot;GGwgdt642odYjbxqbS1cN3ben2A&quot;,&quot;ZfTxd2ozronlgXxPMpxcjrEZnef&quot;,&quot;VvRxdk25voIoKyxw8YDcg2kUnff&quot;,&quot;J3Fud5DynoQGzexqC1OcpGvMnce&quot;,&quot;DrSEdX3HAoY3hUxProvc9ZXxn2f&quot;,&quot;CoUXdaKrEoLpnZxBfOGcuVVPnye&quot;,&quot;WrdydeuU9on9RExUsJNct4WRn2n&quot;,&quot;ZdOGdRDFDoUUECxB3DfclDHcnuh&quot;,&quot;NZMIdUH16oaYHGxLcNAcKqtlnfd&quot;,&quot;YwD9dXiHboOG2ixPIEWc2wx7nhg&quot;,&quot;VdBhdEdSfoRMy9x3SJjcZbkRnrg&quot;,&quot;Hev9dnR6nory1PxLNbXc6kzBnTh&quot;,&quot;XGnDdyQOuoiGpux9PtEc7CAQnD7&quot;,&quot;ZYgSd3aHpoiAvcxyq3OczH4Wn1c&quot;,&quot;KZmFd59J0oX4t1xKYUqcYmBxn2d&quot;,&quot;NaFVd8foXoqYePxNXQYcrNjHnjd&quot;,&quot;R9ZHdEe23oc3G4xbdvvcSLCwn3b&quot;,&quot;AN02d7wLwoafd6xDc8BcpOlRnNf&quot;,&quot;P9aAd00LhoL2z1xdPRWcRailnIe&quot;,&quot;RyeEdbat4oeh6fxvTBmcWgEfnjf&quot;,&quot;C4rEdx4aDopJuSx2HxocVB2dnud&quot;,&quot;FUh6dm1YGodo97xoO1jckhhwn6f&quot;,&quot;LfqLd92eboxblax8IXOcDQ4nnUg&quot;,&quot;VcRIdPv53oi7w2xUtficHAtunc1&quot;,&quot;PbiEdZLU8oktwSxkidCcQVtCnug&quot;,&quot;Prw3dLaYMogyG2xiorScmBqEnOf&quot;,&quot;VRc3d5b4doI0taxvw6icCLuKn3d&quot;,&quot;IoK2dhesPoaT6fxi1n1cMKrBnMc&quot;,&quot;YDeJduHZWouW6nxIwH8ctxKPnQe&quot;,&quot;EWukdw9AqonbwXxGAj9ci1O0nnh&quot;,&quot;Sj8Hdq1cBolGTzxCNA4cB7EKnHg&quot;,&quot;KPDBdz7NtohinyxwnJmc7WlbnEb&quot;,&quot;SCp1dTXd4ovPHkxznWVcB2fBnbf&quot;,&quot;NdijdJDZIoQThxxzVchcqyW0nfd&quot;,&quot;TrgQdpPq1olJ57xyEb6cTpl0nlc&quot;,&quot;XMFqdcR41oSpHSxTBTMcfY0hneN&quot;,&quot;BO1IdP86VoXKTYxJfWacZ5zXngZ&quot;,&quot;Z8vRdU7Q1oVfVuxfkmjcVgWSn3f&quot;,&quot;QjoNdkwSZoUqRRxpIJSc4LDAndc&quot;,&quot;HuhDdf4xsoBCcex31mQcYnFOnNb&quot;,&quot;EUOodHXNio1bHaxEI1yc28NUnjg&quot;,&quot;IOFudZ7AfopWqOxcTnfccbCPnoc&quot;,&quot;F0WldcnKVoPOjmxLqJtcE6VTnmg&quot;,&quot;NslPd5e8Vo7J7QxyLlscgx1dn5f&quot;,&quot;CW1HdAGNHoyTgOxCbklcldN4n7f&quot;,&quot;B6ZBdrMMHoMiBJxFGiBcJuSgnJb&quot;,&quot;JxcBdLPPXoITq1xZedfcqYzungb&quot;,&quot;JycXdI6CYoJluSxUE9wcavTBn5d&quot;,&quot;LKcVd3YXYodJ8MxwjNscNLuwnLc&quot;,&quot;Yut5d1pUBoYjIgxC6WrcdX2Nnof&quot;,&quot;KQ8tdKeF6oft2JxxH5lcrWznnNe&quot;,&quot;GbzPdYRPooOdvyxwKb2cdEiznuc&quot;],&quot;text&quot;:{&quot;apool&quot;:{&quot;nextNum&quot;:1,&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7129372845536198657&quot;]}},&quot;initialAttributedTexts&quot;:{&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+8&quot;},&quot;text&quot;:{&quot;0&quot;:&quot;智利 Chile&quot;}}},&quot;align&quot;:&quot;&quot;,&quot;doc_info&quot;:{&quot;editors&quot;:[&quot;7129372845536198657&quot;,&quot;7321550560460013571&quot;,&quot;7341208408357748740&quot;,&quot;7365790118063964164&quot;],&quot;options&quot;:[&quot;editors&quot;,&quot;create_time&quot;],&quot;deleted_editors&quot;:[],&quot;option_modified&quot;:null}}}},&quot;payloadMap&quot;:{&quot;XGnDdyQOuoiGpux9PtEc7CAQnD7&quot;:{&quot;level&quot;:1},&quot;ZYgSd3aHpoiAvcxyq3OczH4Wn1c&quot;:{&quot;level&quot;:1}},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:185,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:65},&quot;recordId&quot;:&quot;XGnDdyQOuoiGpux9PtEc7CAQnD7&quot;},{&quot;id&quot;:186,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:44},&quot;recordId&quot;:&quot;ZYgSd3aHpoiAvcxyq3OczH4Wn1c&quot;}],&quot;pasteFlag&quot;:&quot;3aba3661-0eda-4f7c-a6cf-c94c0dd10c7f&quot;}" data-lark-record-format="docx/record" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>无明确规定</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工均有权利每年休假15个工作日。休假每月积累1.25天。员工有资格根据雇主和员工的共识逐渐获得年假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工有权利享受无限制的有薪病假。<br>雇主不负责支付病假工资，而是由员工所在的健康机构（ISAPRE或FONASA）支付。智利的医疗体系要求在补贴病假之前要有6个月的缴费记录。<br>病假支付如下：</p><ul><li>1-3天：员工工资不支付，医疗体系仅支付员工的社会法规缴费。</li><li>4-10天：医疗体系支付员工正常工资和员工的社会法规缴费，扣除3天。</li><li>11天以上：医疗体系支付员工正常工资和员工的社会法规缴费。<br>员工的工资按照过去3个月平均每日工资计算，最高限额为86UF每月。<br>医生通过iMed提交医疗证明，该系统会通知雇主。雇主有3天的时间以电子方式处理这段请假。如果文档无法通过iMed提交，员工有48小时的时间将文档提交给雇主。</li></ul><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>为社保缴纳了6个月的怀孕员工有权休126天的带薪产假，分为两部分。其中42天必须在孩子出生前休息（产前假），其余84天必须在孩子出生后休息（产后假）。</p><p>此假期以员工正常工资的100%支付，社保负责支付。</p><p>此假可以通过育婴假延长。</p><p>母亲在休完产假后有权继续育婴假，她可以选择以下其中一项：</p><ol start="1"><li>附加84天全职休假。</li><li>附加144天半日休假。在这种情况下，员工只工作半天，其余半天用于照顾孩子。</li><li>附加84天与孩子的父亲一起分享。员工必须在产后假期结束日前至少30天通知雇主。</li></ol><p>陪产假不需要医疗证明，完全由员工决定，雇主不能影响。</p><p>这段假期由医疗系统支付，支付金额为员工正常工资的100%。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p>个人所得税为0%至40%，按照渐进税率计算。非居民可能需要支付额外的15%的固定税率。</p><figure class="wp-block-table"><table><tbody><tr><td>年度总收入</td><td>税率(%)</td></tr><tr><td>少于854,050.50智利比索</td><td>0%</td></tr><tr><td>854,050.51-1,897,890.00智利比索</td><td>4%</td></tr><tr><td>1,897,890.01-3,163,150.00智利比索</td><td>8%</td></tr><tr><td>3,163,150.01 &#8211; 4,428,410.00智利比索</td><td>13.50%</td></tr><tr><td>4,428,410.01 &#8211; 5,693,670.00智利比索</td><td>23%</td></tr><tr><td>5,693,670.01 &#8211; 7,591,560.00智利比索</td><td>30.40%</td></tr><tr><td>7,591,560.01 &#8211; 19,611,530.00智利比索</td><td>35.50%</td></tr><tr><td>超过19,611,530.01智利比索</td><td>40%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">社会保险</h2><ul><li>健康保险</li><li>人寿保险</li><li>养老基金</li><li>利润分享奖金</li><li>失业保险</li><li>工伤保险</li><li>私人医疗保险 &#8211; Unisure（可选）</li><li>私人医疗保险 &#8211; 安联（可选）</li></ul><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Easter Saturday </td><td>3.30</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Navy Day</td><td>5.21</td></tr><tr><td>Primary Elections Day</td><td>6.9</td></tr><tr><td>National Day of Indigenous Peoples</td><td>6.20</td></tr><tr><td>Feast of St Peter and St Paul</td><td>6.29</td></tr><tr><td>Our Lady of Mount Carmel</td><td>7.16</td></tr><tr><td>Assumption Day</td><td>8.15</td></tr><tr><td>Independence Day</td><td>8.18</td></tr><tr><td>Army Day</td><td>9.19</td></tr><tr><td>Added National Holiday</td><td>9.20</td></tr><tr><td>Meeting of Two Worlds</td><td>10.12</td></tr><tr><td>Municipal Elections Day</td><td>10.27</td></tr><tr><td>Reformation Day</td><td>10.31</td></tr><tr><td>All Saints&#8217; Day</td><td>11.1</td></tr><tr><td>Immaculate Conception</td><td>12.8</td></tr><tr><td>Battle of Ayacucho</td><td>12.9</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>