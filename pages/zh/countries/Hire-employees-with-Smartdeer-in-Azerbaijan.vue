<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在阿塞拜疆雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在阿塞拜疆雇佣',
  ogDescription: '基础信息 首都：巴库 Baku 时区：GMT+4 语言：阿塞拜疆语 货币代码：AZN 人力资源制度概况 雇佣合 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：巴库 Baku 时区：GMT+4 语言：阿塞拜疆语 货币代码：AZN 人力资源制度概况 雇佣合 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在阿塞拜疆雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在阿塞拜疆雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/阿塞拜疆FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/阿塞拜疆Flag.jpg';
const htmlContent = '<p>基础信息</p><p>首都：巴库 Baku</p><p>时区：GMT+4</p><p>语言：阿塞拜疆语</p><p>货币代码：AZN</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>合同可以是双语的。它们必须是书面形式，并由双方签署。</p><p>合同必须包括：</p><ul><li>姓名</li><li>开始日期</li><li>雇佣期限</li><li>职位描述</li><li>终止条件</li><li>工资</li><li>工作时间</li><li>假期</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>标准试用期为90天。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工时为每天8小时，每周40小时。标准工作周从星期一到星期五。</p><p>加班费是强制性的，不包括在工资内，额外支付在月薪之上。超出标准工时的时间被视为加班。员工每天最多可以工作2小时的加班。对于额外的工时，员工将获得每小时工资的200%。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月300阿塞拜疆马纳特。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计为员工薪水的18.9%。</p><p>+一次性强制湿墨雇佣协议处理费 &#8211; 80美元</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>合同可以是双语的。它们必须是书面形式，并由双方签署。</p><p>合同必须包括：</p><ul><li>姓名</li><li>开始日期</li><li>雇佣期限</li><li>职位描述</li><li>终止条件</li><li>工资</li><li>工作时间</li><li>假期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通告期为30天，将根据雇佣的时间长度增加。</p><ul><li>服务时间不满2年时为45天</li><li>服务时间不满5年时为60天</li><li>服务时间不满15年时为90天</li><li>服务时间不满25年时为120天</li><li>服务时间超过25年时为150天</li></ul><p>如果员工辞职，无论服务时间多长，他们还必须提前一个月通知。但是，双方可以在协议中达成更多协议。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>离职费将取决于服务时间和解雇类型。</p><ul><li>冗余：90天工资</li><li>标准解雇：服务不满一年的员工为30天工资。</li><li>标准解雇：服务在1-5年之间的员工为120天工资。</li><li>标准解雇：服务在5-10年之间的员工为210天工资。</li><li>标准解雇：服务时间超过10年的员工为60天工资。</li></ul><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工均有权每年获得21个工作日的带薪休假（PTO）。PTO和每小时费率因服务时间而异。员工在工作满1个月后有资格获得年假。某些分类的员工只有资格获得15个工作日的PTO。</p><p>服务时间是员工工作的资历，与雇主无关。服务时间由社会保障部门确认，并作为员工的工作历史提交。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>阿塞拜疆的病假制度较为复杂。至少工作了六个月的员工有权获得最长365天的带薪病假。这种休假按服务时间、生病时间和病症类型的不同，由不同支付者以不同费率支付。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>连续工作六个月的员工有权获得126天的带薪休假。在孩子出生前必须休假70天。在此期间，员工将获得过去12个月的平均工资的100%，社会保障将负责支付此薪水。</p><p>在阿塞拜疆，育婴假是复杂的。员工在不同情况下有权获得不同程度的假期：</p><ul><li>拥有16岁以下2个以上子女的母亲有权每年获得额外2天带薪休假。</li><li>拥有16岁以下3个或3个以上子女的母亲有权每年获得额外5天带薪休假。</li><li>拥有16岁以下子女的母亲或单亲父亲有权获得14天的无薪休假。</li><li>收养2个月以下婴儿的女性有权从社会保障处获得56天的部分带薪休假。</li></ul><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税适用于0%至14%的渐进税率。</p><h2 class="wp-block-heading has-large-font-size">社会保险</h2><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>