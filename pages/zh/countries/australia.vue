<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .contries
    .contries-banner
      .banner
        figure
          img(src="~/assets/images/countries/australia_banner.webp")
      .flag
        figure
          img(src="~/assets/images/countries/australia.png")
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
    
    .contries-content
      h1 澳大利亚名义雇主服务
      .desc 使用SmartDeer在澳大利亚开展雇佣服务
      
      h3 薪酬福利
      .list
        .list-item 工资
          .list-item 从2022年7月1日起，全国最低工资为每小时21.38美元或每周38小时812.6美元（税前）。
          .list-item 国家最低工资覆盖的临时雇员也获得至少25%的临时负担。
        .list-item 工作时间
          .list-item 标准工作时间为每天 7.6 小时，每周 38 小时（每年 1967 小时）。标准工作周是从周一到周五。
        .list-item 公众假期
          .list-item 假日名称和持续时间
            .list-item.wrap   全职和兼职员工每年都有权享受20天的带薪休假（PTO）。PTO每月累积1.67天。员工在获得足够的假期后，有资格享受年假。
          .list-item 年假
            .list-item   全职和兼职员工根据他们的正常工作时间获得4周的年假。
          .list-item 病假
            .list-item   雇员有权享受长达10天的带薪病假。在此期间，雇员可领取由雇主支付的正常工资。
          .list-item 产假
            .list-item   工作满12个月的怀孕雇员有权享受18周的带薪休假。剩余的日子可以分为至少一周的时间。在此期间，雇员将领取国家最低工资，社会保障局将负责支付这一工资。
            .list-item   雇员可以通过育儿假将无薪假延长24个月。
          .list-item 陪产假
            .list-item   工作满12个月的员工有权享受两周的带薪陪产假。在此期间，员工将获得国家最低工资，社会保障部门将负责支付这一工资。
          .list-item 家庭假
            .list-item   工作满12个月的雇员有权享受两周的带薪陪产假。在此期间，雇员将获得国家最低工资，社会保障局将负责支付这一工资。
          .list-item 其他类型的福利
            .list-item   员工每年有权享受10天带薪休假
            .list-item   丧假2天
          .list-item 社会保障
            .list-item   社会保障
            .list-item   养恤基金
            .list-item   公共卫生
            .list-item   工人赔偿保险
            .list-item   保险
            .list-item   医疗保健
      
      h3 税收
      .list
        .list-item 雇主费率
          .list-item 医疗保险1%
          .list-item 退休金（退休养老金福利基金）10%（上限为每年21,002.06澳元）
          .list-item 工资税4.85%
          .list-item 员工总成本15.85%
        .list-item 个人所得税
          .list-item 所得税税率基于收入等级，居民与非居民不同。
            .list-item   0%：AUD0-AUD18,200
            .list-item   19%：18,201澳元37,000澳元
            .list-item   32.5%：37,001澳元90,000澳元
            .list-item   37%：90,001澳元180,000澳元
            .list-item   45%：180,001澳元及以上
          .list-item VAT澳大利亚的标准VAT税率是10%的商品和服务税（GST）
      
      h3 解雇
      .list
        .list-item 终止要求
          .list-item 澳大利亚的终止可能很复杂。在澳大利亚，试用期外的雇主不能随意解雇，解雇必须出于正当理由。
          .list-item 合规终止包括：
            .list-item   员工自愿
            .list-item   经双方同意
          .list-item 雇主单方面基于：
            .list-item   试用期
            .list-item   客观依据
            .list-item   纪律解雇
            .list-item   由于不适合工作而导致的表现
        .list-item 通知期
          .list-item 最短通知期为 1 周，并会根据雇佣时间的长短而增加。
            .list-item   工作时间少于 1 年 - 提前 1 周通知
            .list-item   1 - 3 年的工作 - 提前 2 周通知
            .list-item   工作 3 - 5 年 - 提前 3 周通知
            .list-item   工作 5 年 - 提前 4 周通知
          .list-item 如果雇员年龄超过 45 岁并且为同一雇主工作了 2 年，他们将有权获得额外一周的通知。
        .list-item 员工遣散费
          .list-item 遣散费将取决于员工在公司工作的年限。
            .list-item   在公司工作不到1年不领取遣散费。
            .list-item   1-2 年 = 4 周的遣散费。
            .list-item   2-3 年 = 6 周的遣散费。
            .list-item   3-4 年 = 7 周的遣散费。
            .list-item   4-5 年 = 8 周的遣散费。
            .list-item   5-6 年 = 10 周的遣散费。
            .list-item   6-7 年 = 11 周的遣散费。
            .list-item   7-8 年 = 13 周的遣散费。
            .list-item   8-9 年 = 14 周的遣散费。
            .list-item   超过 10 年 = 16 周的遣散费。
  
      h3 签证
      .list
        .list-item 雇主提名计划（ENS）签证-允许雇主提名的技术工人永久在该国生活和工作。
        .list-item 地区赞助移民计划（RSMS）签证-允许澳大利亚地区雇主提名的技术工人永久在该国生活和工作。
        .list-item 技术独立签证-受邀工人和新西兰公民的技能，澳大利亚需要在澳大利亚的任何地方永久生活和工作
        .list-item 技术提名签证-允许被提名的技术工人作为永久居民在澳大利亚生活和工作。
        .list-item 临时技能短缺签证-临时签证，使雇主能够在澳大利亚人不可用时赞助合适的技术工人
        .list-item 技术地区（临时）签证-希望在澳大利亚地区生活和工作的技术工人的临时签证。
        .list-item 临时工作（短期停留专家）签证-临时签证，可让您在该国从事短期，高度专业化的工作。
        .list-item 临时工作（国际关系）签证-在改善澳大利亚国际关系的特殊情况下工作的临时签证
        


  site-footer(lang="zh" @contact-us="handleScrollToTop")

</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
definePageMeta({ layout: 'basic' })
useHead({ htmlAttrs: { lang: 'zh-CN' }, title: '澳大利亚名义雇主服务' })

const status = reactive({
  showForm: false
})

function switchLang(lang) {
  langTool.swithLang(lang, '/aboutus')
}

function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss" scoped>
@import './common.scss';
</style>