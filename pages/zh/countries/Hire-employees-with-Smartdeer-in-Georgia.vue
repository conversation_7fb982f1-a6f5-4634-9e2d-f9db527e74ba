<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在格鲁吉亚雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在格鲁吉亚雇佣',
  ogDescription: '基础信息 首都：第比利斯 Tbilisi 时区：GMT-4 语言：格鲁吉亚语 货币代码：GEL 人力资源制度概 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：第比利斯 Tbilisi 时区：GMT-4 语言：格鲁吉亚语 货币代码：GEL 人力资源制度概 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在格鲁吉亚雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在格鲁吉亚雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/格鲁吉亚FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/格鲁吉亚Flag.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：第比利斯 Tbilisi</p><p>时区：GMT-4</p><p>语言：格鲁吉亚语</p><p>货币代码：GEL</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h2 class="wp-block-heading has-large-font-size">雇佣合同</h2><p>合同必须使用格鲁吉亚语或英语书写。它们必须书面并由双方签署。</p><p>合同必须包括：</p><ul><li>工作开始日期和雇佣期限</li><li>工作时间和休息时间</li><li>工作类型：主要或次要就业</li><li>职位及工作类型或描述</li><li>带薪休假的持续时间、无薪带薪休假的持续时间和授予这些休假的程序</li><li>雇主和员工解雇的程序</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期不是强制性的。标准试用期为90天，最长试用期为180天。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工时为每天8小时，每周40小时。标准工作周从星期一到星期五。</p><p>加班费是强制性的。在雇佣协议中规定的标准工时之外的时间被视为加班。对于额外的工时，员工获得每小时薪水的125%。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低月工资为20格鲁吉亚拉里，过去30年未有调整。</p><p>平均最低工资约为1300格鲁吉亚拉里。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计为员工薪水的2%。</p><ul><li>累积养老金计划 &#8211; 2.0%</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>解雇必须遵守复杂的规定和员工所在国家的规定。解雇始终由雇主与主要利益相关者处理。它可能包括特定解雇案件的特定费用以及所需或建议的步骤。</p><p>在格鲁吉亚，解雇可能会变得复杂。在试用期之外，雇主不能随意解雇员工，解雇必须有正当原因。</p><p>符合规定的解雇包括：</p><ul><li>员工自愿离职</li><li>雇主单方面解雇，基于：<ul><li>试用期</li><li>客观原因</li><li>纪律性解雇</li></ul></li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>通告期将取决于解雇的类型。</p><ul><li>有原因的解雇 &#8211; 30天</li><li>辞职 &#8211; 30天</li><li>试用期 &#8211; 无需通告</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>如果雇主提出终止合同，雇员有权获得至少相当于一个月工资的遣散费。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>格鲁吉亚的工资应至少每月支付一次。任何付款或结算每延迟一天，雇主必须向雇员支付延迟金额的 0.07%。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>尽管佐治亚州劳动法没有要求，但建议雇主每月向每位雇员提供工资单，详细说明相关期间收入的组成部分。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工均有权每年获得24个工作日的带薪休假（PTO）。PTO每月累积2天，连续工作11个月后开始。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>按照法律，在格鲁吉亚没有病假制度。如果员工需要请病假，他们可以使用15天的无薪病假。员工的合同可以暂停40天，此后，雇主有权终止合同。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>员工有权获得126天的带薪休假。社会保障将在此期间支付员工过去3个月平均工资的100%（封顶为每月1,000格鲁吉亚拉里）</p><p>员工可以额外休假17天，薪水为100%，封顶为每月1,000格鲁吉亚拉里。</p><p>员工有权获得604天的陪产假。双方父母可以决定如何分配休息时间。社会保障将在此期间支付员工过去3个月平均工资的100%，封顶为每月1,000格鲁吉亚拉里。此期间仅支付前200天，之后将是无薪休假。</p><p>此外，员工还有权在孩子5岁之前每年获得12周的无薪陪产假。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税以单一固定税率20%征收。</p><h2 class="wp-block-heading has-large-font-size">社会保险</h2><p>员工享有法律和集体协议规定的各种福利的权利。这些福利包括退休金、年假、产假、育儿假等。</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>Occasion</td><td>&nbsp;&nbsp;&nbsp;Date</td></tr><tr><td>New Year&#8217;s Day</td><td>&nbsp;&nbsp; 1.1</td></tr><tr><td>New Year Holiday</td><td>&nbsp;&nbsp; 1.2</td></tr><tr><td>Orthodox Christmas Day</td><td>&nbsp;&nbsp; 1.7</td></tr><tr><td>Orthodox Epiphany</td><td>&nbsp;&nbsp; 1.19</td></tr><tr><td>Mother&#8217;s Day</td><td>&nbsp;&nbsp; 3.3</td></tr><tr><td>International Women&#8217;s Day</td><td>&nbsp;&nbsp; 3.8</td></tr><tr><td>Independence Restoration Day</td><td>&nbsp;&nbsp; 4.9</td></tr><tr><td>Orthodox Good Friday</td><td>&nbsp;&nbsp; 5.3</td></tr><tr><td>Orthodox Easter Saturday</td><td>&nbsp;&nbsp; 5.4</td></tr><tr><td>Orthodox Easter Sunday</td><td>&nbsp;&nbsp; 5.5</td></tr><tr><td>Orthodox Easter Monday</td><td>&nbsp;&nbsp; 5.6</td></tr><tr><td>Victory Day</td><td>&nbsp;&nbsp; 5.9</td></tr><tr><td>Saint Andrew the First Called Day</td><td>&nbsp;&nbsp; 5.12</td></tr><tr><td>ndependence Day</td><td>&nbsp;&nbsp; 5.26</td></tr><tr><td>Saint Mary&#8217;s Day</td><td>&nbsp;&nbsp; 8.28</td></tr><tr><td>Svetitskhovloba Day</td><td>&nbsp;&nbsp; 10.14</td></tr><tr><td>Saint George&#8217;s Day</td><td>&nbsp;&nbsp; 11.23</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>