<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在挪威雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在挪威雇佣',
  ogDescription: '基础信息 首都：奥斯陆 Oslo 时区：GMT+1 语言：挪威语、萨米语 货币代码：NOK 人力资源制度概况 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：奥斯陆 Oslo 时区：GMT+1 语言：挪威语、萨米语 货币代码：NOK 人力资源制度概况 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在挪威雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在挪威雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/挪威FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/挪威国旗.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：奥斯陆 Oslo</p><p>时区：GMT+1</p><p>语言：挪威语、萨米语</p><p>货币代码：NOK</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>&nbsp;&nbsp;合同必须使用挪威语或英语，并且可以是双语的。合同必须以书面形式由双方签署。</p><p>&nbsp;&nbsp;合同必须包括：</p><ul><li>员工和雇主的姓名</li><li>工作地点</li><li>职位描述</li><li>雇佣的第一天</li><li>雇佣期限</li><li>试用期</li><li>假期权利和支付</li><li>工资和任何其他补贴</li><li>工作时间</li><li>休息时间长度</li><li>根据有关减少工作时间、灵活工作时间等规定的特殊工作时间安排</li><li>终止条件</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>雇佣合同可包括最长六个月的试用期。试用期必须在劳动合同中规定。在试用期内，由于与雇员有关的情况，在通知期内合法解雇的门槛被认为较低，并且可能是基于雇员不适合工作或缺乏能力的理由。熟练程度或可靠性。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>加班费是强制性的。工作时间超出标准工时被视为加班。员工每周最多可以工作10小时的加班，每4周可以工作25小时的加班。额外的工时将按以下方式支付：</p><ul><li>工作日加班每小时工资的140%</li><li>夜班和周末加班每小时工资的200%</li></ul><p>&nbsp;&nbsp;标准工作时间为每天7.5小时，每周37.5小时。标准工作周从星期一到星期五。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>挪威没有普遍的最低工资。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><ul><li>雇主成本通常估计为员工工资的18%。<ul><li>公共养老金基金 &#8211; 4%</li></ul></li></ul><p>&nbsp;&nbsp;雇主社会保险（ER SS） &#8211; 14.1%</p><ul><li>请注意：</li></ul><p>公共养老金基金缴费的最高工资为2023年为1,423,440挪威克朗。这个上限相当于年度工资的12倍（G-amount），由挪威政府每年设定。</p><ul><li>雇主社会保险（ER SS）是根据员工的养老金计划和总月薪（包括工资、奖金、佣金、津贴等）计算的。</li><li>有条件的费用：</li></ul><p>其他社会保险 &#8211; 员工的养老金计划和总月薪（包括工资、奖金、佣金、津贴等）的5%。</p><ul><li>额外的社会保险费用仅适用于年薪超过75万挪威克朗的员工，并且只有在员工的工资达到此金额后才适用。2024年，此限额将增加到850,000挪威克朗。</li><li>年度费用：<ul><li>假期奖金<ul><li>年龄在59岁以下的员工：年度总薪资的12%，包括工资、奖金、佣金、津贴等。</li><li>年龄在60岁及以上的员工：年度总薪资的14.3%。</li></ul></li><li>雇主社会保险（ER SS）在假期奖金上的费用：假期奖金的14.1%。</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>在挪威，解雇可能很复杂。除了试用期外，挪威没有无理由解雇的规定，解雇必须有正当理由。</p><p>符合规定的解雇包括：</p><ul><li>员工自愿辞职</li><li>双方同意</li><li>无须事先通知的解雇仅在员工违反了严重职责或其他严重违反雇佣合同的情况下才允许</li><li>试用期内的终止应基于员工对工作的不适合或缺乏能力或可靠性</li><li>有理由解雇应基于与雇主或员工有关的情况客观合理</li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>&nbsp;&nbsp;最短通知期为一个月，但通知期根据雇佣期限而增加。</p><p>&nbsp;&nbsp;试用期 &#8211; 14天通知期</p><ul><li>雇佣不满5年 &#8211; 2个月通知期</li><li>雇佣不满10年 &#8211; 3个月通知期</li><li>自雇佣满10年后，通知期为：<ul><li>在员工不满50岁时至少4个月</li><li>在员工55岁时至少5个月</li><li>在员工60岁时至少6个月</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在挪威，没有法定的遣散费要求。在通知期内，员工仍有权获得工资支付和根据雇佣条款的额外合同福利。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>在挪威，工资通常每月支付一次，并直接存入雇员指定的银行账户。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>工资单应显示工资总额、税收和从工资总额、净额中扣除的其他费用，以及假期工资的计算基础。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职员工每年有权享受25个工作日的带薪休假（PTO）。带薪休假每月累积2.08天。</p><p>兼职员工没有带薪休假的权利。他们可以休假，但不会获得工资。他们将在年底获得假期津贴（按照公司周期，最低为10.2%或12%的比例），根据公司周期，最低为10.2%）。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>没有病假的最长天数限制。员工可以最多获得1年的病假津贴。根据病假的长度和支付者的不同，此假期的支付比例也不同。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>&nbsp;&nbsp;怀孕员工，最近10个月中至少工作了6个月的，有权享受带薪产假。休假的天数取决于员工选择的选项：</p><ul><li>49周，薪资平均100%（最高达6G（6G相当于年薪638,394挪威克朗，截至2021年5月）；或</li><li>59周，薪资平均80%</li></ul><p>&nbsp;&nbsp;挪威劳工和福利管理局（NAV）将负责支付此薪资。</p><p>&nbsp;&nbsp; 在整个休假期间，母亲有权享受母亲配额：</p><ul><li>15周（如果选择了49周）薪资平均100%</li><li>19周（如果选择了59周）薪资平均80%</li></ul><p>  前5周必须在婴儿出生后立即休假，其余的可以立即休假或留到以后。员工可以无薪延长休假1年。</p><p>&nbsp;&nbsp;在过去的10个月中至少工作了6个月的父母有资格享受49或59周的联合休假。休假的天数取决于员工选择的选项：</p><ul><li>49周，薪资平均100%（最高达6G（6G相当于年薪638,394挪威克朗，截至2021年5月）；或</li><li>59周，薪资平均80%</li></ul><p>挪威劳工和福利管理局（NAV）将负责支付此薪资。这段休假期间分为母亲配额、父亲配额和联合休假（在下面的部分中解释）。</p><p>员工使用了15周或19周的母亲和父亲配额后，他们有权获得共享的期间，这是共享的育儿津贴。这可以平均分配，也可以由母亲或父亲完全使用。但是，如果父亲/共同父母想要利用联合期间，母亲必须在工作、教育或类似领域中从事职业活动。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税率为22%。所得税采用统一税率计算。</p><h3 class="wp-block-heading">社会保险</h3><p>国民保险计划的成员有权享受以下法定福利： </p><p>养老金 </p><p>遗属养老金 </p><p>残疾养老金</p><p>残疾的基本福利和护理福利 </p><p>康复福利 </p><p>工伤福利 </p><p>单亲父母福利</p><p>生病时的现金福利 </p><p>生育和收养福利 </p><p>失业救济金 </p><p>医疗福利 </p><p>生育和丧葬补助金</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>&nbsp;&nbsp; 日期</td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>Maundy Thursday</td><td>3.28</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Easter Sunday</td><td>3.31</td></tr><tr><td>Easter Monday</td><td>4.1</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Ascension Day</td><td>&nbsp;&nbsp; 5.9</td></tr><tr><td>Constitution Day</td><td>5.17</td></tr><tr><td>Whitsun</td><td>5.19</td></tr><tr><td>Whit Monday</td><td>5.2</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>