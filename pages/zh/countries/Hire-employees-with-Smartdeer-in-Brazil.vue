<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在巴西雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在巴西雇佣',
  ogDescription: '基础信息 首都：巴西利亚 Brasília 时区：GMT-5 语言：葡萄牙语 货币代码：BRL 人力资源制度概 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：巴西利亚 Brasília 时区：GMT-5 语言：葡萄牙语 货币代码：BRL 人力资源制度概 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在巴西雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在巴西雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/th.jpeg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/OIP-C.jpeg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：巴西利亚 Brasília</p><p>时区：GMT-5</p><p>语言：葡萄牙语</p><p>货币代码：BRL</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p>合同必须用葡萄牙语或英语编写，也可以是双语的。它们必须以书面形式，并由双方签署。<br>合同必须包括：</p><ul><li>姓名</li><li>开始日期</li><li>工作地点</li><li>雇佣期限</li><li>工作描述</li><li>终止条件</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期不是强制性的。没有最短试用期。最长试用期为90天（3个月）。</p><p>固定期限合同不能有试用期。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>国家最低工资为每月1,412.00巴西雷亚尔。</p><p>所有超过每周 44 小时标准的工作均应支付加班费，并受雇佣合同/集体协议等规定。 一般情况下，加班上限为每天 2 小时，并按标准的 150.00% 支付加班费。 假期工作的工资最高可达正常工资的 200.00%。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计为员工工资的36.8% + 餐券</p><ul><li>Fundo de Garantia do Tempo e Serviço (FGTS) &#8211; 8%*</li><li>社会保障 &#8211; 28.8%*</li><li>餐补 &#8211; 每个工作日40巴西雷亚尔，每月估算为880巴西雷亚尔</li></ul><p>*这些成本是根据员工每月工资（包括奖金和津贴）计算的</p><p>餐券的金额按每月工作日40巴西雷亚尔计算，因此确切金额可能有所变化。</p><p>年度成本</p><ul><li>13薪 &#8211; 1个月工资，计算为年度内收到的毛月薪，包括其他薪酬支付，如奖金、津贴，除以12。</li><li>工会奖金（利润分成） &#8211; 一年300.50巴西雷亚尔（约合59美元）</li><li>假期奖金 &#8211; 员工月薪的三分之一或休假天数的三分之一</li></ul><p>有条件的成本</p><ul><li>现金津贴 &#8211; 员工可以选择领取最多10天的休假奖金，而不是将其用作带薪休息。</li><li>托儿津贴 &#8211; 年龄在0-60个月的女性员工有权获得每个孩子最多290.98巴西雷亚尔的月度托儿津贴。</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p>终止必须遵守复杂的规定和雇员所在国家的规定。离职总是由雇主与主要利益相关者处理。它可能包括特定终止案例的特定费用或所需或建议的步骤。<br>在巴西，终止可能会很复杂。除了某些员工（怀孕、在工作中遭受事故或被选为内部事故预防委员会主席）外，雇主在巴西可以随时终止雇佣。<br>巴西的合规终止包括：</p><ul><li>双方同意</li><li>员工自愿离职</li><li>雇主基于有正当理由</li><li>重大不当行为</li><li>滥用药物</li><li>违反商业机密</li><li>纪律解雇</li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通知期为1个月，将根据雇佣期的长短而增加。</p><ul><li>雇佣不满1年 &#8211; 30天通知期</li><li>超过1年的雇佣 &#8211; 通知期将根据工作年限每年增加3天，直到达到最长通知期90天</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在巴西，如果员工没有正当理由被解雇，员工有权获得：</p><ul><li>未结算薪水</li><li>未使用的假期支付</li><li>比例13薪（奖金支付）</li><li>在职期间支付的FGTS的40%<br>如果员工因正当理由被解雇，员工有权获得：</li><li>未结算薪水</li><li>未使用的假期支付</li><li>比例13薪（奖金支付）</li></ul><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>所有工资单交易必须采用巴西货币 — BRL。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工每年有权享受30个<strong>日历日</strong>的带薪休假（PTO）。PTO每月累积2.5天。员工在工作满1年后有资格休年假。在第一年的雇佣期内，员工不被允许休假。</p><p>请注意：我们的平台以工作日计算，巴西的PTO是按照日历日计算的。 （20个工作日 &#8211; 30个日历日）。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工没有最长的病假期限。这种休假按照不同的比例和支付者根据时长而异：<br></p><figure class="wp-block-table"><table><tbody><tr><td>时期</td><td>金额（工资的百分比）</td><td>支付方</td></tr><tr><td>0 &#8211; 15天</td><td>100%</td><td>雇主</td></tr><tr><td>15天以上</td><td>91% (最多为6.433,57巴西雷亚尔)</td><td>社会保障机构(INSS)</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>怀孕的员工有权享受120天的带薪休假。在此期间，员工将获得100%的平均工资，雇主将负责支付。然而，政府在同一月份通过税收减免返还此金额。</p><p>员工可以凭医生证明延长休假。在此期间，员工将获得100%由雇主支付的工资。</p><p>在巴西，没有法律规定父母假。然而，员工可能有资格获得产假和陪产假。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p>个人所得税率为0%至27.5%不等。个人所得税按照渐进税率计算。多个附加因素可能影响总税率，如婚姻状况和收入水平。</p><figure class="wp-block-table"><table><tbody><tr><td>年度总收入(BRL)</td><td>税率(%)</td></tr><tr><td>少于1,903.98</td><td>0%</td></tr><tr><td>1,903.99 &#8211; 2,826.65</td><td>7.50%</td></tr><tr><td>2,826.66 &#8211; 3,751.05</td><td>15.00%</td></tr><tr><td>3,751.06 &#8211; 4,664.68</td><td>22.50%</td></tr><tr><td>多于4,664.68</td><td>27.50%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">社会保险</h2><ul><li>餐券</li><li>社会保障</li><li>国家养老基金</li><li>工伤保险</li><li>私人医疗保险 &#8211; Unisure（可选）</li><li>私人医疗保险 &#8211; AMIL（可选）</li></ul><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Carnival</td><td>2.12-2.13</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Tiradentes Day</td><td>4.21</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Corpus Christi</td><td>5.30</td></tr><tr><td>Independence Day</td><td>9.7</td></tr><tr><td>Our Lady of Aparecida</td><td>10.12</td></tr><tr><td>All Souls&#8217; Day</td><td>11.2</td></tr><tr><td>Republic Day</td><td>11.15</td></tr><tr><td>Black Consciousness Day</td><td>11.20</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>