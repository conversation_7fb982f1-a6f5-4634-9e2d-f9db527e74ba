<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在塞浦路斯雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在塞浦路斯雇佣',
  ogDescription: '基础信息 首都：尼科西亚 Nicosia 时区：GMT+2 语言：希腊语、土耳其语 货币代码：EUR 人力资源 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：尼科西亚 Nicosia 时区：GMT+2 语言：希腊语、土耳其语 货币代码：EUR 人力资源 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在塞浦路斯雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在塞浦路斯雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/塞浦路斯-FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/塞浦路斯国旗.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：尼科西亚 Nicosia</p><p>时区：GMT+2</p><p>语言：希腊语、土耳其语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>合同可以用英语、希腊语或双语编写。合同必须是书面形式，并由双方签署。合同必须包括：</p><ul><li>姓名</li><li>入职日期</li><li>雇佣期限</li><li>工作描述</li><li>解雇条件</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期不是强制性的。最长试用期为180天（六个月），可延长至2年。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周从星期一到星期五。</p><p>在零售和销售行业工作的员工可以每天标准工作8小时，每周48小时，从星期一到星期六。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月924欧元。然而，在雇佣的头六个月内，最低工资可以为870欧元。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">雇主成本通常估计为员工工资的15.40%。</span><div data-page-id="ERuPd72gTovciqxe3RycgAcgnWh" data-docx-has-block-data="false"><ul start="1" class="list-bullet1"><li class="ace-line ace-line old-record-id-AyL7dee5voyCXgxIf2PcaWwJnPb" data-list="bullet">社会保障 &#8211; 8.80%*社会凝聚基金 &#8211; 2%人力资源 &#8211; 0.5%*失业基金 &#8211; 1.2%*总体医疗系统 &#8211; 2.9%**</li>*最高工资上限为5,239欧元的缴纳**最高工资上限为15,000欧元的缴纳</ul></div></p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>塞浦路斯的解雇可能很复杂。对于雇主来说，在塞浦路斯，除了试用期外，并且解雇必须有正当理由。</p><p>符合法规的解雇包括：</p><ul><li>员工自愿离职</li><li>达成共识</li><li>雇主单方面解雇，原因包括：<ul><li>试用期</li><li>员工严重违法</li><li>纪律解雇</li></ul></li><li>合同到期</li></ul><p>如果要解雇员工，雇主必须在解雇原因发生后的指定期限内这样做。否则，解雇可能被视为非法。通知的时间跨度必须由法律顾问逐案审查。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通知期为七天，根据雇佣时间的长短而增加。</p><ul><li>如果服务时间不超过51周，则为一周</li><li>如果服务时间不超过103周，则为两周</li><li>如果服务时间不超过155周，则为四周</li><li>如果服务时间不超过207周，则为五周</li><li>如果服务时间不超过311周，则为六周</li><li>如果服务时间超过312周，则为七周</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在塞浦路斯，所有非法解雇或由于雇主行为而被解雇的员工都有权获得解雇补偿。解雇补偿取决于服务时间：</p><ul><li>4年及以下的服务时间 &#8211; 2周薪水</li><li>5至10年的服务时间 &#8211; 2.5周薪水</li><li>11至15年的服务时间 &#8211; 3周薪水</li><li>16至20年的服务时间 &#8211; 3.5周薪水</li><li>20年以上的服务时间 &#8211; 4周薪水</li></ul><p>如果员工在解雇日期之前达到了可领养年龄，则不享有雇佣终止补偿。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>每月1次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>雇主通常提供每月工资单，电子或硬拷贝都可以。此外，雇主必须每年提供薪酬证明。该证明必须包括雇员、总薪酬、所得税、社会保险和医疗保健缴费的详细信息。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职员工有权每年享受20个工作日的带薪休假（PTO）。每周工作六天的员工，有权每年享受24个工作日的带薪PTO。 PTO每月以每月1.67天的速度积累，员工必须在当前年度内休假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>全职员工有权每年享受20个工作日的带薪休假（PTO）。每周工作六天的员工，有权每年享受24个工作日的带薪PTO。 PTO每月以每月1.67天的速度积累，员工必须在当前年度内休假。</p><figure class="wp-block-table"><table><tbody><tr><td>时期</td><td>金额</td><td>支付方</td></tr><tr><td>0 &#8211; 3天</td><td>基本工资的100%</td><td>雇主</td></tr><tr><td>4 &#8211; 156天</td><td>基本工资的60%</td><td>社会保障</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>为同一雇主工作了180个连续天的怀孕员工有权享受18周的带薪产假。必须在孩子出生前休息两周。在此期间，员工将获得其薪水的72%，社会保障将负责支付。</p><p>员工可以在雇主自行决定的情况下延长休假，无需付款。</p><p>员工有权享受8个月的家庭假。父母双方可以决定如何分配休息时间，该时间应在陪产假和产假之后休息。任何一方都可以休家庭假。此假期将不带薪。</p><p>家庭假期无法延长。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税率从0%到35%不等。个人所得税按照渐进税率计算。诸如家庭状况和子女数量等多个附加因素可能影响总税率。</p><figure class="wp-block-table"><table><tbody><tr><td>年度总收入</td><td>税率(%)</td></tr><tr><td>低于19500欧元</td><td>0%</td></tr><tr><td>低于28000欧元</td><td>20%</td></tr><tr><td>低于36300欧元</td><td>25%</td></tr><tr><td>低于60000欧元</td><td>30%</td></tr><tr><td>高于60000欧元</td><td>35%</td></tr></tbody></table></figure><h3 class="wp-block-heading">社会保险</h3><p>在塞浦路斯受雇的所有人员，无论是雇员还是自营职业者，都受到社会保险计划的保护。提供以下好处：</p><p>疾病补助金</p><p> 生育补助金</p><p>工伤事故和职业病津贴，包括暂时丧失工作能力（受伤补助金）</p><p>伤残补助金</p><p>死亡补助金</p><p>伤残养老金 裁员补助金 </p><p>失业补助金 养老金 </p><p>寡妇养老金 </p><p>孤儿抚恤金 </p><p>丧葬补助金</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>Epiphany</td><td>1.6</td></tr><tr><td>Green Monday</td><td>3.18</td></tr><tr><td>Greek Independence Day</td><td>3.25</td></tr><tr><td>National Day</td><td>4.1</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Orthodox Good Friday</td><td>5.3</td></tr><tr><td>Orthodox Easter Monday</td><td>5.6</td></tr><tr><td>Orthodox Whit Monday</td><td>6.24</td></tr><tr><td>Assumption Day</td><td>8.15</td></tr><tr><td>Cyprus Independence Day</td><td>10.1</td></tr><tr><td>Ochi Day</td><td>10.28</td></tr><tr><td>Christmas Eve</td><td>12.24</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr><tr><td>Boxing Day</td><td>12.26</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>