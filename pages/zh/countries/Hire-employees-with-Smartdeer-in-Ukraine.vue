<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在乌克兰雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在乌克兰雇佣',
  ogDescription: '基础信息 首都：基辅 Kyiv 时区：GMT+2 语言：乌克兰语 货币代码：UAH 人力资源制度概况 雇佣合同 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：基辅 Kyiv 时区：GMT+2 语言：乌克兰语 货币代码：UAH 人力资源制度概况 雇佣合同 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在乌克兰雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在乌克兰雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/乌克兰-FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/乌克兰-国旗.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：基辅 Kyiv</p><p>时区：GMT+2</p><p>语言：乌克兰语</p><p>货币代码：UAH</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>&nbsp;&nbsp;合同必须用乌克兰语书写，可以是双语。必须书面签署双方。</p><p>&nbsp;&nbsp;合同必须包括：</p><ul><li>工作地点</li><li>工作时间</li><li>休息时间</li><li>职位</li><li>权利和责任</li><li>报酬金额</li><li>有效期</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期不是强制性的。没有最低试用期。非蓝领员工的最长试用期为90天（3个月）。对于蓝领员工，最长试用期为1个月。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周为周一至周五。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月7100乌克兰格里夫纳。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><ol start="1"><li>  雇主成本通常估计为员工工资的22％。</li></ol><ul><li>强制性国家社会保险 &#8211; 22％</li><li>一次性湿墨雇佣协议处理费 &#8211; 80美元</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>乌克兰的解雇可能很复杂。在试用期之外，乌克兰的雇主不能随意解雇员工，解雇必须有正当理由。符合条件的终止包括：</p><ul><li>员工自愿离职</li><li>双方协议解除</li><li>雇主单方面解除，基于：<ul><li>试用期</li><li>生产和劳动组织的变更</li><li>酗酒状态下上班</li><li>员工与所聘职位不相容</li><li>由于工作不适合而导致的绩效</li><li>员工的系统性不履行</li><li>由于临时伤残而连续缺勤工作超过四个月</li></ul></li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>&nbsp;&nbsp;通知期基于解雇的理由。</p><ul><li>双方协议 &#8211; 无需通知期</li><li>试用期内 &#8211; 3天通知期</li><li>员工自愿离职 &#8211; 14天通知期</li><li>雇主单方面解除，涉及裁员 &#8211; 2个月通知期</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>解雇补偿金是在员工被解雇时支付的，而不是主动提出离职。解雇补偿金的金额基于解除合同的情况。</p><figure class="wp-block-table"><table><tbody><tr><td>案例</td><td>遣散</td></tr><tr><td>1.员工拒绝调到另一个组织工作，以及由于工作条件的重大变化而拒绝继续工作。2. 员工与职位或工作的不相容 3.恢复以前在工作中工作的员工</td><td>1个月工资</td></tr><tr><td>征召雇员或所有者</td><td>2个月工资</td></tr><tr><td>所有者或授权机构的违规行为</td><td>3个月工资</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>按年或按月计算工资的员工每月必须至少领取两次工资，两次工资发放间隔不得超过16天。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>工资单应包含休假、病假、出差、加班、周末和节假日工作的信息。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>&nbsp;&nbsp;全职和兼职员工每年都有最少24天的带薪休假（PTO）。带薪休假根据员工类型而异：</p><ul><li>第一类或第二类残疾人员工 &#8211; 30天带薪休假</li><li>第三类残疾人员工 &#8211; 26天带薪休假</li><li>未满18岁的员工 &#8211; 31天带薪休假</li><li>季节性工人 &#8211; 按工作时间比例计算</li></ul><p>&nbsp;&nbsp;带薪休假每月累积2天。员工在工作满6个月后有资格休带薪休假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>乌克兰员工根据以下标准有权休病假：</p><ul><li>临时伤残：自伤残开始之日起连续4个月</li><li>反复发作的疾病：由于同一疾病或其并发症，在过去12个月内连续5个月</li></ul><p>&nbsp;&nbsp; 结核病：自发病之日起10个月内</p><p>员工必须至少工作6个月并向社会保险基金缴纳费用。</p><p>病假支付如下：</p><ul><li>1-5天：由雇主支付员工正常工资的100％</li><li>6天及以上：由社会保障基金支付员工正常工资的50-100％，根据就业保险记录的长度<ul><li></li></ul></li></ul><figure class="wp-block-table"><table><tbody><tr><td>就业保险记录</td><td>工资（工资的百分比）</td></tr><tr><td>少于 3 年</td><td>50%</td></tr><tr><td>3 &#8211; 5 岁</td><td>60%</td></tr><tr><td>5 &#8211; 8 岁</td><td>70%</td></tr><tr><td>8岁以上</td><td>100%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>女性员工在生育1个孩子且无并发症时享有126天的产假，生育2个或更多孩子或产生并发症时享有140天的产假。直接从产房领养新生儿的员工享有56天的产假。</p><p>对于生育1个孩子且无并发症的情况，必须在预产期前休息70天，产后休息56天。对于生育2个或更多孩子或产生并发症的情况，必须在预产期前休息70天，产后休息70天。</p><p>这种假期由社会保险基金支付，支付金额为员工正常工资的100％。员工不能延长休假。</p><p>在乌克兰，没有法律规定育婴假。然而，员工可能有资格获得产假和陪产假。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>乌克兰的个人所得税率为18％。个人所得税采用统一税率计算。</p><h3 class="wp-block-heading">社会保险</h3><p>雇主除了支付工资外，还需承担社会保障缴款（统一社会保障金，USC）。这笔费用占雇员月薪的 22%，上限为 100,500 乌克兰格里夫纳，按最低工资的 15 倍计算。2023 年乌克兰的最低工资为每月 6,700 乌克兰格里夫纳。</p><p>员工无需向 USC 缴纳费用。</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>&nbsp; 假期</td><td>日期</td></tr><tr><td>&nbsp; New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>&nbsp; Orthodox Christmas Day</td><td>1.7</td></tr><tr><td>&nbsp; Orthodox Christmas Holiday</td><td>1.8</td></tr><tr><td>&nbsp; Women&#8217;s Day</td><td>3.8</td></tr><tr><td>&nbsp; Labour Day</td><td>5.1</td></tr><tr><td>&nbsp; Orthodox Easter Sunday</td><td>5.5</td></tr><tr><td>&nbsp; Orthodox Easter Monday</td><td>5.6</td></tr><tr><td>&nbsp; Victory Day</td><td>5.9</td></tr><tr><td>&nbsp; Orthodox Whit Sunday</td><td>6.23</td></tr><tr><td>&nbsp; Orthodox Whit Monday</td><td>6.24</td></tr><tr><td>&nbsp; Constitution Day</td><td>6.28</td></tr><tr><td>&nbsp; Ukranian Statehood Day</td><td>7.28</td></tr><tr><td>&nbsp; Independence Day</td><td>8.24</td></tr><tr><td>&nbsp; Independence Day Holiday</td><td>8.26</td></tr><tr><td>&nbsp; Defender’s Day Holiday</td><td>10.14&nbsp;</td></tr><tr><td>&nbsp; Catholic Christmas Day</td><td>12.25&nbsp;</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>