<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在瑞典雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在瑞典雇佣',
  ogDescription: '基础信息 首都：斯德哥尔摩 Stockholm 时区：GMT+1 语言：瑞典语 货币代码：SEK 人力资源概况 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：斯德哥尔摩 Stockholm 时区：GMT+1 语言：瑞典语 货币代码：SEK 人力资源概况 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在瑞典雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在瑞典雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/瑞典FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/瑞典国旗.png';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：斯德哥尔摩 Stockholm</p><p>时区：GMT+1</p><p>语言：瑞典语</p><p>货币代码：SEK</p><h2 class="wp-block-heading has-large-font-size">人力资源概况制度</h2><h3 class="wp-block-heading">雇佣合同</h3><p>合同为英文，可以是双语的。合同必须书面并由双方签署。</p><p>合同必须包含员工姓名、入职日期、雇佣期限、假期条件等强制性信息。</p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>根据法律规定，固定期限的雇佣合同不能设定试用期。</p><p>无固定期限合同的最长试用期为180天（6个月）。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周从星期一到星期五。加班费不是强制性的，可能已包含在工资中。如果在雇佣协议中指定，标准工作时间以外的时间可以被视为加班。员工每月最多可以加班50小时，每年最多可加班150小时。对于额外工时，员工的时薪为时薪的168%。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>法律没有关于最低工资的规定。如果适用于公司，最低工资可能由集体协议（CBA）规定。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><ul><li>月成本<ul><li>社会保障缴费 &#8211; 31.42%*</li><li>长期残疾保险 &#8211; 平均成本：SEK 485**</li></ul></li></ul><p>*社会保障缴费涵盖了雇主向健康保险、育婴保险、退休金和遗属抚恤金、劳动市场、工人赔偿以及一般工资税方面的强制性贡献。对于2005年至2007年出生、1938年至1956年出生或1937年或更早出生的员工，预计成本有所不同。</p><p>**长期残疾保险的确切成本将根据员工的工资和年龄在注册时确定，并且会有所不同。</p><ul><li>年度成本<ul><li>假期工资 &#8211; 在休假年度（4月至3月）期间，员工的可变支付（工资、奖金、加班补偿等）的12%。</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>在瑞典，没有无条件解雇。瑞典的解雇可以由雇主、员工或双方协议实现。要终止合同，双方必须提供正确的通知。</p><p>&nbsp;&nbsp;符合规定的解雇包括：</p><ul><li>员工自愿辞职</li><li>双方协议</li><li>合同到期</li><li>雇主单方面基于：<ul><li>试用期</li><li>客观理由</li><li>由于工作不适合</li><li>不当行为</li><li>合作问题</li><li>拒绝工作</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>&nbsp;&nbsp;最短通知期为30天，并根据雇佣年限而增加：</p><ul><li>如果雇佣不满两年，通知期为30天。</li><li>如果雇佣时间在两年到四年之间，通知期为60天。</li><li>如果雇佣时间在四年到六年之间，通知期为90天。</li><li>如果雇佣时间在六年到八年之间，通知期为120天。</li><li>如果雇佣时间在八年到十年之间，通知期为150天。</li><li>如果雇佣时间超过十年，通知期为180天。</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在瑞典，没有法定的遣散费要求。在通知期内，员工仍然有权获得标准工资。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>一月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>员工有权收到书面薪资说明，显示其薪资和扣除额。雇主还必须向雇员和瑞典税务局提供年度损益表。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职员工每年有权利休25天带薪休假（PTO）。带薪休假每月累积2.08天。员工从每年4月1日到次年3月31日获得假期。</p><p>兼职员工的带薪休假将取决于他们每周工作的天数。如果员工每周工作5天，他们将有权利休25天。如果员工工作的天数较少，将采用按比例计算。</p><p>假期的累积是强制性的，占员工总工资的12%。雇主有义务在六月至八月期间为员工提供四周连续的假期。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工有权享受 90 天的带薪病假。该假期按不同的费率支付，并由其他付款人支付，具体取决于时间长短：</p><figure class="wp-block-table"><table><tbody><tr><td>时期</td><td>支付</td><td>付款人</td></tr><tr><td>1 &#8211; 14 天</td><td>基本工资的 80%</td><td>雇主</td></tr><tr><td>15 &#8211; 90 天</td><td>25-80%*</td><td>社会保险</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>怀孕的员工有权享受240天的带薪休假。在孩子出生前必须休假49天，孩子出生后至少休假49天。剩余天数可以与伴侣分成最多三段时间。员工在前195天将获得工资的近80%，剩余45天每天180瑞典克朗。瑞典社会保险局负责支付此薪金。</p><p>在瑞典，产假和陪产假被视为育婴假。因此，父母都有同样的权利休假。员工有权利获得总共480个工作日的育婴假。每个父母有权利获得240天，他们可以决定如何分配这150天的长度。瑞典社会保险局将在此期间支付工资。</p><p></p><figure class="wp-block-table"><table><tbody><tr><td>时期</td><td>支付</td><td>付款人</td></tr><tr><td>0 &#8211; 390 天</td><td>工资的百分比（近 80%）</td><td>社会保险</td></tr><tr><td>391 &#8211; 480 天</td><td>每天 SEK 180</td><td>社会保险</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税根据累进税率计算。许多额外因素可能影响整体税率，例如家庭状况、子女人数以及员工所在的市政府。</p><figure class="wp-block-table"><table><tbody><tr><td>年度总收入</td><td>国家税率 （%）</td><td>市政税率 （%）</td></tr><tr><td>不超过 598,500 瑞典克朗</td><td>0%</td><td>32%</td></tr><tr><td>超过 598,500 瑞典克朗</td><td>20%</td><td>32%</td></tr></tbody></table></figure><h3 class="wp-block-heading">社会保险</h3><p>瑞典拥有世界上最发达的福利体系之一。雇主按月从雇员工资中扣除社会保险缴款，并使用按劳即付（PAYE）系统将其汇给瑞典税务局。</p><p>员工社会保障缴款——员工必须向公共社会保障体系缴款 7%，年收入上限为 538,700 瑞典克朗（最高缴款额为 37,700 瑞典克朗）。</p><figure class="wp-block-table"><table><tbody><tr><td>Program 项目</td><td>Employer&nbsp;(%) 雇主（%）&nbsp;</td></tr><tr><td>Retirement Pension</td><td>10.21</td></tr><tr><td>Survivor&#8217;s Pension</td><td>0.60</td></tr><tr><td>Parental Insurance</td><td>2.60</td></tr><tr><td>Health Insurance</td><td>3.55</td></tr><tr><td>Labour Market Fee</td><td>2.64</td></tr><tr><td>Occupational Injury</td><td>0.20</td></tr><tr><td>General Payroll Tax</td><td>11.62</td></tr><tr><td>TOTAL</td><td>&nbsp; 31.42</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>&nbsp;&nbsp; 日期</td></tr><tr><td>New Year&#8217;s Day</td><td>&nbsp;&nbsp; 1.1</td></tr><tr><td>Epiphany</td><td>&nbsp;&nbsp; 1.6</td></tr><tr><td>Good Friday</td><td>&nbsp;&nbsp; 3.29</td></tr><tr><td>Easter</td><td>&nbsp;&nbsp; 3.31</td></tr><tr><td>Easter Monday</td><td>&nbsp;&nbsp; 4.1</td></tr><tr><td>May Day</td><td>&nbsp;&nbsp; 5.1</td></tr><tr><td>Ascension Day</td><td>&nbsp;&nbsp; 5.9</td></tr><tr><td>Whit Sunday</td><td>&nbsp;&nbsp; 5.19</td></tr><tr><td>National Day</td><td>&nbsp;&nbsp; 6.6</td></tr><tr><td>Midsummer Day</td><td>6.21</td></tr><tr><td>All Saints&#8217;Day</td><td>11.2</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr><tr><td>Second Day of Christmas</td><td>12.26</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>