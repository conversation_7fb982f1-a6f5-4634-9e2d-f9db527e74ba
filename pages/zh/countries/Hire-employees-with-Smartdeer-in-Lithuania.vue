<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在立陶宛雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在立陶宛雇佣',
  ogDescription: '基础信息 首都：维尔纽斯 Vilnius 时区：GMT+2 语言：立陶宛语 货币代码：EUR 人力资源制度概况 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：维尔纽斯 Vilnius 时区：GMT+2 语言：立陶宛语 货币代码：EUR 人力资源制度概况 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在立陶宛雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在立陶宛雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/瑞士FI-2.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/瑞士国旗.webp1_-2.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：维尔纽斯 Vilnius</p><p>时区：GMT+2</p><p>语言：立陶宛语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>&nbsp;&nbsp;合同必须用立陶宛语书面，并可用英语作为第二语言。必须书面签署双方。</p><p>&nbsp;&nbsp;合同必须包括：</p><ul><li>名字</li><li>开始日期</li><li>雇佣期限</li><li>试用期</li><li>工作时间</li><li>年假</li><li>工作描述</li><li>终止条件</li></ul><p>&nbsp;&nbsp;不允许合同反向日期。</p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期最长为三个月，但雇主和雇员可以约定更短的试用期。试用期间，雇主有权提前三天通知终止雇佣关系，且不支付遣散费。但在这种情况下，雇主应能够证明雇员不适合该工作。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>加班支付是强制性的。工作时间超出标准工作时间视为加班。员工每周最多可以工作48小时的加班。员工每年最多可以工作180小时的加班。对于额外的工时，员工将按照其正常工资的1.5倍支付。</p><p>标准工作时间为每天八小时，每周40小时。标准工作周从星期一到星期五。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低毛月薪为924欧元。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计为员工工资的1.77%</p><ul><li>失业社会保障 &#8211; 1.31%</li><li>缴费基金 &#8211; 0.16%</li><li>缴纳长期就业基金 &#8211; 0.16%</li><li>工伤和职业病社会保险 &#8211; 0.14%</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>符合条件的终止包括：</p><ul><li>员工自愿离职</li><li>双方协议解除</li><li>雇主基于合法原因或无任何原因单方面解除<ul><li>试用期</li><li>客观原因</li><li>纪律性解雇</li><li>由于不适合工作而表现不佳</li><li>由于组织结构变化而使工作职能变得多余</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>通知期取决于终止的原因和雇佣期限。</p><p>雇主主动解除雇佣关系，但非因员工过错：</p><ul><li>标准通知期：一个月的通知期</li><li>雇佣期不足一年：两周通知期</li><li>上述通知期限乘以三倍适用于：<ul><li>抚养未满14岁（也包括领养）的未成年子女的员工</li><li>抚养未满18岁的残疾子女的员工</li><li>残疾员工</li><li>两年内将有资格获得退休金的员工</li></ul></li></ul><p>雇佣合同由雇主自行决定终止：</p><ul><li>给予员工三天的通知期</li><li>怀孕妇女，处于产假、陪产假或育婴假的员工不能根据此法律依据被解雇</li></ul><p>员工自行决定终止雇佣合同且没有合法原因：</p><ul><li>员工必须提交书面辞职，至少提前20天通知</li></ul><p>员工基于合法原因自行终止雇佣合同：</p><ul><li>员工不能为雇主设定较短的通知期</li><li>劳动法允许雇主和员工协商，员工在通知期内不工作，但将获得整个通知期的工资</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><ol start="1"><li>离职补偿金将取决于解雇的原因和雇佣期限。</li></ol><p>雇主主动解除雇佣关系，但非因员工过错：</p><ul><li>如果雇佣期超过一年 &#8211; 平均月工资的两倍</li><li>如果雇佣期不到一年 &#8211; 平均月工资的0.5倍</li><li>此外，被解雇的员工还将从特殊国家基金获得解雇补偿金。金额取决于雇佣持续时间</li></ul><p>雇佣合同由雇主自行决定终止：</p><ul><li>最低六个月的平均月工资</li></ul><p>员工基于合法原因自行终止雇佣合同：</p><ul><li>如果雇佣期超过一年 &#8211; 平均薪酬的两倍</li><li>如果雇佣期不到一年 &#8211; 平均薪酬的一倍</li></ul><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>工资支付每月不得少于两次，除非雇员和雇主双方同意按月支付。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>必须每月向员工提供详细的工资单，列明工资总额和净工资、任何扣除额以及加班费（如适用）的金额和用途。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>每周工作五天的全职员工每年有权获得20天的带薪休假（PTO），而每周工作六天的全职员工每年有权获得24天的带薪休假（PTO）。兼职员工有权获得20天的按比例计算的带薪休假。带薪休假每月累积1.67天。</p><p>在雇佣的第一年，所有年假通常在工作年的至少一半的工作日之后才授予。员工的要求下，连续不到六个月的工作时间也可以获得年假：</p><p>1）孕妇在或在产假期间之前</p><p>2）员工在孩子的产假期间，在陪产假之前或之后</p><p>3）在雇主的许可下</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>没有病假的最长天数限制。前三天由雇主支付，员工获得其工资的62.06%。三天后，员工将由国家社会保险基金委员会支付其工资的62.06%。</p><p>为了照顾患病的家庭成员或孩子的假期从第一天开始支付。员工在此期间获得其工资的65.94%。国家社会保险基金委员会负责支付此款项。</p><p>员工在以下情况下有资格获得病假福利：</p><ul><li>暂时无法工作</li><li>通过社会保险获得疾病保险</li><li>因疾病缺席工作并且因此丧失收入</li><li>在过去12个月中至少有三个月的疾病社会保险记录，或者在过去24个月中至少有六个月的记录</li><li>由于在26岁之前学习或从事专业军事服务或担任法定公务员而没有必要的疾病社会保险记录</li><li>在疾病结束后不超过12个月向地区国家社会保险基金委员会提交了福利申请</li></ul><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>怀孕员工在过去24个月中至少有12个月的产假社会保险记录的，有权获得126天的带薪休假。孩子出生前必须休息70天。其余的56天必须在孩子出生后休息。在此期间，员工将获得工资的77.58%，国家社会保险基金委员会将负责支付这笔款项。</p><p>在发生分娩并发症或多胞胎时，员工可以额外获得14天的带薪休假。</p><p>儿童保育津贴是支付给父母之一（领养父母）、其中一位祖父母或监护人照顾儿童的津贴。</p><p>它是根据收入计算的，并取决于所选择的时间尺度：</p><ul><li>参保员工选择在孩子满一岁之前获得津贴 &#8211; 工资的77.58%</li><li>员工选择在孩子满两岁之前获得津贴 &#8211; 第一年工资的54.31%，第二年工资的31.03%</li><li>在多胞胎情况下，津贴随着出生的婴儿数量而变化，但最高不超过工资的77.58%</li></ul><p>国家社会保险基金委员会将负责支付这笔款项。</p><p>在收养孩子期间，不管被收养的孩子的年龄如何，都可以选择支付12或24个月的儿童抚养津贴，直到孩子年满18岁。如果津贴为一年，则金额为工资的77.58%，如果为两年，则为54.31%。</p><p>国家社会保险基金委员会将负责支付这笔款项。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税为20%或32%。个人所得税采用统一税率计算。20%的个人所得税适用于2021年日历年内收入低于81,162欧元（60倍平均工资），超过81,162欧元的收入适用32%的个人所得税率。</p><h3 class="wp-block-heading">社会保险</h3><p>雇员享有国家社会保险，每月从工资中扣除一定比例作为缴费。社会保障服务由国家社会保险基金委员会 (SODRA) 管理。</p><figure class="wp-block-table"><table><tbody><tr><td></td><td> 雇主（%）</td><td>雇员（%）</td><td> 收入上限/年 （EUR）</td></tr><tr><td>Social Security</td><td>1.47 (comprising unemployment 1.31%, accidents at work and occupational diseases 0.14%)*</td><td>19.5 (comprising pension 8.72%, health insurance 6.98%, sickness 1.99%, maternity 1.81%)</td><td>114,162</td></tr><tr><td>Employment Fund</td><td>0.16</td><td></td><td></td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>Independence Day</td><td>2.16</td></tr><tr><td>Independence Restoration Day</td><td>3.11</td></tr><tr><td>Easter Sunday</td><td>3.31</td></tr><tr><td>Easter Monday</td><td>4.1</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Mother&#8217;s Day</td><td>5.5</td></tr><tr><td>Father&#8217;s Day</td><td>6.2</td></tr><tr><td>St.John&#8217;s Day</td><td>6.24</td></tr><tr><td>King Mindaugas&#8217;Day</td><td>7.6</td></tr><tr><td>Assumption Day</td><td>8.15</td></tr><tr><td>All Saints&#8217;Day</td><td>11.1</td></tr><tr><td>All Souls&#8217;Day</td><td>11.2</td></tr><tr><td>Christmas Eve</td><td>12.24</td></tr><tr><td>Christmas Day</td><td>12.25&nbsp;</td></tr><tr><td>Second Day of Christmas</td><td>12.26&nbsp;</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>