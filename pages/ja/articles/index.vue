<!--
 * @Author: sx <EMAIL>
 * @Date: 2023-01-10 09:55:23
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-16 18:08:43
 * @FilePath: \bpo-website-pc\pages\zh\contries\canada.vue
 * @Description: これはデフォルト設定です。`customMade`を設定し、koroFileHeaderを開いて設定を確認してください: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
mixin contact-us
  .contact-form
    //- 傍観するには代償が必要、そうでなければページ遷移に影響する
    //- https://github.com/element-plus/element-plus/pull/9731
    client-only
      el-dialog(v-model="status.showForm" title="" :width="600")
        contact-us-form(@submit="submitSuccess" lang="ja")
.contries-page
  header
    site-header(
      lang="ja"
      source="countries"
      :showContactUs="false")
    .header-banner
      .header-banner-text
        h1.header-title 100カ国以上で人材を採用
        p.header-desc 以下の国別ガイドで、雇用契約、試用期間、解雇規制、税金と社会保障、有給休暇、ビザ、祝日に関する詳細情報をご確認ください。
        button.header-contact(@click="status.showForm = true") お問い合わせ
      .header-banner-image
        img(src="~/assets/images/countries/country-guide-header.png")
  .country-index-main
    h2 SmartDeer リソース
    p.sub-desc HR、人材管理、給与計算に関するすべてのことをお手伝いします。役立つ記事、ガイド、テンプレート、ツールなどをご覧ください。
    .search-bar
      input.text-search(placeholder="記事を検索" v-model="form.keyword" @keyup.enter="searchKeyword")
      button.btn-search(@click="searchKeyword") 検索
    ul.country-list
      a(v-for="(item, index) in paginator.currentPageData" :href="item.link")
        li
          .img-container
            img(:src="item.image.node.sourceUrl" width="100%")
          .link-container {{ item.title }}
  +contact-us
  site-footer(lang="ja" @contact-us="handleScrollToTop")

</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
import {onMounted} from "vue";
import {ceil} from "lodash-es";
import articleList from "./article-list-ja.json";

definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '100カ国以上で人材を採用 - SmartDeer',
  ogDescription: 'SmartDeerでは、世界中の20のSmartDeer所有エンティティが従業員の採用をシームレスにしています。まだエンティティを所有していない国では、豊富な経験を持つトップパートナーと協力して、必要な場所で採用できるようサポートしています。以下で、パートナー、エンティティ、サポートがある国を人気順にご確認いただけます。',
  ogSiteName: 'SmartDeer',
  description: 'SmartDeerでは、世界中の20のSmartDeer所有エンティティが従業員の採用をシームレスにしています。まだエンティティを所有していない国では、豊富な経験を持つトップパートナーと協力して、必要な場所で採用できるようサポートしています。以下で、パートナー、エンティティ、サポートがある国を人気順にご確認いただけます。'
})
useHead({
  htmlAttrs: {
    lang: 'ja'
  },
  title: '100カ国以上で人材を採用 - SmartDeer'
})
const status = reactive({
  showForm: false
})
const form = reactive({
  keyword: ''
})
const paginator = reactive({
  currentPage: 1,
  pageSize: 12,
  totalPages: 0,
  allArticles: articleList,
  filteredArticles: [],
  currentPageData: []
})

function nextPage() {
  if (paginator.currentPage < paginator.totalPages) {
    paginator.currentPage++;
  }
  freshCurrentPageData();
}

function previousPage() {
  if (paginator.currentPage > 1) {
    paginator.currentPage--;
  }
  freshCurrentPageData();
}

function searchKeyword() {
  if (!form.keyword) {
    paginator.filteredArticles = paginator.allArticles;
  } else {
    const searchKeywordLower = form.keyword.toLowerCase();
    paginator.filteredArticles = paginator.allArticles.filter(item => {
      return JSON.stringify(item).toLowerCase().includes(searchKeywordLower);
    });
  }
  paginator.currentPage = 1
  freshCurrentPageData()
}

function freshCurrentPageData() {
  if (!form.keyword) {
    paginator.filteredArticles = paginator.allArticles
  }
  const start = (paginator.currentPage - 1) * paginator.pageSize
  const end = paginator.currentPage * paginator.pageSize
  paginator.currentPageData = paginator.filteredArticles.slice(start, end)
  paginator.totalPages = ceil(paginator.filteredArticles.length / paginator.pageSize)
}

function submitSuccess() {
  ElMessage.success('お問い合わせを受け付けました。できるだけ早くご連絡いたします。')
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}

onMounted(() => {
  freshCurrentPageData()
})
</script>

<style lang="scss" scoped>
@import './article-index.scss';
</style>