.countries {
  width: 1204px;
  margin: 0 auto;

  .countries-banner {
    min-height: 272px;
    position: relative;
    z-index: 10;

    .banner {
      width: 100%;

      figure,img {
        width: 100%;
        height: 100%;
        display: block;
        border-radius: 8px;
      }
    }

    .flag {
      width: 200px;
      // height: 105px;
      position: absolute;
      padding: 4px;
      left: 24px;
      bottom: -24px;
      border-radius: 8px;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.03);
      background: #fff;
      box-sizing: content-box;

      img {
        width: 100%;
        height: 100%;
        display: block;
        border-radius: 8px;
      }
    }
  }

  .countries-content {
    position: relative;
    padding-top: 44px;
    padding-bottom: 81px;
    font-size: 18px;

    h1.article-title {
      width: 650px;
      font-size: 32px;
      font-weight: bold;
      color: #111111;
      line-height: 38px;
    }

    .wp-block-heading {
      font-size: 24px;
      margin: 20px auto;
    }
    .wp-block-image {
      width: 650px;
      img {
        width: 100%;
        height: auto;
      }
    }

    .article-content {
    }
    .form-box {
      position: relative;
      z-index: 2000;
      margin: -38px 20px 20px;
      float: right;
      width: 436px;
      // height: 594px;
      background: #FFFFFF;
      box-shadow: 0px 0px 16px 0px rgba(0,0,0,0.06);
      border-radius: 4px;
      border: 1px solid rgba(151,151,151,0.2);
      // bottom: -402px;

      .contact-us-form {
        .form-title {
          padding-top: 32px;
          margin-top: 0;
          font-size: 24px;
          text-align: left;
          padding-left: 24px;
          margin-bottom: 17px;
        }

        .form-body {
          padding: 0 24px;
        }
      }
    }
    &::after {
      content: '';
      display: block;
      width: 0;
      height: 0;
      clear: both;
    }

    p {
      margin: 20px 0;
    }

    li {
      margin: 0;
      list-style: none;
      line-height: 32px;
      position: relative;
      padding-left: 15px;

      &::before {
        content: '';
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #FF8600;
        position: absolute;
        left: 0;
        top: 14px;
      }
    }

    ul, ol {
      padding: 0;
    }

    table {
      width: auto;
    }

    table, th, td{
      border: 1px #D8D8D8 solid;
      border-collapse: collapse;
    }

    td {
      padding: 8px;
      line-height: 26px;
    }
  }
}