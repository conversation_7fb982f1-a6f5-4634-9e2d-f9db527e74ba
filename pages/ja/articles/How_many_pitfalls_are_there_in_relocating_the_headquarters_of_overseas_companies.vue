<template lang="pug">
.contries-page
  site-header(lang="ja" :showContactUs="false")

  .countries
    .countries-content
      h1.article-title 出海企业总部迁移到底有多少坑？
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="ja" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="ja" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '出海企业总部迁移到底有多少坑？',
  ogDescription: '出海企业总部迁移到底有多少坑？最近我们smartdeer一位客户在美国迁移了总部，建立了新主体同时需要迁移销售 […]',
  ogSiteName: 'SmartDeer',
  description: '出海企业总部迁移到底有多少坑？最近我们smartdeer一位客户在美国迁移了总部，建立了新主体同时需要迁移销售 […]'
})
useHead({
  htmlAttrs: {
    lang: 'ja-JP'
  },
  title: '出海企业总部迁移到底有多少坑？'
})
const status = reactive({
  showForm: false
})
const langSimple = 'ja';
const pageTitle = '出海企业总部迁移到底有多少坑？';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/08/WechatIMG1014.png';
const flagImage = '';
const htmlContent = '<p>出海企业总部迁移到底有多少坑？最近我们smartdeer一位客户在美国迁移了总部，建立了新主体同时需要迁移销售团队，在这个过程中遇到了棘手的困难，不知道该怎么走程序，对他们而言难点有二，一是新总部所在的州是美国比较小众的一个州，二是客户在海外未设立HR部门。导致客户对当地的税法、人力雇佣流程、Payroll服务上都不太清晰明确，急需专业的技术支持，于是找到我们smartdeer，我们的ICB给客户提供完整的企业各项账户开户，为客户员工顺利入职提前做好支持，入职后为客户提供完整的Global Payroll服务，并且在交付过程中时刻提醒客户遵守当地法律法规，避免违规行为。最终帮助客户承担了HR部门海外业务70%以上的HR工作量，以及节省超过50%以上海外部署HR团队的成本，在企业总部的迁移过程中帮他们避了很多坑，节约了时间，也节约了巨额的成本。</p>';
function submitSuccess() {
  ElMessage.success('お問い合わせを受け付けました。できるだけ早くご連絡いたします。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>