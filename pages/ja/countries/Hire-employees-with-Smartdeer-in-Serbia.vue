<template lang="pug">
.contries-page
  site-header(lang="ja" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title Employer Record in Serbia
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="ja" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="ja" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'Employer Record in Serbia',
  ogDescription: 'Basic information Capital: Belgrade Time zone: GMT+1 La […]',
  ogSiteName: 'SmartDeer',
  description: 'Basic information Capital: Belgrade Time zone: GMT+1 La […]'
})
useHead({
  htmlAttrs: {
    lang: 'ja-JP'
  },
  title: 'Employer Record in Serbia'
})
const status = reactive({
  showForm: false
})
const langSimple = 'ja';
const pageTitle = 'Employer Record in Serbia';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/R-C-15.jpeg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/t01da743a2f61333335-1.png';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">Basic information</h2><p>Capital: Belgrade</p><p>Time zone: GMT+1</p><p>Languages: Serbian</p><p>Currency code: RSD</p><h2 class="wp-block-heading has-large-font-size">Human Resources at a Glance </h2><h3 class="wp-block-heading has-large-font-size">Contract Terms</h3><p>Contracts must be in Serbian and can be bilingual. Bonuses should be noted as an Annex. They must be in writing and signed by both parties.<br>A contract must include:</p><ul><li>Name and last name of the employee</li><li>Address from the ID</li><li>JMBG (personal Citizen Number)</li><li>Level of education</li><li>Occupation based on education</li><li>Employer&#8217;s name</li><li>Employers&#8217; PIB and MB number</li><li>Employers address</li><li>Name of the employer&#8217;s legal representative</li><li>Start date</li><li>Length of the employment</li><li>Job description</li><li>Job title in Serbian corresponding to the Serbian codebook of occupations</li><li>Working hours</li><li>Holiday pay</li><li>Bonuses (Annex)</li><li>Termination conditions</li><li>Notice period</li></ul><h2 class="wp-block-heading has-large-font-size">Guidelines Regarding Probation Period/Trial Period</h2><p>Probation periods are not mandatory. The maximum probation period is 180 days.</p><h2 class="wp-block-heading has-large-font-size">Regulations and Rules Regarding Working Hours</h2><p>Standard working hours are 8 hours per day and 40 hours per week from Monday to Friday.</p><h2 class="wp-block-heading has-large-font-size">Overtime is paid according to the following rates</h2><p>Overtime payment is not mandatory and may be included in the salary. Overtime may also be paid as leave in-lieu. Hours outside of standard work hours are considered overtime. For additional hours, employees are paid:</p><ul><li>126% of the hourly rate, for work from 22:00-06:00</li><li>126% of the hourly rate, for work outside of normal hours.</li><li>210% of the hourly rate, for work on a public holiday.</li><li>For each completed year spent in employment with the Employer in the amount of 0.4% of the base salary.</li></ul><h2 class="wp-block-heading has-large-font-size">Minimum Wage Requirements</h2><p>The national minimum wage for full-time employees is:</p><figure class="wp-block-table"><table><tbody><tr><td>2023</td><td>2024</td></tr><tr><td>RSD 230 per hour</td><td>RSD 271 per hour</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Payroll Cost for Employers</h2><p>The employer cost is generally estimated at 17% of the employee salary.</p><ul><li>Pension Insurance &#8211; 11.50%</li><li>Health Insurance &#8211; 5.15%</li><li>Meal Allowance &#8211; 500RSD</li><li>Employment Agreement Handling Fee &#8211; 80 USD<br>The maximum salary for the calculation of pension and health insurance is RSD 500,360.</li></ul><h2 class="wp-block-heading has-large-font-size">Termination </h2><h3 class="wp-block-heading has-large-font-size">Grounds</h3><p>Terminations must respect complex rules and the rules of an employee’s employment country. The off-boarding is always handled by the Employer with the primary stakeholders. It may include ad-hoc fees as well as required or recommended steps on specific termination cases.<br>Terminations in Serbia can be complex. There is no at-will termination in Serbia for employers and termination must be done for just cause.<br>Compliant terminations include:</p><ul><li>Voluntarily by the employee</li><li>By mutual agreement</li><li>Unilaterally by the employer based on:<ul><li>Probation period</li><li>Objective grounds</li><li>Disciplinary dismissal</li><li>Performance due to unsuitability for the job</li></ul></li><li>By the expiration of the contract</li></ul><h2 class="wp-block-heading has-large-font-size">Notice Period</h2><p>If an employee is being terminated due to breach of work duty or inability or lack of qualifications, they have the right to continue working for a notice period of between 5 and 30 days (depending on the period of paid pension insurance).<br>Otherwise, the notice period is as follows:<br>During probation period: At least 5 days<br>After probation period: Between 15 and 30 days</p><h2 class="wp-block-heading has-large-font-size">Severance compensation</h2><p>In Serbia, all employees who are terminated due to redundancy are entitled to severance pay. Severance pay is 1/3 of the employee&#8217;s monthly salary per year of service.</p><h2 class="wp-block-heading has-large-font-size">Payment &amp; leave Compensation &amp; Holidays </h2><h3 class="wp-block-heading has-large-font-size">SalaryPayment</h3><p>Monthly</p><h2 class="wp-block-heading has-large-font-size">Payslip Payroll</h2><p>No explicit provision</p><h2 class="wp-block-heading has-large-font-size">Annual Leave</h2><p>Both full-time and part-time employees are entitled to 20 working days of paid time off (PTO) a year. PTO accrues monthly at 1.67 days per month. Employees are eligible for annual leave after 1 month of work.</p><h2 class="wp-block-heading has-large-font-size">Sick Leave</h2><p>Sick leave in Serbia is complex. Employees are entitled to paid sick leave for up to 12 months. This leave is paid at different rates and by different payers depending upon length and type of sickness:</p><figure class="wp-block-table"><table><tbody><tr><td>Period</td><td>Pay</td><td>Payer</td></tr><tr><td>0 &#8211; 30 days</td><td>65% of the base salary</td><td>Employer</td></tr><tr><td>30 days &#8211; 12 months. Can be less depending on the sickness</td><td>Depending on Sickness</td><td>Social Security</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Maternity &amp; Parental Leave</h2><p>Pregnant employees are entitled to 12 months of paid leave. 28 days must be taken before the child&#8217;s birth. The employee will receive different levels of salary during the leave:</p><figure class="wp-block-table"><table><tbody><tr><td>Period</td><td>Pay</td><td>Payer</td></tr><tr><td>0 &#8211; 30 days</td><td>100%</td><td>Employer</td></tr><tr><td>30 days &#8211; 12 months</td><td>Average salary from the last 18 months</td><td>Social Security</td></tr></tbody></table></figure><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Employees are entitled to 9-21 months of paid paternity leave. The length of the leave depends on the employer. Additionally, employees are eligible for another 5 days of company-paid leave when their legal spouse gives birth. This leave is paid for by the government at 100% of the employee’s salary.</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;Wf6odPhX9oUtDvxoLN3cqgrinpb&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;Employees are entitled to 9-21 months of paid paternity leave. The length of the leave depends on the employer. Additionally, employees are eligible for another 5 days of company-paid leave when their legal spouse gives birth. This leave is paid for by the government at 100% of the employee’s salary.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+8d&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:37,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:301},&quot;recordId&quot;:&quot;F2PWdcfccouOyqxEsabcnap0n2g&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Tax and Social Security Information</h2><h2 class="wp-block-heading has-large-font-size">Personal Income Tax</h2><p>Income taxes are collected by the Tax Administration according at a flat rate of 10%.</p><p>Taxpayers with annual incomes of 3 or more times the average annual salary are subject to supplementary taxes of between 10 and 15%.</p><p>Taxes are offset by a personal tax deduction equal to 40% of the average annual salary. Additional tax relief applies to taxpayers with dependents.</p><h2 class="wp-block-heading has-large-font-size">Social security</h2><ul><li>Meal Allowance</li><li>Public Health Insurance</li><li>Social Security</li><li>Disability Insurance</li></ul><h2 class="wp-block-heading has-large-font-size">Public Holidays 2024</h2><figure class="wp-block-table"><table><tbody><tr><td>Holidays</td><td>Date</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1-1.2</td></tr><tr><td>Christmas Day</td><td>1.7</td></tr><tr><td>Statehood Day of the Republic of Serbia</td><td>2.15-2.16</td></tr><tr><td>Labour Holiday</td><td>5.1-5.2</td></tr><tr><td>Good Friday</td><td>5.3</td></tr><tr><td>Holy Saturday</td><td>5.4</td></tr><tr><td>Easter Day</td><td>5.5-5.6</td></tr><tr><td>Armistice Day</td><td>11.11</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('お問い合わせを受け付けました。できるだけ早くご連絡いたします。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>