<template lang="pug">
.contries-page
  site-header(lang="ja" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title SmartDeer-Philippines-Guide
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="ja" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="ja" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'SmartDeer-Philippines-Guide',
  ogDescription: 'Connect with your dedicated advisor instantly and stay […]',
  ogSiteName: 'SmartDeer',
  description: 'Connect with your dedicated advisor instantly and stay […]'
})
useHead({
  htmlAttrs: {
    lang: 'ja-JP'
  },
  title: 'SmartDeer-Philippines-Guide'
})
const status = reactive({
  showForm: false
})
const langSimple = 'ja';
const pageTitle = 'SmartDeer-Philippines-Guide';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2025/07/image-4.png';
const flagImage = '';
const htmlContent = '<p class="has-text-align-center"><img loading="lazy" decoding="async" width="150" height="150" class="wp-image-1815" style="width: 150px;" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250723033911.png" alt=""></p><p class="has-text-align-center has-accent-4-color has-text-color has-link-color has-small-font-size wp-elements-27235060651842d1eaed78ce466d1434"><strong>Connect with your <strong>dedicated advisor</strong> instantly and stay informed on the latest regulatory trends.</strong></p><p><br><strong>SmartDeer has launched the “China Overseas Business Guide – Philippines Edition”, offering a comprehensive breakdown of key employment compliance insights in the Philippines.</strong>From statutory benefits and salary payments to dismissal regulations, the guide provides practical hiring strategies and localized incentive policies to empower your team’s localization and efficient management. Master local labor laws and seize billion-dollar opportunities in the Philippine market.</p><p><strong>More country/region guides are coming soon!</strong></p><p><br>Stay ahead of the curve with our hands-on overseas expansion resources.<br>👉 Add our assistant now to get instant access to the full series and kickstart your efficient global journey.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="1070" height="1390" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/image-4.png" alt="" class="wp-image-1893"/></figure><p><strong>We&#8217;re Just Getting Started</strong> 🔜</p><p>As a one-stop HR service platform, <strong>SmartDeer is committed to empowering global expansion</strong> by offering comprehensive human resources solutions for businesses going overseas.</p><p>SmartDeer will continue to unlock more <em>Global Expansion Guides</em>, helping you <strong>stay ahead in your global strategy</strong>!</p><p>⭐️ <strong>Scan the QR code now</strong> to add our [<strong>Overseas Assistant</strong>] and reply with &#8220;<strong>Philippines</strong>&#8221; to get your <strong>FREE guide</strong> instantly.<br>You&#8217;ll also receive real-time updates on <strong>policy changes and industry insights</strong>.</p><p class="has-text-align-center"><img loading="lazy" decoding="async" width="150" height="150" class="wp-image-1815" style="width: 150px;" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250723033911.png" alt=""></p><p class="has-text-align-center has-accent-4-color has-text-color has-link-color has-small-font-size wp-elements-12ff05b9ad2163e6c0e9ebc93ca6b6bd">Connect with your <strong>dedicated advisor</strong> instantly and stay informed on the latest regulatory trends.</p><p></p>';
function submitSuccess() {
  ElMessage.success('お問い合わせを受け付けました。できるだけ早くご連絡いたします。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>