<template lang="pug">
mixin header
  header
    site-header(
      lang="ja" 
      source="home" 
      @showForm="()=>{status.showForm = true}"
      @scrollTo="scrollTo"
    )

    .header-banner
      .header-banner-text
        h2.slogon グローバル採用・海外雇用支援
        h1.title 海外人材採用・雇用のワンストップサービス
        .desc 
          span 世界中から優秀な人材を採用し、各国の雇用・給与に関するコンプライアンス対応を含む、プロフェッショナルな人事ソリューションをワンストップで提供します。
          figure
              img(src="~/assets/images/index/global-desc.webp")
      .header-banner-image
        figure.global-min_1
          img(src="~/assets/images/index/global-min_1.png")
        figure.global-min_2
          img(src="~/assets/images/index/global-min_2.png")
        figure.global-min_3
          img(src="~/assets/images/index/global-min_3.png")

mixin customer
  section.customer
    .section-title
      h2 世界中のお客様をサポート
    
    CustomerList
    
mixin service
  section.service
    .section-title
      h2 グローバル人事サービス & SaaSソリューション
    .service-list
      .service-item(id="service-recruitment" v-scroll-show="{ delayOffset: 100 }")
        .service-content
          .service-title
            h3 グローバル採用支援
          .service-desc
            p グローバルに人材を採用したい企業様に向けて、経験豊富な専門チームがターゲット市場でのスムーズな採用をサポートします。
            p 豊富な人材データベースと熟練のヘッドハンティングチームにより、採用から報酬管理まで効率的に対応。24時間365日のサポート体制で、運用も安心です。
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") 詳細情報をリクエストする
              ArrowRight(class="inline-arrow")
        .figure-area
          figure
            img(src="~/assets/images/index/recruitment.webp" alt="Global Recruitment")
      .service-item(id="service-eor" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/eor.webp" alt="EOR")
        .service-content
          .service-title
            h3 海外雇用代行（EOR）サービス
          .service-desc
            p 海外での法人設立が不要。現地法に準拠しつつ、コストを抑えた正社員採用が可能です。
            p コンプライアンス対応、ビザ取得、入社手続き、チーム管理、給与計算、税務、福利厚生まで、すべてを一括サポート。自動化ツールによりレポート業務も効率化し、スムーズな運用を実現します。
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") 詳細情報をリクエストする
              ArrowRight(class="inline-arrow")
      .service-item(id="service-contractor" v-scroll-show="{ delayOffset: 100 }")
        .service-content
          .service-title
            h3 業務委託契約（フリーランス）支援
          .service-desc
            p 新たな市場で短期的かつ柔軟な雇用形態をご希望ですか？現地に法務チームがなくても、迅速に業務委託人材の確保を支援します。  
            p 各国法に準拠した契約テンプレートとスムーズなオンライン締結プロセスを提供。リスク回避のためのバックグラウンドチェックや、明瞭な料金体系による報酬支払いサービスも完備しています。
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") 詳細情報をリクエストする
              ArrowRight(class="inline-arrow")
        .figure-area
          figure
            img(src="~/assets/images/index/contractor.webp" alt="Contractor")

      .service-item(id="service-peo" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/peo.webp" alt="Human Resource Outsourcing")
        .service-content
          .service-title
            h3 人事業務アウトソーシング
          .service-desc
            p 人事業務の負担を軽減したい現地法人設立済みの企業様へ。給与計算、税務対応、社会保険、福利厚生、入社手続きなどを一括して代行します。
            p 貴社のニーズに合わせて柔軟にカスタマイズ可能。各国の法令遵守を徹底し、業務効率化とコスト削減を同時に実現します。
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") 詳細情報をリクエストする
              ArrowRight(class="inline-arrow")
      .service-item(id="service-fintech" v-scroll-show="{ delayOffset: 100 }")
        .service-content
          .service-title
            h3 FinTech活用のグローバル給与ソリューション
          .service-desc
            p SmartDeerのプラットフォームは、150種類以上の通貨に対応し、世界中の従業員や業務委託者への給与をスムーズかつ効率的に支払うことができます。
            p 競争力のある為替レート、強力な為替リスクヘッジ機能、一括支払い機能により、国際送金コストを大幅に削減し、為替変動による給与コストの影響を最小限に抑えます。
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") 詳細情報をリクエストする
              ArrowRight(class="inline-arrow")
        .figure-area
          figure
            img(src="~/assets/images/index/fintech.png" alt="FinTech-Driven Global Payroll Solutions")

      .service-item(id="service-saas" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            video(id="video-player" class="video-js" preload="auto" controls="true")
              source(src="https://static.smartdeer.com/Global_HR_SaaS.mp4", type="video/mp4")
        .service-content
          .service-title
            h3 グローバル人事SaaSプラットフォーム
          .service-desc
            p SmartDeerは、「人事サービス＋SaaSシステム」モデルを通じて、グローバル展開を目指す企業に対し、専門的かつ統合的なソリューションを提供します。SmartDeerのグローバル人事SaaSプラットフォームは、データに基づいたデジタル化された人事管理を実現し、企業のグローバルな人事戦略を力強く支援します。
            p 世界中の従業員、人事チーム、マネージャーが各種人事業務や関連タスクを効率的に管理できるよう支援します。人事データを一元管理することで、業務の効率化と最適化を実現します。
          .service-contact
            button(class="service-contact-button" @click="status.showForm=true") 詳細情報をリクエストする
              ArrowRight(class="inline-arrow")
mixin advantage
  section.advantages
    .section-title
      h2 SmartDeerが選ばれる理由

    .advantage-list(v-scroll-show="{ delayOffset: 100 }")
      .advantage-item(style="transition-delay:0s;")
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-global.svg" alt="Global Connected")
        .advantage-title グローバルネットワーク
        .advantage-content 150カ国以上に対応、24時間365日体制でビジネスをサポート。

      .advantage-item(style="transition-delay:0.4s;")
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-professional-team.svg" alt="Compliance-ready")
        .advantage-title コンプライアンス対応
        .advantage-content 各国の法令やデータ保護規制を、あらゆるプロセスで確実に遵守。

      .advantage-item(style="transition-delay:.8s;")
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-service.svg" alt="24-hours service response")
        .advantage-title 24時間サポート
        .advantage-content 中国語・英語対応のバイリンガルサポートで迅速な対応を実現。

      .advantage-item(style="transition-delay:1.2s;")
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-price.svg" alt="Competitive Service Fee")
        .advantage-title 競争力ある価格設定
        .advantage-content 高品質なサービスを最適なコストでご提供。

mixin lifecycle
  section.lifecycle()
    .section-title
      h2 人材ライフサイクルの一括管理
    .lifecycle-list(v-scroll-show="{ delayOffset: 100 }")
      .lifecycle-item(style="transition-delay: .1s")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/recruitment.svg")
        .lifecycle-item-title 採用


      .lifecycle-item(style="top:20px;transition-delay: .2s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/compliance.svg")
        .lifecycle-item-title コンプライアンス対応

      .lifecycle-item(style="top:40px;transition-delay: .3s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/contract.svg")
        .lifecycle-item-title  契約締結

      .lifecycle-item(style="top:60px;transition-delay: .4s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/on-boarding.svg")
        .lifecycle-item-title 入社手続き

      .lifecycle-item(style="top:40px;transition-delay: .5s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/management.svg")
        .lifecycle-item-title 従業員管理

      .lifecycle-item(style="top:20px;transition-delay: .6s;")
        .lifecycle-item-icon
          .arrow
          figure 
            img(src="~/assets/images/index/payment.svg")
        .lifecycle-item-title 給与支払い
      
      .lifecycle-item(style="transition-delay: .7s;")
        .lifecycle-item-icon
          figure 
            img(src="~/assets/images/index/off-boarding.svg")
        .lifecycle-item-title 退職・契約終了対応

    .lifecycle-repeat
      figure
        img(src="~/assets/images/index/repeat.svg" alt="repeat")

mixin process
  section.process()
    .section-title
      h2 SmartDeerの採用支援の仕組み

    .process-list
      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-right
          .process-num 01

        .process-content-wrapper
          .process-content
            .process-title
              h3 グローバルなサービス提供 × 業界特化型のローカライズ採用支援
            .process-desc
              p 中国、東南アジア、北米、中東などに80名以上の採用コンサルタントを擁し、IT・SaaS・ゲーム・スマート製造など多様な業界に特化。オペレーション、事業開発、営業、エンジニア職まで、企業ニーズに応じたグローバルな人材採用を支援します。
          .figure-area
            figure
              img(src="~/assets/images/index/jp/recriument.png" alt="中国、東南アジア、北米、中東などに80名以上の採用コンサルタントを擁し、IT・SaaS・ゲーム・スマート製造など多様な業界に対応。オペレーション・営業企画・エンジニア職種まで、企業ニーズに応じたグローバル採用サービスをご提供します。")

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-left
          .process-num 02

        .process-content-wrapper 
          .figure-area
            figure
              img(src="~/assets/images/index/jp/compliance.png" alt="ビザ取得支援・コンプライアンス対応")
          .process-content
            .process-title
              h3 ビザ取得支援・コンプライアンス対応
            .process-desc
              p ビザ取得支援からバックグラウンドチェック、各国の法令対応まで、ワンストップで対応。スムーズな入社手続きを実現し、法的リスクの最小化と安全な雇用をサポートします。
              
      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-right
          .process-num 03

        .process-content-wrapper
          .process-content
            .process-title
              h3 契約テンプレートを選びカスタマイズすれば、作成から電子署名までわずか3分で完了
            .process-desc
              p ビジネスモデルや雇用形態に応じて、コンプライアンス対応済みの契約テンプレートを選択。作成から電子署名まで、オンライン上で3分以内に完結します。

          .figure-area
            figure
              img(src="~/assets/images/index/jp/contract.png" alt="ビジネスモデルや雇用形態に応じて、コンプライアンス対応済みの契約テンプレートを選択。作成から電子署名まで、オンライン上で3分以内に完結します。")

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-left
          .process-num 04

        .process-content-wrapper 
          .figure-area
            figure
              img(src="~/assets/images/index/jp/staff.png" alt="自動監査でオンボーディングを効率化")
          .process-content
            .process-title
              h3 自動監査でオンボーディングを効率化
            .process-desc
              p 従業員情報をアップロードするだけで、自動的にプラットフォームが審査を実行。オンラインオンボーディングシステムにより、迅速な承認が可能となり、スムーズに業務を開始できます。

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-right
          .process-num 05

        .process-content-wrapper
          .process-content
            .process-title
              h3 オンライン契約管理、従業員記録、休暇・勤怠管理
            .process-desc
              p 契約進捗を容易に追跡、従業員記録を管理し、人事異動を監視。複数の打刻方法をサポートし、承認プロセスを自動化し、勤怠をリアルタイムで更新。

          .figure-area
            figure
              img(src="~/assets/images/index/jp/progress.png" alt="オンライン契約管理、従業員記録、休暇・勤怠管理")

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-left
          .process-num 06

        .process-content-wrapper 
          .figure-area
            figure
              img(src="~/assets/images/index/jp/pay.png" alt="報酬・給与・保険処理の一括対応")
          .process-content
            .process-title
              h3 報酬・給与・保険処理の一括対応
            .process-desc
              p セルフサービスツールにより、経費精算・手当申請・承認プロセスを効率化。地域ごとの法規制に準拠しながら、給与管理を自動化。複数通貨での支払いにも対応し、透明性のある為替レートで運用可能です。

      .process-item(v-scroll-show="{ delayOffset: 180 }")
        .process-background.layout-right
          .process-num 07

        .process-content-wrapper
          .process-content
            .process-title
              h3 退職手続きと最終清算の効率的な一元管理
            .process-desc
              p 退職申請のスケジューリングから手続きの自動化、最終支払いまでをプラットフォーム上で一元管理。退職プロセスの各段階で、現地法令への確実な準拠を保証します。

          .figure-area
            figure
              img(src="~/assets/images/index/jp/dimission.png" alt="退職申請のスケジューリングから手続きの自動化、最終支払いまでをプラットフォーム上で一元管理。退職プロセスの各段階で、現地法令への確実な準拠を保証します。")

mixin solutionCase
  section.solution(id="solution")
    .section-title
      h2 ソリューション事例
    .solution-list
      .solution-item.case1-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case1.png')
          .solution-content
            .solution-title
              h3 クライアント成功事例：中国大手ICTソリューション企業におけるグローバルチーム拡大と管理のベストプラクティス
            .solution-desc(:style="getSolutionDescStyle('s1')")
              h4 背景と課題
              p 2020年にグローバル展開を開始して以来、30以上の国・地域に進出し、以下のような具体的な課題に直面しました。
              ol
                li #[strong 海外雇用：] 16カ国でのEORおよびビザ手続き、5カ国での人事アウトソーシング対応。
                li #[strong 雇用コスト算定：] 複数国における正確な雇用コスト算出とコンプライアンス対応に関する専門的アドバイス。
                li #[strong ポリシーアドバイザリーと迅速な対応：] 変化する各国の雇用政策に即応可能な体制づくり。
              h4 直面した主な課題：
              ul
                li #[strong 複雑な事務処理：] 海外人事チームは、迅速かつ正確な対応を要する日々の問合せ業務に追われていました。
                li #[strong ビザ申請の難航：] 対象国ではリスクが高く、複雑なビザ申請手続きが必要で、企業担当者に大きな負担がかかっていました。
                li #[strong 精算業務の集中：] 各部門から大量の申請が集中し、人事・経理部門の負担が増大していました。
              h4 SmartDeerが提供したソリューション
              p グローバル展開に伴う課題に対応するため、SmartDeerは以下の包括的ソリューションを構築・提供しました。
              ol
                li #[strong EORおよびHROサービス]
                  ul
                    li 16か国においてEOR（Employer of Record）サービスおよびビザ申請支援を実施。
                    li 5か国での現地化されたHRO（Human Resources Outsourcing）サービスを提供。給与計算、福利厚生、労働契約管理などをカバー。
                li #[strong リアルタイム・ポリシーアドバイザリー]
                  ul
                    li SmartDeerの専門チームが、各国の雇用・税制ポリシーについてリアルタイムに助言。採用戦略の迅速な最適化を支援。
                li #[strong ビザ申請サポートサービス]
                  ul
                    li 書類作成から提出まで、ビザ申請を一括でサポート。特に手続きの複雑な国を重点的にサポートし、ビザ承認率の向上を実現。
                li #[strong データ管理の自動化]
                  ul
                    li 自社プラットフォームによるデータの自動入力・分析により、精算業務のフローを最適化し、手作業の負担を軽減。
                li #[strong 迅速対応を実現するサポート体制]
                  ul
                    li 専任アカウントマネージャーと24時間365日のサポートチームを配置し、複雑な課題にも最大24時間以内で対応できる体制を構築。
              h4 導入効果
              ul
                li #[strong グローバル業務の効率化：] 16か国でのチーム採用およびビザ取得をサポートし、スムーズな市場進出を実現。
                li #[strong 時間コストを40％削減：] 自動化により人事部門の管理業務を大幅に削減。
                li #[strong ビザ承認率95％：] 高難度地域においても専門サポートで高い承認率を達成。
                li #[strong 精算処理効率が50％向上：] データの自動処理により、各部門の精算業務を大幅に最適化。
              h4 お客様の声
              p 「SmartDeerは、当社のグローバル拡大において包括的な支援を提供してくれました。雇用サービスからビザ取得に至るまで、高い専門性と効率性を発揮。国際業務における戦略的パートナーとして不可欠です。」 -— #[strong 国際事業部長]
            .solution-expand
              button(@click="toggle('s1')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s1") 拡大
                span(v-else) 折りたたみ
                ArrowDown(v-if="!solutionExpandStatus.s1" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case2-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-content
            .solution-title
              h3 中国大手ゲーム開発企業、日本市場での成功事例
            .solution-desc(:style="getSolutionDescStyle('s2')")
              h4 背景
              p 2022年に日本法人を設立したこの新興の中国系ゲーム企業は、2023年より本格的に日本市場への進出を開始。初の海外展開として、迅速な現地チーム構築や、日本の厳格な労働法・複雑な人事要件への対応という大きな課題に直面しました。
              h4 具体的な課題：
              ul
                li #[strong 現地人事の専門知識不足：] 日本の労務コンプライアンスや従業員管理に精通した社内人事チームが存在しませんでした。
                li #[strong 複雑な労働法規制：] 日本の厳格な労働関連法により、あらゆる人事管理業務で法令順守が求められ、法的リスク回避のための精密な運用が必要でした。
              h4 SmartDeerが提供したソリューション
              p SmartDeer 针对企业的初期需求和痛点，提供了全面且定制化的解决方案：
              ol
                li #[strong 包括的なHROサービス]
                  ul
                    li 地元規制に準拠する従業員管理ポリシーを策定・提出。
                    li オンボーディングの業務フローを最適化し、必要な人事関連書類を整備。
                    li 従業員の個人情報に関する日本の法令に準拠したデータ利用方針を策定・運用。
                li #[strong 現地における法務・コンプライアンス支援]
                  ul
                    li 雇用契約、給与計算、社会保険手続きについての実務支援を提供。
                    li 労働法改正など最新の法務情報をリアルタイムで提供し、日本の法的環境での事業運営を支援。
                li #[strong 採用・チームビルディング支援]
                  ul
                    li SmartDeerの広範な現地ネットワークを活用し、優秀人材の採用を推進。
                    li 事業立ち上げに向けた実践的なチーム体制を迅速に構築・支援。
                li #[strong カスタマイズ対応の支援サービス]
                  ul
                    li スタートアップフェーズに合わせた現地支援を通じて、日本市場への円滑な立ち上げを実現。
              h4 成果内容
              ul
                li #[strong 迅速な現地チームの構築：] 現地チームを3ヶ月以内に採用・オンボーディングし、計画通りに事業を開始。
                li #[strong コンプライアンス保証：] 日本の労働法規に完全準拠し、法的リスクを排除。
                li #[strong 時間・コストを40％削減：] 人事プロセスの自動化と効率化により、業務負荷と経費を大幅に削減。
              h4 お客様の声
              p 「SmartDeerのチームは、標準的なソリューションのみならず、日本市場への初進出時のニーズを深く理解したカスタマイズ支援も提供してくれました。専門性とプロフェッショナルさのおかげで、ゼロからイチへの道のりが非常にスムーズかつ効率的でした。」—  #[strong -- クライアント企業 人事責任者]
            .solution-expand
              button(@click="toggle('s2')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s2") 拡大
                span(v-else) 折りたたみ
                ArrowDown(v-if="!solutionExpandStatus.s2" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
          .solution-figure
            figure
              img(src='~/assets/images/index/case2.png')
      .solution-item.case3-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case3.png')
          .solution-content
            .solution-title
              h3 スマートロボティクス大手企業によるグローバル給与管理の効率化事例
            .solution-desc(:style="getSolutionDescStyle('s3')")
              h4 背景
              p スマートロボティクス分野で世界的なリーダーである同社は、近年さまざまな業界・地域へと急速に事業を拡大。国際チームの増加に伴い、グローバル給与管理において以下のような課題に直面しました：
              ol
                li #[strong 多様な給与規制：] 国ごとに異なる給与法、税制、福利厚生制度への対応。
                li #[strong 支払いチャネルの分散：] 国際送金における複数の支払手段の管理により、時間とコストが増加。
                li #[strong チーム管理の負荷：] 広域・大規模な給与計算とデータ管理で人事部門に大きな負担。
              h4 主な課題：
              ul
                li #[strong コンプライアンス保証：] 日本の労働法規に完全準拠し、法的リスクを未然に防止。
                li #[strong コスト最適化：] 為替変動や国際送金手数料の影響を抑え、給与コストを低減。
                li #[strong 効率性向上：] グローバルにおける給与支払いの迅速化と処理効率の強化。
              h4 SmartDeerが提供するソリューション：
              ol
                li #[strong グローバル給与サービス]
                  ul
                    li SmartDeerプラットフォームを使った多通貨対応の給与計算・支払いを一元化し、国際決済の業務フローを簡素化。
                li #[strong 税務・法令対応支援]
                  ul
                    li 各国の最新給与・税制情報をリアルタイムで監視・適用し、法令順守を確実にサポート。
                li #[strong 為替管理サービス]
                  ul
                    li 為替変動リスクを抑える通貨ロックサービスを提供し、支払いコストを最小限に。
                li #[strong 給与管理の自動化]
                  ul
                    li AI／自動化ツールを活用した給与計算・支払いシステムを導入し、HR業務の定型作業を削減、業務効率を大幅に改善。
                li #[strong カスタムレポート＆分析機能]
                  ul
                    li 詳細な給与レポートと多次元分析機能により、グローバル人件費のコントロールを強力に支援。
              h4 成果内容
              ul
                li #[strong コンプライアンスリスクゼロ：] 10か国以上の給与管理を法令に完全準拠して実施。
                li #[strong 30％のコスト削減：] 為替最適化と決済集中化により国際給与コストを大幅削減。
                li #[strong 50％の効率向上：] 給与自動化ツールにより人事業務負荷を軽減し、処理効率が向上。
                li #[strong 従業員満足度向上：] グローバルチームへの給与支払いを遅延なく実施し、定着率と満足度の向上を実現。
              h4 お客様の声
              p 「SmartDeerのグローバル給与管理サービスにより、コンプライアンスと効率性を確保しつつ、急速な事業拡大が可能になりました。プロフェッショナルなチームと高度なツールは、当社のグローバル戦略において不可欠な存在です。」 #[strong -- クライアント企業 人事責任者]
            .solution-expand
              button(@click="toggle('s3')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s3") 拡大
                span(v-else) 折りたたみ
                ArrowDown(v-if="!solutionExpandStatus.s3" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case4-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-content
            .solution-title
              h3 中国大手生鮮食品EC企業による中東市場進出支援事例：採用から雇用までのトータルサポート
            .solution-desc(:style="getSolutionDescStyle('s4')")
              h4 背景
              p 中国を代表する生鮮食品EC企業の一つである同社は、国内市場での成功を背景に、2023年に中東市場への進出を開始。サウジアラビアにおける初期運営を支えるため、中国からの幹部派遣に加え、調達・業務・ビジネス分析・マーケティングの現地チーム構築が急務となりました。
              h4 主な課題
              ul
                li #[strong 法人設立・コンプライアンス：] サウジアラビアに現地法人がない状態での直接雇用は法的リスクがありました。また、サウジ化（Saudization）など、現地の労働法に基づく国籍比率基準を満たす必要がありました。
                li #[strong 高度な採用ニーズへの対応：] 中東地域では人材供給に偏りがあり、調達・オペレーション・事業分析・マーケティングなど専門領域における高スキル人材の確保が困難でした。
                li #[strong 越境人材の派遣管理：] 中国からサウジアラビアへの社員派遣には、ビザ申請、就労カードの発行、現地法令の遵守など、複雑な手続きが必要でした。
              h4 SmartDeerが提供したソリューション
              p SmartDeerは、企業のニーズに合わせたカスタマイズされたソリューションを提供しました：
              ol
                li #[strong EOR（雇用代行）サービスの提供]
                  ul
                    li SmartDeerの現地法人がスタッフを直接雇用することで、クライアントによる法人設立が不要となりました。
                    li サウジ化要件を回避しつつ法令に則った運営を確立し、サウジアラビアおよび中東地域での迅速な事業立ち上げが可能となりました。
                li #[strong 中東域内での採用支援]
                  ul
                    li SmartDeerの中東地域ネットワークを活用し、調達・オペレーション・事業分析・マーケティングなどの重要ポジションを迅速に採用。サウジアラビアを含む主要市場をカバーしました。
                li #[strong 越境派遣の一元管理]
                  ul
                    li 中国から派遣される社員について、ビザ申請・就労カード取得・労務書類の準備まで、フルプロセスのサポートを提供し、効率的かつ法令準拠の派遣を実現しました。
                li #[strong 日常支援とアドバイザリー体制]
                  ul
                    li SmartDeer現地スタッフが労働契約管理、給与処理、法規対応などの毎日のサポートを提供し、中東でのHR業務負担を軽減しました。
              h4 成果
              ul
                li #[strong 人材体制の迅速構築：] 2か月以内に中東域内の主要ポジションの採用を完了し、10名の中国人社員をサウジアラビアへ派遣、円滑な事業運営を支援しました。
                li #[strong コンプライアンスリスクゼロ：] SmartDeerの法人雇用モデルにより、サウジアラビアおよび中東地域で完全な法令順守を実現しました。
                li #[strong 時間＆コストの最適化：] 現地法人設立が不要となり、大幅な時間短縮とコスト削減を実現し、クライアントは本業に集中できました。
                li #[strong 地域展開の効率化：] SmartDeerの包括的支援により、サウジアラビアおよび中東での市場展開がスムーズに進み、初期の運営目標を達成しました。
              h4 お客様の声
              p 「SmartDeerのサポートにより、サウジアラビアおよび中東への進出がスムーズに進みました。現地採用の課題を克服し、越境派遣も包括的にサポートいただき、すべてのプロセスが法令準拠かつ効率的でした。グローバル展開における信頼できるパートナーです。」 #[strong -- HR責任者、クライアント企業]
            .solution-expand
              button(@click="toggle('s4')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s4") 拡大
                span(v-else) 折りたたみ
                ArrowDown(v-if="!solutionExpandStatus.s4" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
          .solution-figure
            figure
              img(src='~/assets/images/index/case4.png')
      .solution-item.case5-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case5.png')
          .solution-content
            .solution-title
              h3 グローバルブロックチェーン企業によるEOR導入事例：多国雇用への包括支援
            .solution-desc(:style="getSolutionDescStyle('s5')")
              h4 背景
              p ブロックチェーン技術のグローバルリーダーとして、分散型インターネット基盤の構築と、世界中の優秀な技術・業務人材の獲得を推進している本企業は、急速な事業拡大に伴い、多国籍なチームの雇用において以下の課題に直面しました。
              h4 主な課題
              ul
                li #[strong 多国コンプライアンス対応：] 各国で異なる労働法・税制・社会保障制度に準拠したグローバル採用プロセスの整備が必要でした。
                li #[strong 効率化とコスト最適化：] 迅速な採用・オンボーディングと同時に、管理・運用コストの最小化が求められました。
                li #[strong 多通貨給与管理：] 為替変動によるリスクを伴う多通貨での給与支払い対応が課題でした。
              h4 SmartDeerが提供したソリューション
              p SmartDeerは、グローバルEOR（雇用代行）ワンストップソリューションを提供し、複数国でのチーム拡大を迅速かつ法令遵守のもとで実現しました。
              ol
                li #[strong コンプライアンス管理]
                  ul
                    li SmartDeerのグローバルネットワークを活用し、各国での合法的な従業員雇用を支援。
                    li 労働契約・給与処理のすべてを現地法令に完全準拠させました。
                li #[strong 迅速な従業員オンボーディング]
                  ul
                    li 契約締結・社会保険登録・税務処理など、オンボーディング全体を効率化し、早期戦力化を実現。
                li #[strong 多通貨対応の給与支払いサービス]
                  ul
                    li SmartDeerプラットフォームにより、150種類以上の通貨での給与支払いに対応。
                    li 為替変動リスクを軽減する通貨ロック機能も統合し、財務面の安定性を確保。
                li #[strong 継続的な人事サポート]
                  ul
                    li ポリシーアドバイス、従業員管理、契約更新などの継続支援を通じて、クライアントの人事負担を軽減しました。
              h4 導入成果
              ul
                li #[strong グローバル拡大を迅速化：] 3か月以内に複数国でのチーム構築を実現し、グローバル成長戦略を後押し。
                li #[strong コンプライアンス保証：] すべての雇用・給与業務を現地法に準拠させ、法務・税務リスクを回避。
                li #[strong コスト30％削減：] 人事業務の一元化により、時間・人件費の両面で大幅削減を実現。
                li #[strong 従業員満足度の向上：] 正確で迅速な給与支払いと充実したHRサポートにより、従業員の満足度を高め、長期的な定着につなげました。
              h4 お客様の声
              p 「SmartDeerのグローバルEORサービスにより、各国での採用やコンプライアンスの複雑さを気にせず、本業に集中することができています。プロフェッショナルな対応と効率的なサービスは、当社のグローバル展開を支える重要な柱です。」 #[strong -- クライアント企業 人事責任者]
            .solution-expand
              button(@click="toggle('s5')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s5") 拡大
                span(v-else) 折りたたみ
                ArrowDown(v-if="!solutionExpandStatus.s5" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case6-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-content
            .solution-title
              h3 大手オンライン教育プラットフォームによる香港HRO導入事例：人事業務の効率化支援
            .solution-desc(:style="getSolutionDescStyle('s6')")
              h4 背景
              p 中国大手のオンライン教育プラットフォームである同社は、世界中の学生に質の高い教育コンテンツを提供。グローバルビジネスの拠点として、香港にオペレーションセンターを設けましたが、香港特有の人事管理要件と複雑性に直面しました。
              h4 主な課題
              ul
                li #[strong 給与・法定福利の管理：] 給与処理や強制加入制度（MPFなど）への対応、すべての業務を現地法令に準拠して運用する必要がありました。
                li #[strong データ管理とコンプライアンス：] 従業員情報や給与データを一元管理しながら、香港の個人情報保護法および労働法への対応が求められました。
                li #[strong 人事業務の効率化：] テクノロジーを活用し、事務コスト削減と人事オペレーションの最適化を実現する必要がありました。
              h4 SmartDeerが提供したソリューション
              ol
                li #[strong 給与・福利厚生管理サービス]
                  ul
                    li SmartDeerが給与計算および支払い処理を担当し、正確かつタイムリーな給与支給を実現。
                    li 強制積立年金（MPF）やその他の法定福利も適切に処理、法令順守を徹底し、コンプライアンスリスクの軽減を実現しました。
                li #[strong HR SaaSプラットフォームの提供]
                  ul
                    li 従業員情報、勤怠、給与データ、契約記録などを一元管理できるHR SaaSプラットフォームを導入。
                    li リアルタイムでのデータ更新および自動レポート生成機能により、手作業によるミスを最小限に抑え、業務効率を向上。
                li #[strong 日常的な人事支援サービス]
                  ul
                    li 香港の労働法および政策変更に関するアドバイスを含めた実務サポートを提供し、最新情報を常に共有。
                    li 入社・退職プロセスの対応を支援し、すべての手続きを法令に準拠して実施。
              h4 導入成果
              ul
                li #[strong 管理効率40％向上：] HR SaaS導入により給与・データ管理の業務フローが最適化され、人事部門の日常業務が大幅に軽減。
                li #[strong コンプライアンスリスクゼロ：] 給与・福利厚生のプロ管理により法的リスクを排除し、従業員満足度と定着率を向上。
                li #[strong コスト30％削減：] HROの外部委託により運用コストを大幅削減し、リソースを事業成長に集中可能に。
                li #[strong リアルタイムなデータ管理：] 従業員データを一元的にデジタル管理し、意思決定を支援。
              h4 お客様の声
              p 「SmartDeerの香港におけるHROサービスとHR SaaSプラットフォームは、当社の人事管理効率を大幅に向上させました。給与や法定福利に関する専門性は非常に高く、グローバル展開に欠かせないパートナーです。」 #[strong -- クライアント企業 人事責任者]
            .solution-expand
              button(@click="toggle('s6')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s6") 拡大
                span(v-else) 折りたたみ
                ArrowDown(v-if="!solutionExpandStatus.s6" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
          .solution-figure
            figure
              img(src='~/assets/images/index/case6.png')

mixin contactForm
  .contact-form
    //- 吃瓜就要付出代价，否则影响页面跳转
    //- https://github.com/element-plus/element-plus/pull/9731
    client-only
      el-dialog(v-model="status.showForm" title="" :width="600")
        contact-us-form(@submit="submitSuccess" lang="ja")

mixin anchor
  .anchor
    .consultant(@click="status.showConsultantCode = !status.showConsultantCode")
      figure
        img(src="~/assets/images/index/anchor-avatar-en.png" )
    .consultant-code(v-show="status.showConsultantCode")
      .close(@click="status.showConsultantCode = false")
      figure
        img(src="~/assets/images/index/anchor-code-en.png" )

mixin botButton
  .bot-container(@click="toggleChat")
    img(src="~/assets/images/index/bot_logo_en.png")

mixin goTop
  .go-top-container(@click="smoothScrollTo(500, 0)")
    img(src="~/assets/images/index/top_icon.png")

.index-page
  +header

  main
    +customer
    +service
    +advantage
    +lifecycle
    +process
    +solutionCase

  +contactForm
  site-footer(lang="ja" @contact-us="()=>{status.showForm=true}")
  +botButton
  +goTop
</template>

<script lang="ts" setup>
import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem, ElCarousel, ElCarouselItem } from 'element-plus'
import phoneArea from '~/assets/utils/global-phone-area'
import langTool from '~/assets/utils/lang'
import CustomerList from '@/components/customer-list.vue'
import {getQueryString} from "assets/utils";
import {ref} from "vue";
import videojs from "video.js";
import {ArrowUp, ArrowDown, ArrowRight} from '@element-plus/icons-vue';

definePageMeta({ layout: 'basic' })
useHead({
  htmlAttrs: { lang: 'ja-JP' },
  title: 'SmartDeer - グローバル雇用・採用ソリューション | 国際人事サービス',
  meta: [
    // Basic SEO
    { name: 'description', content: 'SmartDeerは包括的なグローバル雇用・採用ソリューションを提供します。専門的なEORサービス、国際給与管理、海外展開企業向けのコンプライアンス管理。150以上の国をカバーし、専門サポートを提供。' },
    { name: 'keywords', content: 'グローバル雇用, 国際採用, EORサービス, 海外給与管理, 人事アウトソーシング, 海外展開, コンプライアンス管理, 国際人事, グローバル人材, 雇用主責任' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },

    // Canonical and hreflang
    { name: 'canonical', content: 'https://smartdeer.work/ja/' },

    // Open Graph
    { property: 'og:title', content: 'SmartDeer - グローバル雇用&採用ソリューション | 国際人事サービス' },
    { property: 'og:description', content: 'SmartDeerは包括的なグローバル雇用・採用ソリューションを提供。EORサービス、国際給与管理、コンプライアンス対応で海外展開を支援。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://smartdeer.work/ja/' },
    { property: 'og:image', content: 'https://smartdeer.work/images/tg_banner.png' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: 'ja_JP' },

    // Twitter Card
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'SmartDeer - グローバル雇用&採用ソリューション' },
    { name: 'twitter:description', content: 'SmartDeerは包括的なグローバル雇用・採用ソリューションを提供。EORサービス、国際給与管理、コンプライアンス対応で海外展開を支援。' },
    { name: 'twitter:image', content: 'https://smartdeer.work/images/tg_banner.png' },
    { name: 'twitter:image:alt', content: 'SmartDeer - グローバル人事ソリューション' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: 'https://smartdeer.work/ja/' },

    // Hreflang for multilingual SEO
    { rel: 'alternate', hreflang: 'en', href: 'https://smartdeer.work/en/' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://smartdeer.work/zh/' },
    { rel: 'alternate', hreflang: 'ja-JP', href: 'https://smartdeer.work/ja/' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://smartdeer.work/en/' }
  ],
  script: [
    // Structured Data - Organization
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "SmartDeer",
        "description": "グローバル雇用・採用ソリューションプロバイダー",
        "url": "https://smartdeer.work",
        "logo": "https://smartdeer.work/images/logo.png",
        "contactPoint": {
          "@type": "ContactPoint",
          "contactType": "customer service",
          "availableLanguage": ["Japanese", "English", "Chinese"]
        },
        "sameAs": [
          "https://www.linkedin.com/company/smartdeer-global/"
        ],
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "Global"
        },
        "service": [
          {
            "@type": "Service",
            "name": "グローバル採用支援",
            "description": "国際人材獲得・採用サービス"
          },
          {
            "@type": "Service",
            "name": "海外雇用代行サービス (EOR)",
            "description": "現地法人設立不要の合法雇用サービス"
          },
          {
            "@type": "Service",
            "name": "グローバル給与管理",
            "description": "国際給与管理・コンプライアンスサービス"
          },
          {
            "@type": "Service",
            "name": "人事業務アウトソーシング",
            "description": "包括的な人事管理サービス"
          }
        ]
      })
    }
  ]
})

const scrollItems = []

const solutionExpandStatus = ref({
  s1: false,
  s2: false,
  s3: false,
  s4: false,
  s5: false,
  s6: false
})

const defaultHeight = '380px'
const fullHeight = '2000px'

function getSolutionDescStyle(key) {
  return {
    maxHeight: solutionExpandStatus.value[key] ? fullHeight : defaultHeight,
    overflow: 'hidden',
    transition: 'max-height 1s ease-out'
  }
}

function toggle(key) {
  solutionExpandStatus.value[key] = !solutionExpandStatus.value[key];
}

const status = reactive({
  showForm: false,
  showConsultantCode: false
})

const form = reactive({
  name: '',
  company: '',
  service: '',
  countryCode: '+86',
  mobile: '',
  email: '',
  extra: ''
})

const serviceOptions = ref([
  { label: 'Recruitment' },
  { label: 'EOR' },
  { label: 'Contractor' },
  { label: 'PEO' },
  { label: 'Other' }
])

const countryCode = ref(phoneArea)

function scrollTo(tag) {
  const ele = window.document.querySelector(tag)
  if (ele) window.scrollTo({
    top: ele.offsetTop,
    behavior: 'smooth'
  })
}


function smoothScrollTo(duration, target) {
  const start = window.scrollY
  const startTime = performance.now()

  function scrollStep (currentTime) {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)
    const newScrollY = start + (target - start) * progress
    window.scrollTo(0, newScrollY)
    if (progress < 1) {
      requestAnimationFrame(scrollStep)
    }
  }
  requestAnimationFrame(scrollStep)
}

const cozeWebSDK = ref(null)

function toggleChat() {
  console.log(cozeWebSDK.value)
  if (cozeWebSDK.value) {
    cozeWebSDK.value.showChatBot()
  }
}

// 自定义指令
const vScrollShow = {
  mounted: (el, bindings) => {
    const delayOffset = bindings.value.delayOffset || 0
    scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el })
  }
}

let timer = null
function scroll(e) {
  if (timer) return
  timer = setTimeout(() => {
    const offset = window.scrollY + window.innerHeight
    scrollItems.forEach((item, index) => {
      if (item.offsetTop < offset) {
        item.el.setAttribute('show', true)
        scrollItems.splice(index, 1)
      }
    })
    timer = null
  }, 30)
}

onMounted(() => {
  window.addEventListener('scroll', scroll)

  const curScroll = getQueryString('scroll')
  if (curScroll) {
    scrollTo('#' + curScroll)
  }

  const player = videojs('video-player', {
    controls: true
  });

  cozeWebSDK.value = new CozeWebSDK.WebChatClient({
    config: {
      botId: '7439335660751716386'
    },
    ui: {
      base: {
        icon: 'https://static.smartdeer.com/bot_logo.png',
        layout: 'pc',
        zIndex: 1000
      },
      chatBot: {
        title: '顾问 杰哥',
        uploadable: false,
      },
      asstBtn: {
        isNeed: false
      },
      footer: {
        isShow: true,
        expressionText: 'Powered by SmartDeer.'
      }
    }
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', scroll)
})

function switchLang(lang) {
  langTool.swithLang(lang)
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('Submit success!')
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/en.scss';

/* 旋转 */
@keyframes aniRotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.index-page {
  font-family: Helvetica;

  header {
    background-color: #FFF6EC;
    height: 800px;
    margin-bottom: 180px;
    overflow: hidden;
    position: relative;

    .header-banner {
      width: 1204px;
      box-sizing: border-box;
      margin: 0 auto;
      display: flex;
      margin-top: 8px;

      .header-banner-text {

        h2.slogon {
          height: 162px;
          font-size: 58px;
          font-weight: bold;
          color: #000000;
          line-height: 78px;
          padding-top: 97px;
        }

        h1.title {
          height: 47px;
          font-size: 36px;
          font-weight: bold;
          color: #000000;
          line-height: 36px;
          margin-top: 28px;
        }

        .desc {
          width: 641px;
          font-size: 20px;
          color: #000000;
          line-height: 33px;
          margin-top: 28px;
          position: relative;

          span {
            position: relative;
            z-index: 1;
          }

          figure {
            position: absolute;
            left: -56px;
            top: -6px;
            
            img{
              width: 140px;
              height: 140px;
            }
          }
        }
      }

      .header-banner-image {
        user-select: none;
        position: relative;
        width: 638px;
        min-width: 638px;
        height: 637px;
        margin-left: 20px;
        padding: 19px;

        .global-min_1{
          position: absolute;
          top: 50%;
          left: 50%;
          width: 598px;
          height: 600px;
          margin-top: -300px;
          margin-left: -299px;
          z-index: 1;
          
          animation: aniRotate 18s linear infinite;
          animation-fill-mode: forwards;

          img {
            transform: rotate(45deg);
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        .global-min_2{
          position: absolute;
          top: 50%;
          left: 50%;
          z-index: 2;
          width: 480px;
          height: 480px;
          margin-top: -240px;
          margin-left: -240px;

          animation: aniRotate 30s linear infinite;
          animation-fill-mode: forwards;

          img {
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        .global-min_3{
          position: absolute;
          top: 50%;
          left: 50%;
          width: 598px;
          height: 600px;
          margin-top: -300px;
          margin-left: -299px;
          z-index: 3;

          img {
            width: 559px;
            height: 556px;
            display: block;
            position: absolute;
            top: 19px;
            left: -10px;
          }
        }
      }
    }
  }

  main {
    section {
      margin-bottom: 160px;
      &:last-child {
        margin-bottom: 0;
      }
    }

    section .section-title {
      text-align: center;
      margin-bottom: 40px;

      h2 {
        font-size: 48px;
        font-weight: 500;
        line-height: 67px;
        font-weight: bold;
      }

      p {
        font-size: 18px;
        color: #999999;
        letter-spacing: 5px;
        margin-top: 8px;
        line-height: 22px;
        margin-bottom: 0;
      }
    }
  }
}

.anchor {
  position: fixed;
  right: 40px;
  bottom: 40px;
  z-index: 99;

  .consultant {
    width: 91px;
    height: 101px;
    cursor: pointer;
  }
  .consultant-code {
    width: 236px;
    height: 321px;
    position: fixed;
    right: 130px;
    bottom: 25px;

    .close {
      width: 20px;
      height: 20px;
      cursor: pointer;
      position: absolute;
      top: 22px;
      right: 19px;
      // background: #000;
    }
  }

  figure {
    width: 100%;
    height: 100%;

    img{
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}

// 服务客户模块
section.customer {
  width: 1204px;
  // padding: 0 20px;
  margin: 0 auto;
}

// 服务模式模块
section.service {
  overflow: hidden;
  min-width: 1280px;

  .service-contact {
    .service-contact-button {
      background: transparent;
      line-height: 28px;
      font-size: 14px;
      border-radius: 20px;
      padding: 0 20px;
      border: 1px #000 solid;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }

  .service-list {
    width: 1204px;
    margin: 0 auto;
    // padding: 0 20px;

    .service-item {
      display: flex;
      justify-content: space-between;
      padding: 58px 0px;

      &:nth-child(2n+1) {
        .figure-area {
          right: -100px;
        }

        .service-content {
          left: -100px;
        }
      }

      &:nth-child(2n) {
        .figure-area {
          left: -100px;
        }

        .service-content {
          right: -100px;
        }
      }

      &[show="true"] {
        &:nth-child(2n+1) {
          .figure-area {
            right: 0px;
            opacity: 1;
          }

          .service-content {
            left: 0px;
            opacity: 1;
          }
        }

        &:nth-child(2n) {
          .figure-area {
            left: 0px;
            opacity: 1;
          }

          .service-content {
            right: 0px;
            opacity: 1;
          }
        }
      }

      .figure-area {
        position: relative;
        opacity: 0;
        min-height: 333px;
        transition: all .5s;
        figure {
          img, #video-player{
            width: 587px;
          }
          #video-player {
            height: 333px;
            background: transparent;

            video {
              border-radius: 20px;
              overflow: hidden;
            }
          }
        }
      }

      .service-content {
        position: relative;
        opacity: 0;
        width: 500px;
        flex: 0 0 auto;
        padding: 0 16px;
        box-sizing: border-box;
        transition: all .5s;

        .service-title {
          color: #333333;

          h3 {
            font-size: 36px;
            font-weight: bold;
            line-height: 1;
            margin-bottom: 8px;
          }

          p {
            font-size: 18px;
            color: #333333;
            letter-spacing: 0;
            line-height: 1;
            margin: 0;
            padding: 0;
          }
        }

        .service-desc {
          font-size: 16px;
          color: #454545;
          line-height: 27px;

          p{
            text-indent: 1em;
            position: relative;

            &::before{
              content: '';
              width: 3px;
              height: 3px;
              background: #FF7F00;
              position: absolute;
              left: 0;
              top: 12px;
            }
          }
        }
      }
    }
  }
}

section.advantages {
  width: 1204px;
  margin: 0 auto;

  .advantage-list {
    display: flex;
    justify-content: space-between;

    &[show="true"] {
      .advantage-item {
        transform: scale(1);
        transition: all .4s;
      }
    }

    .advantage-item {
      padding-bottom: 40px;
      width: 279px;
      background-color: #FEEFDF;
      border-radius: 18px;
      transform: scale(0);
      transition: all .4s;
      color: #333333;
      text-align: center;
      height: 318px;
      padding-top: 48px;
      box-sizing: border-box;

      .advantage-icon-area {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 64px;
        }
      }

      .advantage-title {
        font-size: 28px;
        margin-top: 24px;
        line-height: 28px;
      }

      .advantage-content {
        font-size: 18px;
        margin: 0 auto;
        margin-top: 14px;
        width: 222px;
      }
    }
  }
}

section.lifecycle {
  width: 1204px;
  // padding: 0 20px;
  margin: 0 auto;

  .lifecycle-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 86px;

    &[show="true"] {
      .lifecycle-item {
        left: 0;
        opacity: 1;
        transition: all .4s;
      }
    }

    .lifecycle-item {
      position: relative;
      left: -40px;
      opacity: 0;

      .lifecycle-item-icon {
        background: #FFFFFF;
        // box-shadow: 0 2px 20px 0 rgba(254, 145, 18, 0.10);
        height: 152px;
        width: 152px;
        border-radius: 50%;
        margin-bottom: 32px;
        position: relative;

        .arrow {
          background-image: url("~/assets/images/index/arrow.svg");
          height: 14px;
          width: 12px;
          background-size: contain;
          background-repeat: no-repeat;
          position: absolute;
          top: 67px;
          right: -18px;
        }
      }

      .lifecycle-item-title {
        text-align: center;
      }
    }
  }

  .lifecycle-repeat {
    width: 1000px;
    margin: 0 auto;

    img {
      width: 100%;
    }
  }
}

// 服务模式模块
section.process {
  .process-list {
    .process-item {
      position: relative;
      min-width: 1280px;
      margin-bottom: 160px;
      height: 506px;

      &[show="true"] {
        .figure-area {
          top: 0;
          transition: all .8s;
          opacity: 1;
        }
      }

      &:last-child {
        .process-content-wrapper {
          padding-bottom: 0;
        }
      }

      &:nth-child(6){
        .process-content-wrapper{
          position: relative;
          top: -46px;
        }
      }

      .process-content-wrapper {
        width: 1253px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 506px;
      }

      .process-background {
        width: 50%;
        height: 506px;
        position: absolute;
        z-index: -1;

        .process-num {
          font-family: Verdana;
          font-size: 64px;
          color: #333333;
          position: absolute;
          line-height: 1;
          top: 100px;
        }

        &.layout-left {
          left: 0;
          background: linear-gradient(270deg, #FFFFFF 0%, #FFF2E3 100%);

          .process-num {
            right: 610px
          }
        }

        &.layout-right {
          right: 0;
          background-image: linear-gradient(90deg, #FFFFFF 0%, #FFF2E3 100%);

          .process-num {
            left: 610px
          }
        }
      }

      .figure-area {
        flex: 1 1 auto;
        display: flex;
        justify-content: center;
        position: relative;
        top: 120px;
        transition: all .8s;
        opacity: 0;

        img {
          width: 659px;
          filter: drop-shadow(0 2px 16px RGBA(0, 0, 0, .18));
        }
      }

      .process-content {
        margin-top: 48px;
        width: 500px;
        flex: 0 0 auto;
        padding: 0 8px;
        box-sizing: border-box;

        .process-title {

          h3 {
            font-size: 32px;
            font-weight: 500;
            line-height: 40px;
            font-weight: bold;
            color: #222222;
          }

          p {
            width: 484px;
            font-size: 16px;
            color: #454545;
            line-height: 26px;
            margin-top: 24px;
          }
        }

        .process-desc {
          font-size: 16px;
          color: #666666;
          line-height: 27px;
        }
      }
    }
  }
}

.contact-form {
  .form-title {
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    margin-top: -30px;
  }

  .form-body {
    padding: 0 80px;

    .mobile {
      line-height: 46px;
      border: 1px solid;
      border-radius: 8px;
      width: 100%;
    }
  }
}
// 服务模式模块
section.solution {
  overflow: hidden;
  min-width: 1280px;

  .solution-list {
    margin: 0 auto;

    .case1-bg {
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(239, 223, 207, 0.4));
    }
    .case2-bg {
      background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
    }
    .case3-bg {
      background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rgba(149, 201, 246, 0.4));
    }
    .case4-bg {
      background: linear-gradient(to bottom, rgba(251, 235, 186, 0.04), rgba(251, 235, 186, 0.4));
    }
    .case5-bg {
      background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
    }
    .case6-bg {
      background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rrgba(149, 201, 246, 0.4));
    }

    .solution-item {
      padding: 68px 0;

      &:nth-child(2n+1) {
        .figure-area {
          right: -70px;
        }

        .service-content {
          left: -70px;
        }
      }

      &:nth-child(2n) {
        .figure-area {
          left: -70px;
        }

        .service-content {
          right: -70px;
        }
      }

      &[show="true"] {
        &:nth-child(2n+1) {
          .solution-figure {
            right: 0;
            opacity: 1;
          }

          .solution-content {
            left: 0;
            opacity: 1;
          }
        }

        &:nth-child(2n) {
          .solution-figure {
            left: 0;
            opacity: 1;
          }

          .solution-content {
            right: 0;
            opacity: 1;
          }
        }
      }

      .solution-wrapper {
        width: 1204px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;

        .solution-figure {
          position: relative;
          opacity: 0;
          min-height: 333px;
          transition: all .5s;

          figure {
            img {
              width: 400px;
            }
          }
        }

        .solution-content {
          position: relative;
          opacity: 0;
          width: 734px;
          flex: 0 0 auto;
          box-sizing: border-box;
          transition: all .5s;

          .solution-title {
            color: #333333;

            h3 {
              font-size: 30px;
              font-weight: 500;
              font-family: PingFangSC-Medium, PingFang SC;
              line-height: 40px;
              color: #333333;
              margin: 0;
            }

            p {
              font-size: 18px;
              font-family: DIN-Regular, DIN;
              font-weight: 400;
              color: #999;
              line-height: 26px;
              margin: 10px 0 0 0;
            }
          }

          .solution-desc {
            font-size: 16px;
            color: #333;
            line-height: 27px;
            h4 {
              margin: 20px 0 10px 0;
              font-weight: bold;
            }
            ol {
              position: relative;
              padding-left: 20px;
              margin: 0
            }
            ol li {
              list-style: decimal;
              position: relative;
            }
            ul {
              position: relative;
              padding-left: 20px;
              margin: 0;
            }
            ul li {
              position: relative;
            }

            li>ul {
              padding-left: 20px;
            }
            li>ul li{
              list-style: circle;
            }
            p{
              position: relative;
              margin: 0;
            }
          }

          .solution-expand {
            margin-top: 20px;
            .solution-toggle {
              background: transparent;
              border: 1px #000 solid;
              padding: 0 20px;
              line-height: 28px;
              border-radius: 15px;
              cursor: pointer;
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }
  }
}
.inline-arrow {
  width: 16px;
  height: 16px;
  margin-left: 5px;
}
.bot-container {
  z-index: 1000;
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 124px;
  right: 50px;
  transition: transform 0.3s ease;
  &:hover {
    transform: scale(1.16);
  }
  img {
    width: 80px;
    height: 91px;
  }
}
.go-top-container {
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 30px;
  right: 55px;

  img {
    width: 70px;
    height: 70px;
  }
}
</style>