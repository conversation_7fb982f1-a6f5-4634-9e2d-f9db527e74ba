// SEO优化通用函数
export const useSeoOptimization = () => {
  
  // 生成页面SEO配置
  const generatePageSeo = (config: {
    title: string
    description: string
    keywords?: string
    lang: string
    path: string
    image?: string
    type?: string
  }) => {
    const { title, description, keywords, lang, path, image, type = 'website' } = config
    
    const baseUrl = 'https://smartdeer.work'
    const fullUrl = `${baseUrl}${path}`
    const defaultImage = `${baseUrl}/images/tg_banner.png`
    
    // 多语言URL映射
    const langUrls = {
      en: path.replace(/\/(zh|ja)\//, '/en/'),
      zh: path.replace(/\/(en|ja)\//, '/zh/'),
      ja: path.replace(/\/(en|zh)\//, '/ja/')
    }
    
    return {
      htmlAttrs: { lang: lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US' },
      title,
      meta: [
        // 基础SEO
        { name: 'description', content: description },
        { name: 'keywords', content: keywords || '' },
        { name: 'author', content: 'SmartDeer' },
        { name: 'robots', content: 'index, follow' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
        
        // Canonical URL
        { rel: 'canonical', href: fullUrl },
        
        // 多语言标签
        { rel: 'alternate', hreflang: 'en', href: `${baseUrl}${langUrls.en}` },
        { rel: 'alternate', hreflang: 'zh-CN', href: `${baseUrl}${langUrls.zh}` },
        { rel: 'alternate', hreflang: 'ja-JP', href: `${baseUrl}${langUrls.ja}` },
        { rel: 'alternate', hreflang: 'x-default', href: `${baseUrl}${langUrls.en}` },
        
        // Open Graph
        { property: 'og:title', content: title },
        { property: 'og:description', content: description },
        { property: 'og:type', content: type },
        { property: 'og:url', content: fullUrl },
        { property: 'og:site_name', content: 'SmartDeer' },
        { property: 'og:image', content: image || defaultImage },
        { property: 'og:image:width', content: '1200' },
        { property: 'og:image:height', content: '630' },
        { property: 'og:locale', content: lang === 'zh' ? 'zh_CN' : lang === 'ja' ? 'ja_JP' : 'en_US' },
        
        // Twitter Card
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:site', content: '@SmartDeer' },
        { name: 'twitter:title', content: title },
        { name: 'twitter:description', content: description },
        { name: 'twitter:image', content: image || defaultImage },
        { name: 'twitter:image:alt', content: title },
        
        // 百度特定优化
        { name: 'applicable-device', content: 'pc,mobile' },
        { name: 'MobileOptimized', content: 'width' },
        { name: 'HandheldFriendly', content: 'true' }
      ]
    }
  }
  
  // 生成文章页面SEO
  const generateArticleSeo = (config: {
    title: string
    description: string
    lang: string
    path: string
    publishDate?: string
    modifiedDate?: string
    author?: string
    image?: string
  }) => {
    const baseSeo = generatePageSeo({
      ...config,
      type: 'article'
    })
    
    // 添加文章特定的meta标签
    const articleMeta = [
      { property: 'article:published_time', content: config.publishDate || new Date().toISOString() },
      { property: 'article:modified_time', content: config.modifiedDate || new Date().toISOString() },
      { property: 'article:author', content: config.author || 'SmartDeer' },
      { property: 'article:section', content: 'Global Employment' },
      { property: 'article:tag', content: 'EOR, Global HR, International Recruitment' }
    ]
    
    return {
      ...baseSeo,
      meta: [...baseSeo.meta, ...articleMeta]
    }
  }
  
  // 生成关键词建议
  const getKeywordSuggestions = (lang: string, pageType: string) => {
    const keywords = {
      en: {
        homepage: 'global employment, international recruitment, EOR services, global payroll, HR outsourcing, overseas expansion, compliance management, international hiring, global HR solutions, employer of record',
        calculator: 'employee cost calculator, global payroll calculator, international hiring costs, EOR cost calculator, global employment cost, salary calculator international',
        aboutus: 'SmartDeer company, global HR company, international employment services, EOR provider, global recruitment agency, HR outsourcing company'
      },
      zh: {
        homepage: '全球雇佣, 全球招聘, 海外雇佣, EOR服务, 国际薪酬, 人力资源外包, 出海企业, 合规管理, 海外人事, 跨境用工, 雇主责任',
        calculator: '员工成本计算器, 全球薪酬计算器, 国际招聘成本, EOR成本计算, 全球雇佣成本, 海外薪资计算器',
        aboutus: 'SmartDeer公司, 全球人力资源公司, 国际雇佣服务, EOR服务商, 全球招聘机构, 人力资源外包公司'
      },
      ja: {
        homepage: 'グローバル雇用, 国際採用, EORサービス, 海外給与管理, 人事アウトソーシング, 海外展開, コンプライアンス, 国際人事, グローバル人材, 雇用主責任',
        calculator: '従業員コスト計算機, グローバル給与計算機, 国際採用コスト, EORコスト計算, グローバル雇用コスト, 海外給与計算機',
        aboutus: 'SmartDeer会社, グローバル人事会社, 国際雇用サービス, EORプロバイダー, グローバル採用代行, 人事アウトソーシング会社'
      }
    }
    
    return keywords[lang as keyof typeof keywords]?.[pageType as keyof typeof keywords['en']] || keywords.en.homepage
  }
  
  return {
    generatePageSeo,
    generateArticleSeo,
    getKeywordSuggestions
  }
}
