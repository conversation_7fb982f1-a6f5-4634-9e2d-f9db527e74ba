// SEO结构化数据配置
export const useSeoStructuredData = () => {
  
  // 组织信息结构化数据
  const getOrganizationSchema = (lang: string = 'en') => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "SmartDeer",
      "url": "https://smartdeer.work",
      "logo": "https://smartdeer.work/images/logo.png",
      "foundingDate": "2020",
      "numberOfEmployees": "50-100",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "SG",
        "addressLocality": "Singapore"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+65-1234-5678",
        "contactType": "customer service",
        "availableLanguage": ["English", "Chinese", "Japanese"]
      },
      "sameAs": [
        "https://www.linkedin.com/company/smartdeer",
        "https://twitter.com/smartdeer"
      ]
    }

    const descriptions = {
      en: "SmartDeer provides global employment and recruitment solutions, offering comprehensive HR services for companies expanding overseas.",
      zh: "SmartDeer提供全球雇佣和招聘解决方案，为出海企业提供一站式人力资源服务。",
      ja: "SmartDeerはグローバル雇用・採用ソリューションを提供し、海外展開企業向けの包括的人事サービスを提供しています。"
    }

    return {
      ...baseData,
      "description": descriptions[lang as keyof typeof descriptions] || descriptions.en
    }
  }

  // 网站结构化数据
  const getWebsiteSchema = (lang: string = 'en') => {
    const urls = {
      en: "https://smartdeer.work/en/",
      zh: "https://smartdeer.work/zh/",
      ja: "https://smartdeer.work/ja/"
    }

    return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "SmartDeer",
      "url": urls[lang as keyof typeof urls] || urls.en,
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": `${urls[lang as keyof typeof urls] || urls.en}?q={search_term_string}`
        },
        "query-input": "required name=search_term_string"
      }
    }
  }

  // 服务结构化数据
  const getServiceSchema = (lang: string = 'en') => {
    const services = {
      en: {
        name: "Global Employment & Recruitment Services",
        description: "Comprehensive EOR services, global payroll management, and compliance support for international expansion.",
        serviceType: "Human Resources Services"
      },
      zh: {
        name: "全球雇佣与招聘服务",
        description: "全面的EOR服务、全球薪酬管理和合规支持，助力企业国际化扩张。",
        serviceType: "人力资源服务"
      },
      ja: {
        name: "グローバル雇用・採用サービス",
        description: "包括的なEORサービス、グローバル給与管理、コンプライアンスサポートによる国際展開支援。",
        serviceType: "人事サービス"
      }
    }

    const serviceData = services[lang as keyof typeof services] || services.en

    return {
      "@context": "https://schema.org",
      "@type": "Service",
      "name": serviceData.name,
      "description": serviceData.description,
      "serviceType": serviceData.serviceType,
      "provider": {
        "@type": "Organization",
        "name": "SmartDeer",
        "url": "https://smartdeer.work"
      },
      "areaServed": {
        "@type": "Place",
        "name": "Worldwide"
      }
    }
  }

  // 面包屑导航结构化数据
  const getBreadcrumbSchema = (breadcrumbs: Array<{name: string, url: string}>) => {
    return {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": breadcrumbs.map((item, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "name": item.name,
        "item": item.url
      }))
    }
  }

  // FAQ结构化数据
  const getFAQSchema = (faqs: Array<{question: string, answer: string}>) => {
    return {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": faqs.map(faq => ({
        "@type": "Question",
        "name": faq.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": faq.answer
        }
      }))
    }
  }

  return {
    getOrganizationSchema,
    getWebsiteSchema,
    getServiceSchema,
    getBreadcrumbSchema,
    getFAQSchema
  }
}
