<!--
 * @Author: xu.sun <EMAIL>
 * @Date: 2022-11-09 18:42:17
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2022-11-18 17:09:58
 * @FilePath: /bpo-website-pc/app.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
div
  NuxtLayout
    NuxtPage
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { ID_INJECTION_KEY } from 'element-plus'
import { switchingPlatforms } from '~/assets/utils'

onMounted(() => {
  switchingPlatforms()
})

provide(ID_INJECTION_KEY, {
  prefix: 100,
  current: 0,
})
</script>

<style lang="scss">
@import '~/assets/styles/reset.scss';
</style>
