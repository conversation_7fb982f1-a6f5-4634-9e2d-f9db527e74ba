#!/usr/bin/env node

/**
 * SmartDeer网站SEO优化工具
 * 自动修复SEO问题，优化meta标签和页面标题
 */

const fs = require('fs');
const path = require('path');

class SEOOptimizer {
  constructor() {
    this.optimizations = [];
    this.seoTemplates = this.loadSEOTemplates();
  }

  // 加载SEO模板配置
  loadSEOTemplates() {
    return {
      zh: {
        homepage: {
          title: 'SmartDeer - 全球雇佣&全球招聘解决方案 | 出海企业一站式人力资源服务平台',
          description: 'SmartDeer提供全球雇佣、全球招聘一站式解决方案。专业EOR服务、国际薪酬管理、合规支持，助力中国企业海外扩张，覆盖150+国家和地区。立即获取免费咨询，开启您的全球化之旅。',
          keywords: '全球雇佣, 全球招聘, 海外雇佣, EOR服务, 国际薪酬, 人力资源外包, 出海企业, 合规管理, 海外人事, 跨境用工, 国际招聘, 全球HR'
        },
        aboutus: {
          title: '关于SmartDeer - 全球人力资源解决方案提供商 | 出海企业首选合作伙伴',
          description: '了解SmartDeer，领先的全球人力资源解决方案提供商。我们提供专业EOR服务、国际招聘、薪酬管理，覆盖150+国家，在全球设有办事处，拥有ISO 27001认证，助力中国企业安全合规出海。',
          keywords: '关于SmartDeer, 全球人力资源公司, 国际雇佣服务, EOR服务商, 全球招聘公司, 人力资源解决方案, 国际薪酬, 出海企业服务, 全球合规, ISO认证'
        },
        calculator: {
          title: '全球员工成本计算器 - 免费在线工具 | SmartDeer人力资源解决方案',
          description: '使用SmartDeer免费全球员工成本计算器，即时计算150+国家的雇主成本、税费、福利。为您的国际招聘预算提供精准规划，助力出海企业做出明智的用工决策，降低海外扩张风险。',
          keywords: '员工成本计算器, 全球薪酬计算器, 国际招聘成本, 雇主成本计算, 全球雇佣计算器, 国际薪资计算器, EOR成本计算, 出海用工成本, 海外员工费用, 免费工具'
        },
        countries: {
          title: '全球国家雇佣指南 - 150+国家HR合规信息 | SmartDeer',
          description: '查看SmartDeer全球国家雇佣指南，获取150+国家的HR合规信息、劳动法规、税务政策、薪酬福利标准。为您的海外扩张提供专业指导，确保合规雇佣，降低法律风险。',
          keywords: '全球雇佣指南, 国家HR政策, 劳动法规, 海外合规, 国际税务, 全球薪酬, 雇佣法律, 出海指南, HR合规, 国际劳动法'
        },
        marketing: {
          title: 'SmartDeer营销资讯 - 全球HR行业动态与出海企业案例分享',
          description: '获取最新的全球HR行业动态、出海企业成功案例、国际雇佣趋势分析。SmartDeer为您提供专业的行业洞察，助力企业制定更好的全球化人力资源策略，把握海外市场机遇。',
          keywords: 'HR行业动态, 出海企业案例, 全球雇佣趋势, 国际HR资讯, 海外扩张案例, 全球化策略, 人力资源趋势, 国际市场分析'
        }
      },
      en: {
        homepage: {
          title: 'SmartDeer - Global Employment & Recruitment Solutions | International HR Services',
          description: 'SmartDeer provides comprehensive global employment and recruitment solutions. Professional EOR services, international payroll, compliance management for companies expanding overseas. Covering 150+ countries with expert support.',
          keywords: 'global employment, international recruitment, EOR services, global payroll, HR outsourcing, overseas expansion, compliance management, international hiring, global HR solutions, remote work'
        },
        aboutus: {
          title: 'About SmartDeer - Leading Global HR Solutions Provider | International Employment',
          description: 'Learn about SmartDeer, a leading global HR solutions provider. We offer professional EOR services, international recruitment, payroll management across 150+ countries with global offices and ISO 27001 certification for secure compliance.',
          keywords: 'about SmartDeer, global HR company, international employment services, EOR provider, global recruitment agency, HR solutions, international payroll, overseas business services, global compliance'
        },
        calculator: {
          title: 'Global Employee Cost Calculator - Free Online Tool | SmartDeer HR Solutions',
          description: 'Use SmartDeer\'s free global employee cost calculator to instantly calculate employer costs, taxes, and benefits across 150+ countries. Get accurate budget planning for international recruitment and make informed hiring decisions.',
          keywords: 'employee cost calculator, global payroll calculator, international recruitment costs, employer cost calculation, global hiring calculator, international salary calculator, EOR cost calculator, overseas employment costs'
        }
      },
      ja: {
        homepage: {
          title: 'SmartDeer - グローバル雇用・採用ソリューション | 国際人事サービス',
          description: 'SmartDeerは包括的なグローバル雇用・採用ソリューションを提供します。専門的なEORサービス、国際給与管理、海外展開企業向けのコンプライアンス管理。150以上の国をカバーし、専門サポートを提供。',
          keywords: 'グローバル雇用, 国際採用, EORサービス, 海外給与管理, 人事アウトソーシング, 海外展開, コンプライアンス管理, 国際人事, グローバル人材, 雇用主責任'
        }
      }
    };
  }

  // 优化主要页面
  async optimizeMainPages() {
    console.log('🔧 开始优化主要页面...');

    // 修复根目录index.vue的noindex问题
    await this.fixRootIndexPage();

    // 优化中文主要页面
    await this.optimizePage('pages/zh/index.vue', 'zh', 'homepage');
    await this.optimizePage('pages/zh/aboutus.vue', 'zh', 'aboutus');
    await this.optimizePage('pages/zh/calculator.vue', 'zh', 'calculator');

    // 优化英文主要页面
    await this.optimizePage('pages/en/index.vue', 'en', 'homepage');
    await this.optimizePage('pages/en/aboutus.vue', 'en', 'aboutus');
    await this.optimizePage('pages/en/calculator.vue', 'en', 'calculator');

    // 优化日文主要页面
    await this.optimizePage('pages/ja/index.vue', 'ja', 'homepage');
    await this.optimizePage('pages/ja/aboutus.vue', 'ja', 'aboutus');
    await this.optimizePage('pages/ja/calculator.vue', 'ja', 'calculator');

    console.log('✅ 主要页面优化完成');
  }

  // 修复根目录index.vue的noindex问题
  async fixRootIndexPage() {
    const filePath = 'pages/index.vue';
    if (!fs.existsSync(filePath)) return;

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 将noindex改为index
      content = content.replace(
        /robots['"`]:\s*['"`]noindex,\s*follow['"`]/,
        'robots\': \'index, follow\''
      );

      // 优化标题和描述
      const template = this.seoTemplates.zh.homepage;
      content = content.replace(
        /title:\s*['"`]([^'"`]*?)['"`]/,
        `title: '${template.title}'`
      );

      content = content.replace(
        /name:\s*['"`]description['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/,
        `name: 'description', content: '${template.description}'`
      );

      fs.writeFileSync(filePath, content);
      this.optimizations.push(`✅ 修复根目录index.vue的索引设置`);
      console.log('✅ 修复根目录index.vue的索引设置');
    } catch (error) {
      console.error(`❌ 修复根目录index.vue失败:`, error.message);
    }
  }

  // 优化单个页面
  async optimizePage(filePath, language, pageType) {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  页面不存在: ${filePath}`);
      return;
    }

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const template = this.seoTemplates[language]?.[pageType];
      
      if (!template) {
        console.log(`⚠️  没有找到模板: ${language}/${pageType}`);
        return;
      }

      let modified = false;

      // 优化标题
      if (template.title) {
        const titleRegex = /title:\s*['"`]([^'"`]*?)['"`]/;
        if (titleRegex.test(content)) {
          content = content.replace(titleRegex, `title: '${template.title}'`);
          modified = true;
        }
      }

      // 优化描述
      if (template.description) {
        const descRegex = /name:\s*['"`]description['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/;
        if (descRegex.test(content)) {
          content = content.replace(descRegex, `name: 'description', content: '${template.description}'`);
          modified = true;
        }
      }

      // 优化关键词
      if (template.keywords) {
        const keywordsRegex = /name:\s*['"`]keywords['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/;
        if (keywordsRegex.test(content)) {
          content = content.replace(keywordsRegex, `name: 'keywords', content: '${template.keywords}'`);
          modified = true;
        }
      }

      if (modified) {
        fs.writeFileSync(filePath, content);
        this.optimizations.push(`✅ 优化页面: ${filePath}`);
        console.log(`✅ 优化页面: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ 优化页面失败 ${filePath}:`, error.message);
    }
  }

  // 为缺少SEO配置的页面添加基础配置
  async addBasicSEOToPages() {
    console.log('🔧 为缺少SEO配置的页面添加基础配置...');
    
    // 读取审计报告
    const reportPath = 'SEO_AUDIT_REPORT.json';
    if (!fs.existsSync(reportPath)) {
      console.log('❌ 找不到审计报告，请先运行SEO审计');
      return;
    }

    const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
    const pagesNeedingSEO = report.pages.filter(page => 
      page.issues.includes('缺少描述') || page.issues.includes('缺少标题')
    );

    console.log(`📝 找到 ${pagesNeedingSEO.length} 个需要添加SEO配置的页面`);

    for (const page of pagesNeedingSEO.slice(0, 10)) { // 限制处理前10个页面
      await this.addBasicSEO(page);
    }

    console.log('✅ 基础SEO配置添加完成');
  }

  // 为单个页面添加基础SEO配置
  async addBasicSEO(pageData) {
    const filePath = `pages/${pageData.path}`;
    if (!fs.existsSync(filePath)) return;

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否已有useHead配置
      if (content.includes('useHead(')) {
        console.log(`⚠️  页面已有SEO配置: ${pageData.path}`);
        return;
      }

      // 生成基础SEO配置
      const seoConfig = this.generateBasicSEO(pageData);
      
      // 在script标签后添加SEO配置
      const scriptMatch = content.match(/<script[^>]*setup[^>]*>/);
      if (scriptMatch) {
        const insertPosition = content.indexOf(scriptMatch[0]) + scriptMatch[0].length;
        const seoBlock = `\n\ndefinePageMeta({ layout: 'basic' })\n${seoConfig}\n`;
        
        content = content.slice(0, insertPosition) + seoBlock + content.slice(insertPosition);
        
        fs.writeFileSync(filePath, content);
        this.optimizations.push(`✅ 添加SEO配置: ${pageData.path}`);
        console.log(`✅ 添加SEO配置: ${pageData.path}`);
      }
    } catch (error) {
      console.error(`❌ 添加SEO配置失败 ${pageData.path}:`, error.message);
    }
  }

  // 生成基础SEO配置
  generateBasicSEO(pageData) {
    const lang = pageData.language;
    const langCode = lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US';
    
    // 根据页面路径生成标题和描述
    let title = this.generateTitleFromPath(pageData.path, lang);
    let description = this.generateDescriptionFromPath(pageData.path, lang);
    
    return `useHead({
  htmlAttrs: { lang: '${langCode}' },
  title: '${title}',
  meta: [
    { name: 'description', content: '${description}' },
    { name: 'keywords', content: '${this.generateKeywords(pageData.path, lang)}' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
    
    // Open Graph
    { property: 'og:title', content: '${title}' },
    { property: 'og:description', content: '${description}' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: '${pageData.url}' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: '${langCode.replace('-', '_')}' }
  ]
})`;
  }

  // 从路径生成标题
  generateTitleFromPath(path, lang) {
    const pathParts = path.replace('.vue', '').split('/');
    const pageName = pathParts[pathParts.length - 1];
    
    const titleMappings = {
      zh: {
        marketing: 'SmartDeer营销资讯 - 全球HR行业动态',
        countries: '全球国家雇佣指南 - SmartDeer',
        legal: '法律条款 - SmartDeer',
        articles: '文章资讯 - SmartDeer全球HR解决方案'
      },
      en: {
        marketing: 'SmartDeer Marketing News - Global HR Industry Updates',
        countries: 'Global Country Employment Guide - SmartDeer',
        legal: 'Legal Terms - SmartDeer',
        articles: 'Articles & Insights - SmartDeer Global HR Solutions'
      },
      ja: {
        marketing: 'SmartDeerマーケティングニュース - グローバルHR業界動向',
        countries: 'グローバル国別雇用ガイド - SmartDeer',
        legal: '法的条項 - SmartDeer',
        articles: '記事・インサイト - SmartDeerグローバルHRソリューション'
      }
    };

    return titleMappings[lang]?.[pageName] || `${pageName} - SmartDeer`;
  }

  // 从路径生成描述
  generateDescriptionFromPath(path, lang) {
    const descMappings = {
      zh: {
        marketing: 'SmartDeer营销资讯页面，提供最新的全球HR行业动态、出海企业案例分析、国际雇佣趋势洞察，助力企业制定更好的全球化人力资源策略。',
        countries: 'SmartDeer全球国家雇佣指南，提供150+国家的HR合规信息、劳动法规、税务政策，为您的海外扩张提供专业指导。',
        legal: 'SmartDeer法律条款页面，了解我们的服务条款、隐私政策、用户协议等重要法律信息。',
        articles: 'SmartDeer文章资讯页面，提供全球HR行业深度分析、出海企业实践案例、国际雇佣最佳实践等专业内容。'
      },
      en: {
        marketing: 'SmartDeer marketing news and updates, featuring the latest global HR industry trends, overseas expansion case studies, and international employment insights.',
        countries: 'SmartDeer global country employment guide, providing HR compliance information, labor regulations, and tax policies for 150+ countries.',
        legal: 'SmartDeer legal terms page, including our terms of service, privacy policy, user agreements and other important legal information.',
        articles: 'SmartDeer articles and insights, featuring in-depth global HR industry analysis, overseas business case studies, and international employment best practices.'
      }
    };

    const pathParts = path.replace('.vue', '').split('/');
    const pageName = pathParts[pathParts.length - 1];
    
    return descMappings[lang]?.[pageName] || `SmartDeer ${pageName} page providing professional global HR solutions and services.`;
  }

  // 生成关键词
  generateKeywords(path, lang) {
    const keywordMappings = {
      zh: {
        marketing: 'HR行业动态, 出海企业案例, 全球雇佣趋势, 国际HR资讯, 海外扩张案例',
        countries: '全球雇佣指南, 国家HR政策, 劳动法规, 海外合规, 国际税务',
        legal: '法律条款, 服务条款, 隐私政策, 用户协议, 法律声明',
        articles: 'HR文章, 全球雇佣资讯, 国际招聘, 出海指南, 人力资源'
      },
      en: {
        marketing: 'HR industry news, overseas expansion cases, global employment trends, international HR insights',
        countries: 'global employment guide, country HR policies, labor regulations, overseas compliance',
        legal: 'legal terms, terms of service, privacy policy, user agreement, legal notice',
        articles: 'HR articles, global employment news, international recruitment, overseas guide'
      }
    };

    const pathParts = path.replace('.vue', '').split('/');
    const pageName = pathParts[pathParts.length - 1];
    
    return keywordMappings[lang]?.[pageName] || 'SmartDeer, global HR, international employment';
  }

  // 运行所有优化
  async runOptimizations() {
    console.log('🚀 开始SEO优化...\n');
    
    await this.optimizeMainPages();
    await this.addBasicSEOToPages();
    
    console.log('\n📊 优化总结:');
    this.optimizations.forEach(opt => console.log(opt));
    console.log(`\n✅ 总共完成 ${this.optimizations.length} 项优化`);
  }
}

// 运行优化
if (require.main === module) {
  const optimizer = new SEOOptimizer();
  optimizer.runOptimizations().catch(console.error);
}

module.exports = SEOOptimizer;
