// SEO监控和分析脚本
const fs = require('fs');
const path = require('path');

// 生成SEO报告
function generateSeoReport() {
  const report = {
    timestamp: new Date().toISOString(),
    checks: {
      robotsTxt: checkRobotsTxt(),
      sitemap: checkSitemap(),
      metaTags: checkMetaTags(),
      structuredData: checkStructuredData(),
      performance: checkPerformance()
    }
  };

  // 保存报告
  fs.writeFileSync(
    path.join(__dirname, '../seo-report.json'),
    JSON.stringify(report, null, 2)
  );

  console.log('SEO报告已生成:', report);
  return report;
}

// 检查robots.txt
function checkRobotsTxt() {
  const robotsPath = path.join(__dirname, '../public/robots.txt');
  return {
    exists: fs.existsSync(robotsPath),
    content: fs.existsSync(robotsPath) ? fs.readFileSync(robotsPath, 'utf8') : null
  };
}

// 检查sitemap
function checkSitemap() {
  const sitemapPath = path.join(__dirname, '../public/sitemap.xml');
  const sitemapZhPath = path.join(__dirname, '../public/sitemap-zh.xml');
  
  return {
    mainSitemap: fs.existsSync(sitemapPath),
    zhSitemap: fs.existsSync(sitemapZhPath),
    count: fs.existsSync(sitemapPath) ? 
      (fs.readFileSync(sitemapPath, 'utf8').match(/<url>/g) || []).length : 0
  };
}

// 检查meta标签配置
function checkMetaTags() {
  const configPath = path.join(__dirname, '../nuxt.config.ts');
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  return {
    hasDescription: configContent.includes('name: \'description\''),
    hasKeywords: configContent.includes('name: \'keywords\''),
    hasOgTags: configContent.includes('property: \'og:'),
    hasTwitterCards: configContent.includes('name: \'twitter:'),
    hasBaiduVerification: configContent.includes('baidu-site-verification')
  };
}

// 检查结构化数据
function checkStructuredData() {
  const structuredDataPath = path.join(__dirname, '../composables/useSeoStructuredData.ts');
  return {
    exists: fs.existsSync(structuredDataPath),
    hasOrganization: fs.existsSync(structuredDataPath) && 
      fs.readFileSync(structuredDataPath, 'utf8').includes('Organization'),
    hasWebsite: fs.existsSync(structuredDataPath) && 
      fs.readFileSync(structuredDataPath, 'utf8').includes('WebSite')
  };
}

// 检查性能配置
function checkPerformance() {
  const nginxPath = path.join(__dirname, '../nginx.conf');
  const nginxContent = fs.readFileSync(nginxPath, 'utf8');
  
  return {
    hasGzip: nginxContent.includes('gzip on'),
    hasCaching: nginxContent.includes('expires'),
    hasImageOptimization: nginxContent.includes('jpg|jpeg|png')
  };
}

// 生成关键词密度报告
function analyzeKeywordDensity(content, keywords) {
  const wordCount = content.split(/\s+/).length;
  const keywordStats = {};
  
  keywords.forEach(keyword => {
    const regex = new RegExp(keyword, 'gi');
    const matches = content.match(regex) || [];
    keywordStats[keyword] = {
      count: matches.length,
      density: ((matches.length / wordCount) * 100).toFixed(2) + '%'
    };
  });
  
  return keywordStats;
}

// 检查页面SEO健康度
function checkPageSeoHealth(pageContent) {
  const checks = {
    hasTitle: /<title>.*<\/title>/.test(pageContent),
    hasMetaDescription: /name="description"/.test(pageContent),
    hasH1: /<h1.*?>/.test(pageContent),
    hasAltTags: /<img[^>]+alt=/.test(pageContent),
    hasStructuredData: /application\/ld\+json/.test(pageContent),
    hasCanonical: /rel="canonical"/.test(pageContent)
  };
  
  const score = Object.values(checks).filter(Boolean).length;
  const maxScore = Object.keys(checks).length;
  
  return {
    checks,
    score: `${score}/${maxScore}`,
    percentage: Math.round((score / maxScore) * 100)
  };
}

// 导出函数
module.exports = {
  generateSeoReport,
  analyzeKeywordDensity,
  checkPageSeoHealth
};

// 如果直接运行此脚本
if (require.main === module) {
  generateSeoReport();
}
