/**
 * 文章构建器主控制器
 * 协调各个服务组件，管理整个构建流程
 */

const ArticleDataFetcher = require('./services/ArticleDataFetcher');
const TemplateProcessor = require('./services/TemplateProcessor');
const FileGenerator = require('./services/FileGenerator');
const { CATEGORY_PATH_MAPPING } = require('./config');
const { logError, logInfo } = require('./utils');

class ArticleBuilder {
  constructor() {
    this.dataFetcher = new ArticleDataFetcher();
    this.templateProcessor = new TemplateProcessor();
    this.fileGenerator = new FileGenerator();
  }

  /**
   * 处理单篇文章
   * @param {Object} post - 文章数据
   */
  async processArticle(post) {
    try {
      if (!post.categories || !post.categories.nodes) {
        logError(`Post ${post.id} has no categories`, new Error('Missing categories'));
        return;
      }

      // 处理每个分类
      for (const category of post.categories.nodes) {
        const categoryId = category.categoryId;
        const mapping = CATEGORY_PATH_MAPPING[categoryId];

        if (!mapping) {
          // 跳过未配置的分类
          continue;
        }

        try {
          // 确定需要生成的语言
          const languages = [mapping.language];
          if (mapping.generateJapanese) {
            languages.push('ja');
          }

          // 处理模板
          const processedTemplates = this.templateProcessor.processMultiLanguageTemplates(
            post, 
            categoryId, 
            languages
          );

          // 生成文件
          this.fileGenerator.processArticleFiles(post, categoryId, processedTemplates);

          logInfo(`Processed article: ${post.title} (Category: ${categoryId})`);
        } catch (error) {
          logError(`Failed to process article ${post.id} for category ${categoryId}`, error);
          // 继续处理其他分类，不中断整个流程
        }
      }
    } catch (error) {
      logError(`Failed to process article ${post.id}`, error);
    }
  }

  /**
   * 构建所有文章
   * @param {Object} queryOptions - 查询选项
   */
  async buildArticles(queryOptions = {}) {
    try {
      logInfo('Starting article build process...');

      // 验证模板文件
      const templateValidation = this.templateProcessor.validateTemplates();
      const invalidTemplates = Object.entries(templateValidation)
        .filter(([, result]) => !result.exists);

      if (invalidTemplates.length > 0) {
        logError('Some template files are missing:', new Error('Template validation failed'));
        invalidTemplates.forEach(([type, result]) => {
          logError(`  ${type}: ${result.path}`, new Error(result.error));
        });
        throw new Error('Template validation failed');
      }

      // 验证输出配置
      const outputValidation = this.fileGenerator.validateOutputConfig();
      logInfo('Output configuration validated');

      // 清空之前的文章列表
      this.fileGenerator.clearArticleLists();

      // 获取所有文章数据
      logInfo('Fetching articles from server...');
      const articles = await this.dataFetcher.fetchAllArticles(queryOptions);

      if (articles.length === 0) {
        logInfo('No articles found');
        return;
      }

      // 处理每篇文章
      logInfo(`Processing ${articles.length} articles...`);
      for (const article of articles) {
        await this.processArticle(article);
      }

      // 生成文章列表文件
      logInfo('Generating article list files...');
      this.fileGenerator.generateArticleListFiles();

      // 输出统计信息
      const stats = this.fileGenerator.getStatistics();
      logInfo('Build completed successfully!');
      logInfo('Statistics:');
      Object.entries(stats).forEach(([language, types]) => {
        Object.entries(types).forEach(([type, count]) => {
          if (count > 0) {
            logInfo(`  ${language}/${type}: ${count} articles`);
          }
        });
      });

    } catch (error) {
      logError('Article build process failed', error);
      throw error;
    }
  }

  /**
   * 获取构建统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    return {
      articles: this.fileGenerator.getStatistics(),
      pagination: this.dataFetcher.getPaginationState(),
      templates: this.templateProcessor.getCacheInfo()
    };
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.templateProcessor.clearCache();
    this.fileGenerator.clearArticleLists();
    this.dataFetcher.resetPagination();
  }

  /**
   * 验证构建环境
   * @returns {Object} 验证结果
   */
  validateEnvironment() {
    return {
      templates: this.templateProcessor.validateTemplates(),
      output: this.fileGenerator.validateOutputConfig(),
      api: {
        endpoint: this.dataFetcher.client.url,
        configured: true
      }
    };
  }
}

module.exports = ArticleBuilder;
