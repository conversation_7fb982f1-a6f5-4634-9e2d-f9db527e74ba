/**
 * 文件生成服务
 * 负责 Vue 文件和 JSON 文件的生成，包括目录创建
 */

const path = require('path');
const { OUTPUT_CONFIG, CATEGORY_PATH_MAPPING } = require('../config');
const { safeWriteFile, createArticleListItem, generateFilePath, logError, logInfo } = require('../utils');

class FileGenerator {
  constructor() {
    // 初始化文章列表存储
    this.articleLists = {
      zh: {
        countries: [],
        articles: [],
        marketing: [],
        legal: []
      },
      en: {
        countries: [],
        articles: [],
        marketing: [],
        legal: []
      },
      ja: {
        countries: [],
        articles: [],
        marketing: [],
        legal: []
      }
    };
  }

  /**
   * 生成Vue文件
   * @param {string} content - Vue文件内容
   * @param {string} language - 语言代码
   * @param {string} type - 文件类型 (countries, articles, marketing, legal)
   * @param {string} fileName - 文件名
   */
  generateVueFile(content, language, type, fileName) {
    try {
      const basePath = OUTPUT_CONFIG.pages[language][type];
      const filePath = generateFilePath(basePath, fileName, '.vue');
      
      safeWriteFile(filePath, content);
      logInfo(`Generated Vue file: ${filePath}`);
    } catch (error) {
      logError(`Failed to generate Vue file for ${language}/${type}/${fileName}`, error);
      throw error;
    }
  }

  /**
   * 添加文章到列表
   * @param {Object} post - 文章数据
   * @param {string} language - 语言代码
   * @param {string} type - 文件类型
   */
  addToArticleList(post, language, type) {
    try {
      const articleItem = createArticleListItem(post);
      const fileName = post.countryGuideExternal?.fileName;
      
      if (!fileName) {
        logError(`Missing fileName for post ${post.id}`, new Error('fileName is required'));
        return;
      }

      // 添加链接信息
      articleItem.link = `/${language}/${type}/${fileName}`;
      
      // 添加到对应的列表
      if (this.articleLists[language] && this.articleLists[language][type]) {
        this.articleLists[language][type].push(articleItem);
        logInfo(`Added article to ${language}/${type} list: ${post.title}`);
      } else {
        logError(`Invalid language/type combination: ${language}/${type}`, new Error('Invalid path'));
      }
    } catch (error) {
      logError(`Failed to add article to list: ${post.id}`, error);
    }
  }

  /**
   * 处理单个文章的文件生成
   * @param {Object} post - 文章数据
   * @param {number} categoryId - 分类ID
   * @param {Object} processedTemplates - 处理后的模板内容
   */
  processArticleFiles(post, categoryId, processedTemplates) {
    const mapping = CATEGORY_PATH_MAPPING[categoryId];
    if (!mapping) {
      logError(`No mapping found for category ${categoryId}`, new Error('Category mapping not found'));
      return;
    }

    const { language, type, generateJapanese } = mapping;
    const fileName = post.countryGuideExternal?.fileName;

    if (!fileName) {
      logError(`Missing fileName for post ${post.id}`, new Error('fileName is required'));
      return;
    }

    try {
      // 生成主语言文件
      if (processedTemplates[language]) {
        this.generateVueFile(processedTemplates[language], language, type, fileName);
        this.addToArticleList(post, language, type);
      }

      // 生成日语文件（如果需要）
      if (generateJapanese && processedTemplates.ja) {
        this.generateVueFile(processedTemplates.ja, 'ja', type, fileName);
        this.addToArticleList(post, 'ja', type);
      }
    } catch (error) {
      logError(`Failed to process article files for post ${post.id}`, error);
    }
  }

  /**
   * 生成所有文章列表JSON文件
   */
  generateArticleListFiles() {
    try {
      Object.entries(this.articleLists).forEach(([language, types]) => {
        Object.entries(types).forEach(([type, articles]) => {
          if (articles.length > 0) {
            const listPath = OUTPUT_CONFIG.lists[language][type];
            const jsonContent = JSON.stringify(articles, null, 2);
            
            safeWriteFile(listPath, jsonContent);
            logInfo(`Generated article list: ${listPath} (${articles.length} articles)`);
          }
        });
      });
    } catch (error) {
      logError('Failed to generate article list files', error);
      throw error;
    }
  }

  /**
   * 获取文章列表统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    const stats = {};
    
    Object.entries(this.articleLists).forEach(([language, types]) => {
      stats[language] = {};
      Object.entries(types).forEach(([type, articles]) => {
        stats[language][type] = articles.length;
      });
    });

    return stats;
  }

  /**
   * 清空所有文章列表
   */
  clearArticleLists() {
    Object.keys(this.articleLists).forEach(language => {
      Object.keys(this.articleLists[language]).forEach(type => {
        this.articleLists[language][type] = [];
      });
    });
  }

  /**
   * 验证输出目录配置
   * @returns {Object} 验证结果
   */
  validateOutputConfig() {
    const results = {
      pages: {},
      lists: {}
    };

    // 验证页面目录
    Object.entries(OUTPUT_CONFIG.pages).forEach(([language, types]) => {
      results.pages[language] = {};
      Object.entries(types).forEach(([type, path]) => {
        results.pages[language][type] = {
          path,
          exists: require('fs').existsSync(path)
        };
      });
    });

    // 验证列表文件目录
    Object.entries(OUTPUT_CONFIG.lists).forEach(([language, types]) => {
      results.lists[language] = {};
      Object.entries(types).forEach(([type, filePath]) => {
        const dir = require('path').dirname(filePath);
        results.lists[language][type] = {
          path: filePath,
          dirExists: require('fs').existsSync(dir)
        };
      });
    });

    return results;
  }
}

module.exports = FileGenerator;
