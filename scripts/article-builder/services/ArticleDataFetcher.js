/**
 * 文章数据获取服务
 * 负责从 GraphQL API 获取文章数据，支持分页和错误处理
 */

const { GraphQLClient } = require('graphql-request');
const { API_CONFIG } = require('../config');
const { logError, logInfo } = require('../utils');

class ArticleDataFetcher {
  /**
   * 构造函数
   * @param {string} endpoint - GraphQL API 端点
   * @param {number} pageSize - 每页数据量
   */
  constructor(endpoint = API_CONFIG.endpoint, pageSize = API_CONFIG.pageSize) {
    this.client = new GraphQLClient(endpoint, {});
    this.pageSize = pageSize;
    this.hasNextPage = true;
    this.afterCursor = '';
  }

  /**
   * 构建 GraphQL 查询语句
   * @param {Object} where - 查询条件
   * @param {string} after - 分页游标
   * @param {number} limit - 数据限制
   * @returns {string} GraphQL 查询语句
   */
  buildGraphQLQuery(where, after, limit) {
    let condition = `first: ${limit}`;
    
    if (after) {
      condition += `, after: "${after}"`;
    }
    
    const whereJsonString = JSON.stringify(where);
    condition += `, where: ${whereJsonString.replace(/"/g, '')}`;

    return `
      query articleQuery {
        posts(${condition}) {
          nodes {
            id
            title
            slug
            categories {
              nodes {
                categoryId
              }
            }
            seo {
              metaDesc
              metaKeywords
              opengraphDescription
              opengraphSiteName
              opengraphType
              title
            }
            featuredImage {
              node {
                sourceUrl(size: LARGE)
              }
            }
            countryGuideExternal {
              listingImage {
                node {
                  sourceUrl(size: LARGE)
                }
              }
              countryFlagImage {
                node {
                  sourceUrl(size: LARGE)
                }
              }
              countryName
              fileName
            }
            content
          }
          pageInfo {
            startCursor
            hasPreviousPage
            hasNextPage
            endCursor
          }
        }
      }
    `;
  }

  /**
   * 获取单页文章数据
   * @param {Object} queryOptions - 查询选项
   * @returns {Promise<Object>} 文章数据和分页信息
   */
  async fetchPage(queryOptions = {}) {
    const defaultOptions = {
      orderby: {
        field: 'DATE',
        order: 'DESC'
      }
    };

    const options = { ...defaultOptions, ...queryOptions };
    const query = this.buildGraphQLQuery(options, this.afterCursor, this.pageSize);

    try {
      logInfo(`Fetching articles page with cursor: ${this.afterCursor || 'initial'}`);
      const data = await this.client.request(query);
      
      // 更新分页状态
      this.hasNextPage = data.posts.pageInfo.hasNextPage;
      this.afterCursor = data.posts.pageInfo.endCursor;

      logInfo(`Fetched ${data.posts.nodes.length} articles, hasNextPage: ${this.hasNextPage}`);
      
      return {
        posts: data.posts.nodes,
        pageInfo: data.posts.pageInfo
      };
    } catch (error) {
      logError('Failed to fetch articles from server', error);
      
      // 重置分页状态以防止无限循环
      this.hasNextPage = false;
      this.afterCursor = '';
      
      throw new Error(`GraphQL request failed: ${error.message}`);
    }
  }

  /**
   * 获取所有文章数据（自动处理分页）
   * @param {Object} queryOptions - 查询选项
   * @returns {Promise<Array>} 所有文章数据
   */
  async fetchAllArticles(queryOptions = {}) {
    const allArticles = [];
    
    // 重置分页状态
    this.hasNextPage = true;
    this.afterCursor = '';

    try {
      while (this.hasNextPage) {
        const { posts } = await this.fetchPage(queryOptions);
        allArticles.push(...posts);
      }

      logInfo(`Successfully fetched ${allArticles.length} articles in total`);
      return allArticles;
    } catch (error) {
      logError('Failed to fetch all articles', error);
      throw error;
    }
  }

  /**
   * 重置分页状态
   */
  resetPagination() {
    this.hasNextPage = true;
    this.afterCursor = '';
  }

  /**
   * 获取当前分页状态
   * @returns {Object} 分页状态
   */
  getPaginationState() {
    return {
      hasNextPage: this.hasNextPage,
      afterCursor: this.afterCursor
    };
  }
}

module.exports = ArticleDataFetcher;
