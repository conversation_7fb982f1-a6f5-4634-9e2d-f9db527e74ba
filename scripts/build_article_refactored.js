#!/usr/bin/env node

/**
 * 重构后的文章构建脚本
 * 
 * 这个脚本保持与原始 build_article.js 完全相同的功能，但使用了重构后的模块化架构。
 * 
 * 主要改进：
 * - 模块化设计，职责分离
 * - 配置驱动，消除硬编码
 * - 错误处理和日志记录
 * - 可测试性和可维护性
 * 
 * 使用方法：
 * node scripts/build_article_refactored.js
 */

const path = require('path');
const ArticleBuilder = require('./article-builder/ArticleBuilder');
const { logError, logInfo } = require('./article-builder/utils');

/**
 * 主函数
 */
async function main() {
  const startTime = Date.now();
  
  try {
    logInfo('='.repeat(50));
    logInfo('Starting Article Build Process (Refactored)');
    logInfo('='.repeat(50));

    // 创建文章构建器实例
    const builder = new ArticleBuilder();

    // 验证构建环境
    logInfo('Validating build environment...');
    const validation = builder.validateEnvironment();
    
    // 检查模板文件
    const invalidTemplates = Object.entries(validation.templates)
      .filter(([, result]) => !result.exists);
    
    if (invalidTemplates.length > 0) {
      logError('Template validation failed. Missing templates:', new Error('Validation failed'));
      invalidTemplates.forEach(([type, result]) => {
        logError(`  ${type}: ${result.path}`, new Error(result.error));
      });
      process.exit(1);
    }

    logInfo('Environment validation passed');

    // 执行构建
    await builder.buildArticles({
      orderby: {
        field: 'DATE',
        order: 'DESC'
      }
    });

    // 输出最终统计信息
    const stats = builder.getStatistics();
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    logInfo('='.repeat(50));
    logInfo('Build Process Completed Successfully!');
    logInfo(`Total time: ${duration} seconds`);
    logInfo('='.repeat(50));

    // 清理资源
    builder.cleanup();

  } catch (error) {
    logError('Build process failed', error);
    process.exit(1);
  }
}

/**
 * 处理未捕获的异常
 */
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:', promise);
  logError('Reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError('Uncaught Exception:', error);
  process.exit(1);
});

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
