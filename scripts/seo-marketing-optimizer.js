#!/usr/bin/env node

/**
 * SmartDeer营销页面和文章页面SEO优化工具
 * 专门处理营销页面和文章页面的SEO配置
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class MarketingSEOOptimizer {
  constructor() {
    this.optimizations = [];
    this.marketingTemplates = this.loadMarketingTemplates();
  }

  // 加载营销页面SEO模板
  loadMarketingTemplates() {
    return {
      zh: {
        'SmartDeer-with-E-town-Link-The-World': {
          title: 'SmartDeer携手亦庄国投链接世界 - 助力企业全球化扩张 | 出海合作案例',
          description: 'SmartDeer与亦庄国投达成战略合作，为出海企业提供全球雇佣、国际招聘、合规管理等一站式人力资源服务。通过专业EOR服务，助力中国企业安全合规地进行海外扩张，降低国际化风险。',
          keywords: 'SmartDeer亦庄国投, 出海合作, 全球雇佣服务, 企业国际化, EOR合作, 海外扩张, 国际招聘, 合规管理, 出海企业服务'
        },
        'SmartDeer-Joins-CASFT': {
          title: 'SmartDeer加入中国服务贸易协会 - 推动全球服务贸易发展 | 行业合作',
          description: 'SmartDeer正式加入中国服务贸易协会，致力于推动全球服务贸易发展。作为专业的全球人力资源服务提供商，我们将为更多企业提供国际化人才解决方案，促进中国服务贸易走向世界。',
          keywords: 'SmartDeer中国服务贸易协会, 服务贸易, 全球人力资源, 国际化人才, 出海服务, 贸易合作, 人才服务, 国际招聘'
        },
        'SmartDeer-HK-Guide': {
          title: 'SmartDeer香港雇佣指南 - 香港劳动法规与HR合规完整攻略',
          description: 'SmartDeer香港雇佣指南，详细解读香港劳动法规、雇佣条例、薪酬福利、税务政策等关键信息。为企业在香港设立分支机构、招聘员工提供专业指导，确保合规运营，降低法律风险。',
          keywords: '香港雇佣指南, 香港劳动法, 香港HR合规, 香港雇佣条例, 香港薪酬, 香港税务, 香港招聘, 香港分公司设立'
        },
        'SingaporeDTSP_Policy_Impact_Global_Compliance_Employment_Sharing': {
          title: '新加坡DTSP政策对全球合规雇佣的影响分析 - SmartDeer专业解读',
          description: '深度分析新加坡DTSP（数字贸易与服务伙伴关系）政策对全球合规雇佣的影响。SmartDeer专业团队解读政策变化，为企业在新加坡及东南亚地区的人力资源布局提供战略指导。',
          keywords: '新加坡DTSP政策, 全球合规雇佣, 数字贸易政策, 东南亚HR政策, 新加坡雇佣法规, 国际合规, 政策影响分析'
        },
        'Oversea-with-SmartDeer-Tongshang-and-NBCB': {
          title: 'SmartDeer携手通商银行助力企业出海 - 金融+HR一站式服务',
          description: 'SmartDeer与通商银行、宁波银行达成合作，为出海企业提供金融+HR一站式服务。结合专业的全球雇佣服务和金融支持，为中国企业海外扩张提供全方位解决方案。',
          keywords: 'SmartDeer通商银行合作, 出海金融服务, 企业海外扩张, 金融HR服务, 银行合作, 出海一站式服务, 国际化金融支持'
        }
      },
      en: {
        'SmartDeer-with-E-town-Link-The-World': {
          title: 'SmartDeer Partners with E-town to Link the World - Global Expansion Solutions',
          description: 'SmartDeer partners with E-town Investment to provide comprehensive global employment, international recruitment, and compliance management services for expanding businesses. Professional EOR services help Chinese companies expand overseas safely and compliantly.',
          keywords: 'SmartDeer E-town partnership, global expansion, international employment services, EOR collaboration, overseas expansion, international recruitment, compliance management'
        }
      }
    };
  }

  // 优化营销页面
  async optimizeMarketingPages() {
    console.log('🔧 开始优化营销页面...');

    const marketingDir = 'pages/zh/marketing';
    if (!fs.existsSync(marketingDir)) {
      console.log('❌ 营销页面目录不存在');
      return;
    }

    const marketingFiles = glob.sync('*.vue', { cwd: marketingDir });
    console.log(`📝 找到 ${marketingFiles.length} 个营销页面`);

    for (const file of marketingFiles) {
      const fileName = file.replace('.vue', '');
      if (this.marketingTemplates.zh[fileName]) {
        await this.optimizeMarketingPage(path.join(marketingDir, file), fileName, 'zh');
      } else {
        await this.generateMarketingSEO(path.join(marketingDir, file), fileName, 'zh');
      }
    }

    console.log('✅ 营销页面优化完成');
  }

  // 优化单个营销页面
  async optimizeMarketingPage(filePath, fileName, language) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const template = this.marketingTemplates[language][fileName];

      if (!template) return;

      let modified = false;

      // 检查是否已有useHead配置
      if (content.includes('useHead(')) {
        // 更新现有配置
        content = content.replace(
          /title:\s*['"`]([^'"`]*?)['"`]/,
          `title: '${template.title}'`
        );
        content = content.replace(
          /name:\s*['"`]description['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/,
          `name: 'description', content: '${template.description}'`
        );
        content = content.replace(
          /name:\s*['"`]keywords['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/,
          `name: 'keywords', content: '${template.keywords}'`
        );
        modified = true;
      } else {
        // 添加新的SEO配置
        const seoConfig = this.generateMarketingSEOConfig(template, filePath, language);
        const scriptMatch = content.match(/<script[^>]*setup[^>]*>/);
        
        if (scriptMatch) {
          const insertPosition = content.indexOf(scriptMatch[0]) + scriptMatch[0].length;
          content = content.slice(0, insertPosition) + `\n\n${seoConfig}\n` + content.slice(insertPosition);
          modified = true;
        }
      }

      if (modified) {
        fs.writeFileSync(filePath, content);
        this.optimizations.push(`✅ 优化营销页面: ${filePath}`);
        console.log(`✅ 优化营销页面: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ 优化营销页面失败 ${filePath}:`, error.message);
    }
  }

  // 为没有模板的营销页面生成SEO
  async generateMarketingSEO(filePath, fileName, language) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes('useHead(')) {
        console.log(`⚠️  页面已有SEO配置: ${filePath}`);
        return;
      }

      // 从文件名生成SEO信息
      const seoInfo = this.generateSEOFromFileName(fileName, language);
      const seoConfig = this.generateMarketingSEOConfig(seoInfo, filePath, language);
      
      const scriptMatch = content.match(/<script[^>]*setup[^>]*>/);
      if (scriptMatch) {
        const insertPosition = content.indexOf(scriptMatch[0]) + scriptMatch[0].length;
        content = content.slice(0, insertPosition) + `\n\n${seoConfig}\n` + content.slice(insertPosition);
        
        fs.writeFileSync(filePath, content);
        this.optimizations.push(`✅ 生成营销页面SEO: ${filePath}`);
        console.log(`✅ 生成营销页面SEO: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ 生成营销页面SEO失败 ${filePath}:`, error.message);
    }
  }

  // 从文件名生成SEO信息
  generateSEOFromFileName(fileName, language) {
    // 将文件名转换为可读标题
    const title = fileName
      .replace(/-/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .trim()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    const templates = {
      zh: {
        title: `${title} - SmartDeer全球HR解决方案 | 出海企业服务案例`,
        description: `了解SmartDeer ${title}相关的全球人力资源服务案例。我们为出海企业提供专业的EOR服务、国际招聘、合规管理等解决方案，助力企业成功进行海外扩张，覆盖150+国家和地区。`,
        keywords: `${title}, SmartDeer, 全球HR服务, 出海企业, EOR服务, 国际招聘, 海外扩张, 合规管理, 全球雇佣, 人力资源解决方案`
      },
      en: {
        title: `${title} - SmartDeer Global HR Solutions | International Business Services`,
        description: `Learn about SmartDeer ${title} related global HR service cases. We provide professional EOR services, international recruitment, compliance management for expanding businesses across 150+ countries and regions.`,
        keywords: `${title}, SmartDeer, global HR services, international business, EOR services, international recruitment, overseas expansion, compliance management`
      }
    };

    return templates[language] || templates.zh;
  }

  // 生成营销页面SEO配置
  generateMarketingSEOConfig(template, filePath, language) {
    const langCode = language === 'zh' ? 'zh-CN' : language === 'ja' ? 'ja-JP' : 'en-US';
    const url = this.getPageURL(filePath);

    return `definePageMeta({ layout: 'basic' })
useHead({
  htmlAttrs: { lang: '${langCode}' },
  title: '${template.title}',
  meta: [
    { name: 'description', content: '${template.description}' },
    { name: 'keywords', content: '${template.keywords}' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
    
    // Open Graph
    { property: 'og:title', content: '${template.title}' },
    { property: 'og:description', content: '${template.description}' },
    { property: 'og:type', content: 'article' },
    { property: 'og:url', content: '${url}' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: '${langCode.replace('-', '_')}' },
    
    // Twitter Card
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '${template.title}' },
    { name: 'twitter:description', content: '${template.description}' }
  ]
})`;
  }

  // 获取页面URL
  getPageURL(filePath) {
    const baseUrl = 'https://www.smartdeer.work';
    let cleanPath = filePath.replace('pages/', '').replace('.vue', '');
    
    if (cleanPath.endsWith('/index')) {
      cleanPath = cleanPath.replace('/index', '');
    }
    
    return `${baseUrl}/${cleanPath}`;
  }

  // 优化文章页面
  async optimizeArticlePages() {
    console.log('🔧 开始优化文章页面...');

    const languages = ['zh', 'en', 'ja'];
    
    for (const lang of languages) {
      const articlesDir = `pages/${lang}/articles`;
      if (fs.existsSync(articlesDir)) {
        await this.optimizeLanguageArticles(articlesDir, lang);
      }
    }

    console.log('✅ 文章页面优化完成');
  }

  // 优化特定语言的文章页面
  async optimizeLanguageArticles(articlesDir, language) {
    const articleFiles = glob.sync('**/*.vue', { cwd: articlesDir });
    console.log(`📝 找到 ${articleFiles.length} 个${language}文章页面`);

    for (const file of articleFiles.slice(0, 5)) { // 限制处理前5个文章
      await this.optimizeArticlePage(path.join(articlesDir, file), file, language);
    }
  }

  // 优化单个文章页面
  async optimizeArticlePage(filePath, fileName, language) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes('useHead(')) {
        console.log(`⚠️  文章页面已有SEO配置: ${filePath}`);
        return;
      }

      const seoInfo = this.generateArticleSEO(fileName, language);
      const seoConfig = this.generateArticleSEOConfig(seoInfo, filePath, language);
      
      const scriptMatch = content.match(/<script[^>]*setup[^>]*>/);
      if (scriptMatch) {
        const insertPosition = content.indexOf(scriptMatch[0]) + scriptMatch[0].length;
        content = content.slice(0, insertPosition) + `\n\n${seoConfig}\n` + content.slice(insertPosition);
        
        fs.writeFileSync(filePath, content);
        this.optimizations.push(`✅ 优化文章页面: ${filePath}`);
        console.log(`✅ 优化文章页面: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ 优化文章页面失败 ${filePath}:`, error.message);
    }
  }

  // 生成文章SEO信息
  generateArticleSEO(fileName, language) {
    const title = fileName
      .replace('.vue', '')
      .replace(/-/g, ' ')
      .split('/')
      .pop()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    const templates = {
      zh: {
        title: `${title} - SmartDeer全球HR专业解读 | 出海企业必读`,
        description: `SmartDeer专业解读${title}相关的全球人力资源政策、法规变化和最佳实践。为出海企业提供权威的HR合规指导，助力企业在国际化过程中规避风险，实现可持续发展。`,
        keywords: `${title}, 全球HR政策, 出海企业指南, 国际雇佣法规, HR合规, 海外扩张, SmartDeer专业解读, 全球人力资源, 国际化风险管理`
      },
      en: {
        title: `${title} - SmartDeer Global HR Professional Analysis | Essential for International Business`,
        description: `SmartDeer professional analysis of ${title} related global HR policies, regulatory changes and best practices. Providing authoritative HR compliance guidance for expanding businesses to mitigate risks in internationalization.`,
        keywords: `${title}, global HR policies, international business guide, employment regulations, HR compliance, overseas expansion, SmartDeer analysis, global human resources`
      },
      ja: {
        title: `${title} - SmartDeerグローバルHR専門解説 | 海外展開企業必読`,
        description: `SmartDeerによる${title}に関するグローバル人事政策、規制変更、ベストプラクティスの専門解説。海外展開企業に権威あるHRコンプライアンスガイダンスを提供し、国際化プロセスでのリスク回避を支援。`,
        keywords: `${title}, グローバルHR政策, 海外展開企業ガイド, 雇用規制, HRコンプライアンス, 海外展開, SmartDeer専門解説, グローバル人事`
      }
    };

    return templates[language] || templates.zh;
  }

  // 生成文章SEO配置
  generateArticleSEOConfig(template, filePath, language) {
    const langCode = language === 'zh' ? 'zh-CN' : language === 'ja' ? 'ja-JP' : 'en-US';
    const url = this.getPageURL(filePath);

    return `definePageMeta({ layout: 'basic' })
useHead({
  htmlAttrs: { lang: '${langCode}' },
  title: '${template.title}',
  meta: [
    { name: 'description', content: '${template.description}' },
    { name: 'keywords', content: '${template.keywords}' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
    
    // Open Graph
    { property: 'og:title', content: '${template.title}' },
    { property: 'og:description', content: '${template.description}' },
    { property: 'og:type', content: 'article' },
    { property: 'og:url', content: '${url}' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: '${langCode.replace('-', '_')}' },
    
    // Twitter Card
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '${template.title}' },
    { name: 'twitter:description', content: '${template.description}' },
    
    // Article specific
    { property: 'article:author', content: 'SmartDeer' },
    { property: 'article:section', content: 'Global HR' },
    { property: 'article:tag', content: 'Global Employment, International HR, EOR Services' }
  ]
})`;
  }

  // 运行所有优化
  async runOptimizations() {
    console.log('🚀 开始营销页面和文章页面SEO优化...\n');
    
    await this.optimizeMarketingPages();
    await this.optimizeArticlePages();
    
    console.log('\n📊 优化总结:');
    this.optimizations.forEach(opt => console.log(opt));
    console.log(`\n✅ 总共完成 ${this.optimizations.length} 项优化`);
  }
}

// 运行优化
if (require.main === module) {
  const optimizer = new MarketingSEOOptimizer();
  optimizer.runOptimizations().catch(console.error);
}

module.exports = MarketingSEOOptimizer;
