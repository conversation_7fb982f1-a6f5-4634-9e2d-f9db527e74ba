/**
 * graphql-request 模块的模拟实现
 */

// 模拟 GraphQL 响应数据
const mockArticleData = {
  posts: {
    nodes: [
      {
        id: '1',
        title: 'Test Article 1',
        slug: 'test-article-1',
        categories: {
          nodes: [{ categoryId: 4 }]
        },
        seo: {
          metaDesc: 'Test description',
          opengraphDescription: 'Test OG description',
          opengraphSiteName: 'Test Site'
        },
        featuredImage: {
          node: {
            sourceUrl: 'https://example.com/image1.jpg'
          }
        },
        countryGuideExternal: {
          listingImage: 'https://example.com/listing1.jpg',
          countryFlagImage: {
            node: {
              sourceUrl: 'https://example.com/flag1.jpg'
            }
          },
          countryName: 'Test Country',
          fileName: 'test-article-1'
        },
        content: 'Test content for article 1'
      },
      {
        id: '2',
        title: 'Test Article 2',
        slug: 'test-article-2',
        categories: {
          nodes: [{ categoryId: 5 }]
        },
        seo: {
          metaDesc: 'Test description 2',
          opengraphDescription: 'Test OG description 2',
          opengraphSiteName: 'Test Site'
        },
        featuredImage: {
          node: {
            sourceUrl: 'https://example.com/image2.jpg'
          }
        },
        countryGuideExternal: {
          listingImage: 'https://example.com/listing2.jpg',
          countryFlagImage: {
            node: {
              sourceUrl: 'https://example.com/flag2.jpg'
            }
          },
          countryName: 'Test Country 2',
          fileName: 'test-article-2'
        },
        content: 'Test content for article 2'
      }
    ],
    pageInfo: {
      startCursor: 'start',
      hasPreviousPage: false,
      hasNextPage: false,
      endCursor: 'end'
    }
  }
};

class MockGraphQLClient {
  constructor(endpoint, options) {
    this.url = endpoint;
    this.options = options;
  }

  async request(query) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 10));
    
    // 根据查询返回不同的数据
    if (query.includes('first: 10')) {
      return mockArticleData;
    }
    
    return mockArticleData;
  }
}

// 模拟错误情况的客户端
class MockGraphQLClientWithError {
  constructor(endpoint, options) {
    this.url = endpoint;
    this.options = options;
  }

  async request(query) {
    throw new Error('GraphQL request failed');
  }
}

module.exports = {
  GraphQLClient: MockGraphQLClient,
  MockGraphQLClientWithError
};
