/**
 * fs 模块的模拟实现
 */

const fs = jest.createMockFromModule('fs');

// 模拟文件系统状态
let mockFiles = {};

// 模拟文件存在性检查
fs.existsSync = jest.fn((path) => {
  return mockFiles.hasOwnProperty(path);
});

// 模拟文件读取
fs.readFileSync = jest.fn((path, encoding) => {
  if (!mockFiles[path]) {
    const error = new Error(`ENOENT: no such file or directory, open '${path}'`);
    error.code = 'ENOENT';
    throw error;
  }
  return mockFiles[path];
});

// 模拟文件写入
fs.writeFileSync = jest.fn((path, data) => {
  // 检查是否有错误模拟设置
  if (fs.__shouldThrowWriteError) {
    throw new Error('Write failed');
  }
  mockFiles[path] = data;
});

// 模拟目录创建
fs.mkdirSync = jest.fn();

// 辅助函数：设置模拟文件
fs.__setMockFiles = (newMockFiles) => {
  mockFiles = { ...newMockFiles };
};

// 辅助函数：获取模拟文件
fs.__getMockFiles = () => mockFiles;

// 辅助函数：清空模拟文件
fs.__clearMockFiles = () => {
  mockFiles = {};
};

// 辅助函数：设置写入错误
fs.__shouldThrowWriteError = false;
fs.__setWriteError = (shouldThrow) => {
  fs.__shouldThrowWriteError = shouldThrow;
};

module.exports = fs;
