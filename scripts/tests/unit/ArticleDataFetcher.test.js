/**
 * ArticleDataFetcher 单元测试
 */

jest.mock('graphql-request');

const ArticleDataFetcher = require('../../article-builder/services/ArticleDataFetcher');
const { GraphQLClient, MockGraphQLClientWithError } = require('graphql-request');

describe('ArticleDataFetcher', () => {
  let fetcher;

  beforeEach(() => {
    fetcher = new ArticleDataFetcher();
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    test('should initialize with default values', () => {
      expect(fetcher.pageSize).toBe(10);
      expect(fetcher.hasNextPage).toBe(true);
      expect(fetcher.afterCursor).toBe('');
    });

    test('should initialize with custom values', () => {
      const customFetcher = new ArticleDataFetcher('custom-endpoint', 20);
      expect(customFetcher.pageSize).toBe(20);
    });
  });

  describe('buildGraphQLQuery', () => {
    test('should build query without after cursor', () => {
      const where = { orderby: { field: 'DATE', order: 'DESC' } };
      const query = fetcher.buildGraphQLQuery(where, '', 10);
      
      expect(query).toContain('first: 10');
      expect(query).toContain('where: {orderby:{field:DATE,order:DESC}}');
      expect(query).not.toContain('after:');
    });

    test('should build query with after cursor', () => {
      const where = { orderby: { field: 'DATE', order: 'DESC' } };
      const query = fetcher.buildGraphQLQuery(where, 'cursor123', 10);
      
      expect(query).toContain('first: 10');
      expect(query).toContain('after: "cursor123"');
      expect(query).toContain('where: {orderby:{field:DATE,order:DESC}}');
    });

    test('should include all required fields in query', () => {
      const query = fetcher.buildGraphQLQuery({}, '', 10);
      
      expect(query).toContain('id');
      expect(query).toContain('title');
      expect(query).toContain('categories');
      expect(query).toContain('seo');
      expect(query).toContain('featuredImage');
      expect(query).toContain('countryGuideExternal');
      expect(query).toContain('content');
      expect(query).toContain('pageInfo');
    });
  });

  describe('fetchPage', () => {
    test('should fetch page successfully', async () => {
      const result = await fetcher.fetchPage();
      
      expect(result.posts).toHaveLength(2);
      expect(result.posts[0].id).toBe('1');
      expect(result.posts[1].id).toBe('2');
      expect(result.pageInfo).toBeDefined();
    });

    test('should update pagination state after fetch', async () => {
      await fetcher.fetchPage();
      
      expect(fetcher.hasNextPage).toBe(false);
      expect(fetcher.afterCursor).toBe('end');
    });

    test('should handle GraphQL errors', async () => {
      // 替换客户端为错误版本
      fetcher.client = new MockGraphQLClientWithError();
      
      await expect(fetcher.fetchPage()).rejects.toThrow('GraphQL request failed');
      expect(fetcher.hasNextPage).toBe(false);
      expect(fetcher.afterCursor).toBe('');
    });

    test('should merge query options with defaults', async () => {
      const customOptions = { customField: 'value' };
      const spy = jest.spyOn(fetcher.client, 'request');

      await fetcher.fetchPage(customOptions);

      // 验证请求被调用
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('fetchAllArticles', () => {
    test('should fetch all articles when no pagination', async () => {
      const articles = await fetcher.fetchAllArticles();
      
      expect(articles).toHaveLength(2);
      expect(articles[0].id).toBe('1');
      expect(articles[1].id).toBe('2');
    });

    test('should reset pagination before fetching', async () => {
      fetcher.hasNextPage = false;
      fetcher.afterCursor = 'some-cursor';
      
      const articles = await fetcher.fetchAllArticles();
      
      expect(articles).toHaveLength(2);
    });

    test('should handle errors during fetch all', async () => {
      fetcher.client = new MockGraphQLClientWithError();
      
      await expect(fetcher.fetchAllArticles()).rejects.toThrow('GraphQL request failed');
    });
  });

  describe('resetPagination', () => {
    test('should reset pagination state', () => {
      fetcher.hasNextPage = false;
      fetcher.afterCursor = 'some-cursor';
      
      fetcher.resetPagination();
      
      expect(fetcher.hasNextPage).toBe(true);
      expect(fetcher.afterCursor).toBe('');
    });
  });

  describe('getPaginationState', () => {
    test('should return current pagination state', () => {
      fetcher.hasNextPage = false;
      fetcher.afterCursor = 'test-cursor';
      
      const state = fetcher.getPaginationState();
      
      expect(state).toEqual({
        hasNextPage: false,
        afterCursor: 'test-cursor'
      });
    });
  });
});
