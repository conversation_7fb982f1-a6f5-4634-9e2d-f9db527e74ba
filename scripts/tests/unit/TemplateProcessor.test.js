/**
 * TemplateProcessor 单元测试
 */

jest.mock('fs');

const TemplateProcessor = require('../../article-builder/services/TemplateProcessor');
const fs = require('fs');

describe('TemplateProcessor', () => {
  let processor;

  beforeEach(() => {
    processor = new TemplateProcessor();
    fs.__clearMockFiles();
    jest.clearAllMocks();

    // 设置模拟模板文件
    fs.__setMockFiles({
      './article-detail-template.vue': '<template>${TITLE} - ${LANGUAGE} - ${CONTENT}</template>',
      './article-detail-flag-template.vue': '<template>FLAG: ${TITLE} - ${LANGUAGE} - ${CONTENT}</template>',
      './article-detail-nobanner-template.vue': '<template>NO_BANNER: ${TITLE} - ${LANGUAGE} - ${CONTENT}</template>',
      './article-detail-nobanner-noflag-nocontact-template.vue': '<template>LEGAL: ${TITLE} - ${LANGUAGE} - ${CONTENT}</template>'
    });
  });

  describe('getTemplate', () => {
    test('should read and cache template', () => {
      const templateContent = '<template>Test Template</template>';
      fs.__setMockFiles({
        './article-detail-template.vue': templateContent
      });

      const result = processor.getTemplate('default');
      expect(result).toBe(templateContent);
      expect(fs.readFileSync).toHaveBeenCalledWith('./article-detail-template.vue', 'utf8');
    });

    test('should return cached template on second call', () => {
      const templateContent = '<template>Test Template</template>';
      fs.__setMockFiles({
        './article-detail-template.vue': templateContent
      });

      processor.getTemplate('default');
      processor.getTemplate('default');

      expect(fs.readFileSync).toHaveBeenCalledTimes(1);
    });

    test('should throw error for unknown template type', () => {
      expect(() => processor.getTemplate('unknown')).toThrow('Unknown template type: unknown');
    });

    test('should throw error for missing template file', () => {
      // 清除模拟文件以测试缺失模板的情况
      fs.__clearMockFiles();

      expect(() => processor.getTemplate('default')).toThrow();
    });
  });

  describe('getTemplateType', () => {
    test('should return correct template type for category ID', () => {
      expect(processor.getTemplateType(4)).toBe('flag');
      expect(processor.getTemplateType(5)).toBe('flag');
      expect(processor.getTemplateType(7)).toBe('noBanner');
      expect(processor.getTemplateType(8)).toBe('noBanner');
      expect(processor.getTemplateType(13)).toBe('noBannerNoFlagNoContact');
    });

    test('should return default for unknown category ID', () => {
      expect(processor.getTemplateType(999)).toBe('default');
    });
  });

  describe('buildTemplateVariables', () => {
    const mockPost = {
      id: '1',
      title: 'Test Article',
      content: "Test content\nwith 'quotes'",
      seo: {
        opengraphDescription: 'Test OG description',
        opengraphSiteName: 'Test Site',
        metaDesc: 'Test meta description'
      },
      featuredImage: {
        node: {
          sourceUrl: 'https://example.com/image.jpg'
        }
      },
      countryGuideExternal: {
        countryFlagImage: {
          node: {
            sourceUrl: 'https://example.com/flag.jpg'
          }
        }
      }
    };

    test('should build variables for Chinese language with country guide (categoryId 4)', () => {
      const variables = processor.buildTemplateVariables(mockPost, 'zh', 4);

      expect(variables.TITLE).toBe('Test Article');
      expect(variables.CONTENT).toBe("Test contentwith \\'quotes\\'");
      expect(variables.LANGUAGE).toBe('zh-CN');
      expect(variables.LANGUAGE_SIMPLE).toBe('zh');
      expect(variables.FORM_CONFIRM_PROMPT).toBe('您的请求已收到，我们会尽快与您联系。');
      expect(variables.DESCRIPTION).toBe('Test OG description');
      expect(variables.SITE_NAME).toBe('Test Site');
      expect(variables.FEATURE_IMAGE_URL).toBe('https://example.com/image.jpg');
      expect(variables.COUNTRY_FLAT_IMAGE_URL).toBe('https://example.com/flag.jpg');
    });

    test('should build variables for English language with country guide (categoryId 4)', () => {
      const variables = processor.buildTemplateVariables(mockPost, 'en', 4);

      expect(variables.LANGUAGE).toBe('en-US');
      expect(variables.LANGUAGE_SIMPLE).toBe('en');
      expect(variables.FORM_CONFIRM_PROMPT).toBe('We have received your request and we will contact with you as soon as possible.');
      expect(variables.COUNTRY_FLAT_IMAGE_URL).toBe('https://example.com/flag.jpg');
    });

    test('should build variables for Japanese language with country guide (categoryId 4)', () => {
      const variables = processor.buildTemplateVariables(mockPost, 'ja', 4);

      expect(variables.LANGUAGE).toBe('ja-JP');
      expect(variables.LANGUAGE_SIMPLE).toBe('ja');
      expect(variables.FORM_CONFIRM_PROMPT).toBe('お問い合わせを受け付けました。できるだけ早くご連絡いたします。');
      expect(variables.COUNTRY_FLAT_IMAGE_URL).toBe('https://example.com/flag.jpg');
    });

    test('should infer flag image from title for article type (categoryId 7)', () => {
      const articlePost = {
        ...mockPost,
        title: '沙特为什么是中国出海的很大的机会呢'
      };
      const variables = processor.buildTemplateVariables(articlePost, 'zh', 7);

      expect(variables.COUNTRY_FLAT_IMAGE_URL).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/05/沙特国旗.jpg');
    });

    test('should infer flag image from English title for article type (categoryId 8)', () => {
      const articlePost = {
        ...mockPost,
        title: 'Why Saudi Arabia is a great opportunity'
      };
      const variables = processor.buildTemplateVariables(articlePost, 'en', 8);

      expect(variables.COUNTRY_FLAT_IMAGE_URL).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/05/沙特国旗.jpg');
    });

    test('should return empty flag image for article without country name', () => {
      const articlePost = {
        ...mockPost,
        title: '企业出海的三大挑战'
      };
      const variables = processor.buildTemplateVariables(articlePost, 'zh', 7);

      expect(variables.COUNTRY_FLAT_IMAGE_URL).toBe('');
    });

    test('should handle missing SEO data', () => {
      const postWithoutSeo = { ...mockPost, seo: {} };
      const variables = processor.buildTemplateVariables(postWithoutSeo, 'zh');
      
      expect(variables.DESCRIPTION).toBe('Test Article');
      expect(variables.SITE_NAME).toBe('SmartDeer');
    });

    test('should throw error for unsupported language', () => {
      expect(() => processor.buildTemplateVariables(mockPost, 'fr')).toThrow('Unsupported language: fr');
    });
  });

  describe('processTemplate', () => {
    const mockPost = {
      id: '1',
      title: 'Test Article',
      content: 'Test content',
      seo: {
        opengraphDescription: 'Test description'
      }
    };

    beforeEach(() => {
      fs.__setMockFiles({
        './article-detail-flag-template.vue': '<template>${TITLE} - ${LANGUAGE}</template>'
      });
    });

    test('should process template successfully', () => {
      const result = processor.processTemplate(mockPost, 4, 'zh');
      expect(result).toBe('<template>Test Article - zh-CN</template>');
    });

    test('should handle processing errors', () => {
      expect(() => processor.processTemplate(mockPost, 4, 'invalid')).toThrow();
    });
  });

  describe('processMultiLanguageTemplates', () => {
    const mockPost = {
      id: '1',
      title: 'Test Article',
      content: 'Test content',
      seo: {}
    };

    beforeEach(() => {
      fs.__setMockFiles({
        './article-detail-flag-template.vue': '<template>${TITLE}</template>'
      });
    });

    test('should process multiple languages', () => {
      const results = processor.processMultiLanguageTemplates(mockPost, 4, ['zh', 'en']);
      
      expect(results.zh).toBe('<template>Test Article</template>');
      expect(results.en).toBe('<template>Test Article</template>');
    });

    test('should handle errors in individual languages', () => {
      const results = processor.processMultiLanguageTemplates(mockPost, 4, ['zh', 'invalid']);
      
      expect(results.zh).toBe('<template>Test Article</template>');
      expect(results.invalid).toBeNull();
    });
  });

  describe('cache management', () => {
    test('should clear cache', () => {
      fs.__setMockFiles({
        './article-detail-template.vue': 'content'
      });

      processor.getTemplate('default');
      expect(processor.getCacheInfo().size).toBe(1);

      processor.clearCache();
      expect(processor.getCacheInfo().size).toBe(0);
    });

    test('should return cache info', () => {
      fs.__setMockFiles({
        './article-detail-template.vue': 'content1',
        './article-detail-flag-template.vue': 'content2'
      });

      processor.getTemplate('default');
      processor.getTemplate('flag');

      const info = processor.getCacheInfo();
      expect(info.size).toBe(2);
      expect(info.keys).toContain('default');
      expect(info.keys).toContain('flag');
    });
  });

  describe('validateTemplates', () => {
    test('should validate existing templates', () => {
      fs.__setMockFiles({
        './article-detail-template.vue': 'content',
        './article-detail-flag-template.vue': 'content'
      });

      const results = processor.validateTemplates();
      
      expect(results.default.exists).toBe(true);
      expect(results.flag.exists).toBe(true);
    });

    test('should detect missing templates', () => {
      // 清除模拟文件以测试缺失模板的情况
      fs.__clearMockFiles();

      const results = processor.validateTemplates();

      expect(results.default.exists).toBe(false);
      expect(results.default.error).toContain('Template file not found');
    });
  });
});
