/**
 * FileGenerator 单元测试
 */

jest.mock('fs');

const FileGenerator = require('../../article-builder/services/FileGenerator');
const fs = require('fs');

describe('FileGenerator', () => {
  let generator;

  beforeEach(() => {
    generator = new FileGenerator();
    fs.__clearMockFiles();
    fs.__setWriteError(false);
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    test('should initialize with empty article lists', () => {
      const stats = generator.getStatistics();
      
      expect(stats.zh.countries).toBe(0);
      expect(stats.zh.articles).toBe(0);
      expect(stats.en.countries).toBe(0);
      expect(stats.ja.countries).toBe(0);
    });
  });

  describe('generateVueFile', () => {
    test('should generate Vue file successfully', () => {
      const content = '<template>Test Content</template>';
      
      generator.generateVueFile(content, 'zh', 'countries', 'test-file');
      
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        'pages/zh/countries/test-file.vue',
        content
      );
    });

    test('should handle file generation errors', () => {
      fs.__setWriteError(true);

      expect(() => {
        generator.generateVueFile('content', 'zh', 'countries', 'test');
      }).toThrow('Write failed');
    });
  });

  describe('addToArticleList', () => {
    const mockPost = {
      id: '1',
      title: 'Test Article',
      countryGuideExternal: {
        listingImage: 'image.jpg',
        countryName: 'Test Country',
        fileName: 'test-article'
      }
    };

    test('should add article to list successfully', () => {
      generator.addToArticleList(mockPost, 'zh', 'countries');
      
      const stats = generator.getStatistics();
      expect(stats.zh.countries).toBe(1);
      
      const zhCountries = generator.articleLists.zh.countries;
      expect(zhCountries[0]).toEqual({
        id: '1',
        title: 'Test Article',
        image: 'image.jpg',
        countryName: 'Test Country',
        link: '/zh/countries/test-article'
      });
    });

    test('should handle missing fileName', () => {
      const postWithoutFileName = {
        ...mockPost,
        countryGuideExternal: {
          ...mockPost.countryGuideExternal,
          fileName: undefined
        }
      };

      generator.addToArticleList(postWithoutFileName, 'zh', 'countries');
      
      const stats = generator.getStatistics();
      expect(stats.zh.countries).toBe(0);
    });

    test('should handle invalid language/type combination', () => {
      generator.addToArticleList(mockPost, 'invalid', 'countries');
      
      const stats = generator.getStatistics();
      expect(stats.zh.countries).toBe(0);
    });
  });

  describe('processArticleFiles', () => {
    const mockPost = {
      id: '1',
      title: 'Test Article',
      countryGuideExternal: {
        fileName: 'test-article',
        listingImage: 'image.jpg',
        countryName: 'Test Country'
      }
    };

    const mockProcessedTemplates = {
      zh: '<template>Chinese Content</template>',
      en: '<template>English Content</template>',
      ja: '<template>Japanese Content</template>'
    };

    test('should process files for Chinese country guide', () => {
      generator.processArticleFiles(mockPost, 4, mockProcessedTemplates);
      
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        'pages/zh/countries/test-article.vue',
        '<template>Chinese Content</template>'
      );
      
      const stats = generator.getStatistics();
      expect(stats.zh.countries).toBe(1);
    });

    test('should process files for English with Japanese generation', () => {
      generator.processArticleFiles(mockPost, 5, mockProcessedTemplates);
      
      expect(fs.writeFileSync).toHaveBeenCalledTimes(2);
      
      const stats = generator.getStatistics();
      expect(stats.en.countries).toBe(1);
      expect(stats.ja.countries).toBe(1);
    });

    test('should handle unknown category ID', () => {
      generator.processArticleFiles(mockPost, 999, mockProcessedTemplates);
      
      expect(fs.writeFileSync).not.toHaveBeenCalled();
    });

    test('should handle missing fileName', () => {
      const postWithoutFileName = {
        ...mockPost,
        countryGuideExternal: {
          ...mockPost.countryGuideExternal,
          fileName: undefined
        }
      };

      generator.processArticleFiles(postWithoutFileName, 4, mockProcessedTemplates);
      
      expect(fs.writeFileSync).not.toHaveBeenCalled();
    });
  });

  describe('generateArticleListFiles', () => {
    beforeEach(() => {
      const mockPost = {
        id: '1',
        title: 'Test Article',
        countryGuideExternal: {
          fileName: 'test-article',
          listingImage: 'image.jpg',
          countryName: 'Test Country'
        }
      };

      generator.addToArticleList(mockPost, 'zh', 'countries');
      generator.addToArticleList(mockPost, 'en', 'articles');
    });

    test('should generate JSON files for all non-empty lists', () => {
      generator.generateArticleListFiles();
      
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        './pages/zh/countries/article-list-zh.json',
        expect.stringContaining('"id": "1"')
      );
      
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        './pages/en/articles/article-list-en.json',
        expect.stringContaining('"id": "1"')
      );
    });

    test('should handle file generation errors', () => {
      fs.__setWriteError(true);

      expect(() => generator.generateArticleListFiles()).toThrow('Write failed');
    });
  });

  describe('getStatistics', () => {
    test('should return correct statistics', () => {
      const mockPost = {
        id: '1',
        title: 'Test Article',
        countryGuideExternal: {
          fileName: 'test-article',
          listingImage: 'image.jpg',
          countryName: 'Test Country'
        }
      };

      generator.addToArticleList(mockPost, 'zh', 'countries');
      generator.addToArticleList(mockPost, 'zh', 'articles');
      generator.addToArticleList(mockPost, 'en', 'countries');

      const stats = generator.getStatistics();
      
      expect(stats.zh.countries).toBe(1);
      expect(stats.zh.articles).toBe(1);
      expect(stats.en.countries).toBe(1);
      expect(stats.ja.countries).toBe(0);
    });
  });

  describe('clearArticleLists', () => {
    test('should clear all article lists', () => {
      const mockPost = {
        id: '1',
        title: 'Test Article',
        countryGuideExternal: {
          fileName: 'test-article',
          listingImage: 'image.jpg',
          countryName: 'Test Country'
        }
      };

      generator.addToArticleList(mockPost, 'zh', 'countries');
      expect(generator.getStatistics().zh.countries).toBe(1);

      generator.clearArticleLists();
      expect(generator.getStatistics().zh.countries).toBe(0);
    });
  });
});
