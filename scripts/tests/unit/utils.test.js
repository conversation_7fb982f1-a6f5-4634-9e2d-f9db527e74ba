/**
 * 工具函数单元测试
 */

const {
  cleanContent,
  safeGet,
  replaceTemplateVariables,
  createArticleListItem,
  generateFilePath,
  inferCountryFlagFromTitle
} = require('../../article-builder/utils');

describe('Utils', () => {
  describe('cleanContent', () => {
    test('should remove newlines and escape single quotes', () => {
      const input = "Hello\nWorld\nWith 'quotes'";
      const expected = "HelloWorldWith \\'quotes\\'";
      expect(cleanContent(input)).toBe(expected);
    });

    test('should handle empty content', () => {
      expect(cleanContent('')).toBe('');
      expect(cleanContent(null)).toBe('');
      expect(cleanContent(undefined)).toBe('');
    });

    test('should handle content with only newlines', () => {
      expect(cleanContent('\n\n\n')).toBe('');
    });

    test('should handle content with only quotes', () => {
      expect(cleanContent("'test'")).toBe("\\'test\\'");
    });
  });

  describe('safeGet', () => {
    const testObj = {
      a: {
        b: {
          c: 'value'
        }
      },
      x: null,
      y: undefined
    };

    test('should get nested property value', () => {
      expect(safeGet(testObj, 'a.b.c')).toBe('value');
    });

    test('should return default value for non-existent path', () => {
      expect(safeGet(testObj, 'a.b.d', 'default')).toBe('default');
    });

    test('should return empty string as default when no default provided', () => {
      expect(safeGet(testObj, 'a.b.d')).toBe('');
    });

    test('should handle null values', () => {
      expect(safeGet(null, 'x', 'default')).toBe('default');
    });

    test('should handle undefined values', () => {
      expect(safeGet(testObj, 'y', 'default')).toBe('default');
    });

    test('should handle null object', () => {
      expect(safeGet(null, 'a.b.c', 'default')).toBe('default');
    });
  });

  describe('replaceTemplateVariables', () => {
    test('should replace all template variables', () => {
      const template = 'Hello ${NAME}, welcome to ${SITE}!';
      const variables = {
        NAME: 'John',
        SITE: 'Test Site'
      };
      const expected = 'Hello John, welcome to Test Site!';
      expect(replaceTemplateVariables(template, variables)).toBe(expected);
    });

    test('should handle missing variables with empty string', () => {
      const template = 'Hello ${NAME}, welcome to ${SITE}!';
      const variables = {
        NAME: 'John',
        SITE: ''
      };
      const expected = 'Hello John, welcome to !';
      expect(replaceTemplateVariables(template, variables)).toBe(expected);
    });

    test('should handle null/undefined variables', () => {
      const template = 'Hello ${NAME}!';
      const variables = {
        NAME: null
      };
      const expected = 'Hello !';
      expect(replaceTemplateVariables(template, variables)).toBe(expected);
    });

    test('should handle empty template', () => {
      expect(replaceTemplateVariables('', {})).toBe('');
    });

    test('should handle object values by converting to empty string', () => {
      const template = 'Image: ${IMAGE_URL}, Title: ${TITLE}';
      const variables = {
        IMAGE_URL: { node: { sourceUrl: 'https://example.com/image.jpg' } }, // 对象
        TITLE: 'Test Title' // 字符串
      };
      const result = replaceTemplateVariables(template, variables);
      expect(result).toBe('Image: , Title: Test Title');
    });

    test('should handle different data types correctly', () => {
      const template = 'String: ${STR}, Number: ${NUM}, Boolean: ${BOOL}, Null: ${NULL}, Undefined: ${UNDEF}, Object: ${OBJ}';
      const variables = {
        STR: 'hello',
        NUM: 42,
        BOOL: true,
        NULL: null,
        UNDEF: undefined,
        OBJ: { key: 'value' }
      };
      const result = replaceTemplateVariables(template, variables);
      expect(result).toBe('String: hello, Number: 42, Boolean: true, Null: , Undefined: , Object: ');
    });
  });

  describe('createArticleListItem', () => {
    test('should create article list item with all properties', () => {
      const post = {
        id: '123',
        title: 'Test Article',
        countryGuideExternal: {
          listingImage: 'image.jpg',
          countryName: 'Test Country'
        }
      };

      const result = createArticleListItem(post);
      expect(result).toEqual({
        id: '123',
        title: 'Test Article',
        image: 'image.jpg',
        countryName: 'Test Country'
      });
    });

    test('should handle missing countryGuideExternal', () => {
      const post = {
        id: '123',
        title: 'Test Article'
      };

      const result = createArticleListItem(post);
      expect(result).toEqual({
        id: '123',
        title: 'Test Article',
        image: '',
        countryName: ''
      });
    });
  });

  describe('generateFilePath', () => {
    test('should generate file path with default extension', () => {
      const result = generateFilePath('/base/path', 'filename');
      expect(result).toBe('/base/path/filename.vue');
    });

    test('should generate file path with custom extension', () => {
      const result = generateFilePath('/base/path', 'filename', '.json');
      expect(result).toBe('/base/path/filename.json');
    });

    test('should handle empty base path', () => {
      const result = generateFilePath('', 'filename');
      expect(result).toBe('filename.vue');
    });
  });

  describe('inferCountryFlagFromTitle', () => {
    test('should return correct flag URL for Chinese country names', () => {
      expect(inferCountryFlagFromTitle('沙特为什么是中国出海的很大的机会呢')).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/05/沙特国旗.jpg');
      expect(inferCountryFlagFromTitle('为什么许多中国企业开始逐步走向阿联酋？')).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/04/阿联酋国旗.png');
      expect(inferCountryFlagFromTitle('新加坡，中国企业出海的重要跳板')).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/04/新加坡国旗.png');
    });

    test('should return correct flag URL for English country names', () => {
      expect(inferCountryFlagFromTitle('Why Saudi Arabia is a great opportunity')).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/05/沙特国旗.jpg');
      expect(inferCountryFlagFromTitle('Companies expanding to UAE')).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/04/阿联酋国旗.png');
      expect(inferCountryFlagFromTitle('Singapore as a springboard')).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/04/新加坡国旗.png');
    });

    test('should return empty string for titles without country names', () => {
      expect(inferCountryFlagFromTitle('企业出海的三大挑战')).toBe('');
      expect(inferCountryFlagFromTitle('Global expansion strategies')).toBe('');
      expect(inferCountryFlagFromTitle('')).toBe('');
    });

    test('should handle null or undefined titles', () => {
      expect(inferCountryFlagFromTitle(null)).toBe('');
      expect(inferCountryFlagFromTitle(undefined)).toBe('');
    });

    test('should match the first country name found in title', () => {
      // 如果标题中包含多个国家名称，应该返回第一个匹配的
      const title = '从沙特到阿联酋的投资机会';
      const result = inferCountryFlagFromTitle(title);
      // 应该匹配到"沙特"（因为它在标题中出现得更早）
      expect(result).toBe('https://blog.smartdeer.work/wp-content/uploads/2024/05/沙特国旗.jpg');
    });
  });
});
