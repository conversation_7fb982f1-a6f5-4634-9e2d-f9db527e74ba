/**
 * ArticleBuilder 集成测试
 */

jest.mock('fs');
jest.mock('graphql-request');

const ArticleBuilder = require('../../article-builder/ArticleBuilder');
const fs = require('fs');

describe('ArticleBuilder Integration', () => {
  let builder;

  beforeEach(() => {
    builder = new ArticleBuilder();
    fs.__clearMockFiles();
    fs.__setWriteError(false);
    jest.clearAllMocks();

    // 设置模拟模板文件
    fs.__setMockFiles({
      './article-detail-template.vue': '<template>${TITLE} - ${LANGUAGE} - ${CONTENT}</template>',
      './article-detail-flag-template.vue': '<template>FLAG: ${TITLE} - ${LANGUAGE} - ${CONTENT}</template>',
      './article-detail-nobanner-template.vue': '<template>NO_BANNER: ${TITLE} - ${LANGUAGE} - ${CONTENT}</template>',
      './article-detail-nobanner-noflag-nocontact-template.vue': '<template>LEGAL: ${TITLE} - ${LANGUAGE} - ${CONTENT}</template>'
    });
  });

  describe('buildArticles', () => {
    test('should complete full build process successfully', async () => {
      await builder.buildArticles();

      // 验证文件生成
      const mockFiles = fs.__getMockFiles();
      
      // 检查生成的Vue文件
      expect(mockFiles['pages/zh/countries/test-article-1.vue']).toContain('FLAG: Test Article 1 - zh-CN');
      expect(mockFiles['pages/en/countries/test-article-2.vue']).toContain('FLAG: Test Article 2 - en-US');
      expect(mockFiles['pages/ja/countries/test-article-2.vue']).toContain('FLAG: Test Article 2 - ja-JP');

      // 检查生成的JSON文件
      expect(mockFiles['./pages/zh/countries/article-list-zh.json']).toBeDefined();
      expect(mockFiles['./pages/en/countries/article-list-en.json']).toBeDefined();
      expect(mockFiles['./pages/ja/countries/article-list-ja.json']).toBeDefined();

      // 验证JSON内容
      const zhList = JSON.parse(mockFiles['./pages/zh/countries/article-list-zh.json']);
      expect(zhList).toHaveLength(1);
      expect(zhList[0].title).toBe('Test Article 1');
      expect(zhList[0].link).toBe('/zh/countries/test-article-1');

      const enList = JSON.parse(mockFiles['./pages/en/countries/article-list-en.json']);
      expect(enList).toHaveLength(1);
      expect(enList[0].title).toBe('Test Article 2');
      expect(enList[0].link).toBe('/en/countries/test-article-2');
    });

    test('should handle articles with different categories', async () => {
      // 这个测试使用默认的模拟数据，包含categoryId 4和5
      await builder.buildArticles();

      const mockFiles = fs.__getMockFiles();
      
      // 验证不同分类的文章被正确处理
      expect(mockFiles['pages/zh/countries/test-article-1.vue']).toBeDefined();
      expect(mockFiles['pages/en/countries/test-article-2.vue']).toBeDefined();
      expect(mockFiles['pages/ja/countries/test-article-2.vue']).toBeDefined();
    });

    test('should handle template validation failure', async () => {
      // 清空模板文件
      fs.__clearMockFiles();

      await expect(builder.buildArticles()).rejects.toThrow('Template validation failed');
    });

    test('should handle empty article list', async () => {
      // 模拟空的GraphQL响应
      const mockEmptyResponse = {
        posts: {
          nodes: [],
          pageInfo: {
            hasNextPage: false,
            endCursor: null
          }
        }
      };

      builder.dataFetcher.client.request = jest.fn().mockResolvedValue(mockEmptyResponse);

      await builder.buildArticles();

      // 验证没有Vue文件被生成（但模板文件仍然存在）
      const mockFiles = fs.__getMockFiles();
      const vueFiles = Object.keys(mockFiles).filter(path =>
        path.endsWith('.vue') && path.includes('/pages/')
      );
      expect(vueFiles).toHaveLength(0);
    });
  });

  describe('processArticle', () => {
    const mockArticle = {
      id: '1',
      title: 'Integration Test Article',
      content: 'Test content for integration',
      categories: {
        nodes: [{ categoryId: 4 }]
      },
      seo: {
        opengraphDescription: 'Test description'
      },
      countryGuideExternal: {
        fileName: 'integration-test',
        listingImage: 'image.jpg',
        countryName: 'Test Country'
      }
    };

    test('should process single article correctly', async () => {
      await builder.processArticle(mockArticle);

      const mockFiles = fs.__getMockFiles();
      expect(mockFiles['pages/zh/countries/integration-test.vue']).toContain('Integration Test Article');
      
      const stats = builder.getStatistics();
      expect(stats.articles.zh.countries).toBe(1);
    });

    test('should handle article with multiple categories', async () => {
      const multiCategoryArticle = {
        ...mockArticle,
        categories: {
          nodes: [
            { categoryId: 4 },
            { categoryId: 5 }
          ]
        }
      };

      await builder.processArticle(multiCategoryArticle);

      const mockFiles = fs.__getMockFiles();
      expect(mockFiles['pages/zh/countries/integration-test.vue']).toBeDefined();
      expect(mockFiles['pages/en/countries/integration-test.vue']).toBeDefined();
      expect(mockFiles['pages/ja/countries/integration-test.vue']).toBeDefined();
    });

    test('should handle article with no categories', async () => {
      const noCategoryArticle = {
        ...mockArticle,
        categories: null
      };

      await builder.processArticle(noCategoryArticle);

      const mockFiles = fs.__getMockFiles();
      const vueFiles = Object.keys(mockFiles).filter(path =>
        path.endsWith('.vue') && path.includes('/pages/')
      );
      expect(vueFiles).toHaveLength(0);
    });
  });

  describe('service integration', () => {
    test('should integrate all services correctly', async () => {
      // 验证服务之间的协作
      const initialStats = builder.getStatistics();
      expect(initialStats.templates.size).toBe(0);

      await builder.buildArticles();

      const finalStats = builder.getStatistics();
      
      // 验证模板被缓存
      expect(finalStats.templates.size).toBeGreaterThan(0);
      
      // 验证文章被处理
      expect(finalStats.articles.zh.countries + finalStats.articles.en.countries).toBeGreaterThan(0);
      
      // 验证分页状态
      expect(finalStats.pagination.hasNextPage).toBe(false);
    });

    test('should handle service errors gracefully', async () => {
      // 模拟模板处理错误
      builder.templateProcessor.processTemplate = jest.fn().mockImplementation(() => {
        throw new Error('Template processing failed');
      });

      // 构建过程应该继续，但不会生成文件
      await builder.buildArticles();

      const mockFiles = fs.__getMockFiles();
      const vueFiles = Object.keys(mockFiles).filter(path =>
        path.endsWith('.vue') && path.includes('/pages/')
      );
      expect(vueFiles).toHaveLength(0);
    });
  });

  describe('cleanup and validation', () => {
    test('should validate environment correctly', () => {
      const validation = builder.validateEnvironment();
      
      expect(validation.templates.default.exists).toBe(true);
      expect(validation.templates.flag.exists).toBe(true);
      expect(validation.api.configured).toBe(true);
    });

    test('should cleanup resources', async () => {
      await builder.buildArticles();
      
      let stats = builder.getStatistics();
      expect(stats.templates.size).toBeGreaterThan(0);
      expect(stats.articles.zh.countries).toBeGreaterThan(0);

      builder.cleanup();

      stats = builder.getStatistics();
      expect(stats.templates.size).toBe(0);
      expect(stats.articles.zh.countries).toBe(0);
    });
  });
});
