// 百度主动推送脚本
const https = require('https');
const fs = require('fs');
const path = require('path');

// 百度站长平台配置
const BAIDU_CONFIG = {
  site: 'smartdeer.work',
  token: 'YOUR_BAIDU_TOKEN', // 需要在百度站长平台获取
  api: 'http://data.zz.baidu.com/urls'
};

// 获取所有需要推送的URL
function getAllUrls() {
  const baseUrl = 'https://smartdeer.work';
  
  // 主要页面URL
  const mainUrls = [
    `${baseUrl}/`,
    `${baseUrl}/en/`,
    `${baseUrl}/zh/`,
    `${baseUrl}/ja/`,
    `${baseUrl}/en/calculator`,
    `${baseUrl}/zh/calculator`,
    `${baseUrl}/ja/calculator`,
    `${baseUrl}/en/aboutus`,
    `${baseUrl}/zh/aboutus`,
    `${baseUrl}/ja/aboutus`
  ];

  // 文章页面URL
  const articleUrls = [
    `${baseUrl}/zh/articles/Singapore_an_important_springboard_for_Chinese_companies_to_go_global`,
    `${baseUrl}/zh/articles/why_companies_choose_Singapore_when_going_overseas`,
    `${baseUrl}/zh/articles/Saudi_Arabia_builds_first-tier_cities`,
    `${baseUrl}/zh/articles/Golden-opportunities-for-electric-vehicles-and-supporting-facilities`,
    `${baseUrl}/zh/articles/The-opportunity-to-go-abroad-to-Brazil-is-just-around-the-corner`,
    `${baseUrl}/zh/articles/Green-has-become-the-new-label-of-this-passionate-country`,
    `${baseUrl}/zh/articles/The-serious-consequences-of-not-reporting-personal-income-tax-in-Brazil`
  ];

  return [...mainUrls, ...articleUrls];
}

// 推送URL到百度
function pushToBaidu(urls, type = 'urls') {
  return new Promise((resolve, reject) => {
    const postData = urls.join('\n');
    const apiUrl = `${BAIDU_CONFIG.api}?site=${BAIDU_CONFIG.site}&token=${BAIDU_CONFIG.token}&type=${type}`;
    
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(apiUrl, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('百度推送结果:', result);
          resolve(result);
        } catch (error) {
          console.error('解析响应失败:', error);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('推送失败:', error);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// 批量推送
async function batchPush() {
  const allUrls = getAllUrls();
  const batchSize = 10; // 每批推送10个URL
  
  console.log(`开始推送 ${allUrls.length} 个URL到百度...`);
  
  for (let i = 0; i < allUrls.length; i += batchSize) {
    const batch = allUrls.slice(i, i + batchSize);
    
    try {
      console.log(`推送第 ${Math.floor(i/batchSize) + 1} 批，共 ${batch.length} 个URL`);
      const result = await pushToBaidu(batch);
      
      // 记录推送结果
      const logEntry = {
        timestamp: new Date().toISOString(),
        batch: Math.floor(i/batchSize) + 1,
        urls: batch,
        result: result
      };
      
      // 保存到日志文件
      const logPath = path.join(__dirname, '../logs/baidu-push.log');
      fs.appendFileSync(logPath, JSON.stringify(logEntry) + '\n');
      
      // 延迟1秒避免频率限制
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`第 ${Math.floor(i/batchSize) + 1} 批推送失败:`, error);
    }
  }
  
  console.log('批量推送完成');
}

// 推送新内容（实时推送）
function pushNewContent(urls) {
  console.log('推送新内容到百度:', urls);
  return pushToBaidu(urls, 'realtime');
}

// 推送更新内容
function pushUpdatedContent(urls) {
  console.log('推送更新内容到百度:', urls);
  return pushToBaidu(urls, 'batch');
}

// 创建日志目录
function ensureLogDirectory() {
  const logDir = path.join(__dirname, '../logs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
}

// 导出函数
module.exports = {
  pushToBaidu,
  batchPush,
  pushNewContent,
  pushUpdatedContent,
  getAllUrls
};

// 如果直接运行此脚本
if (require.main === module) {
  ensureLogDirectory();
  
  // 检查是否提供了token
  if (BAIDU_CONFIG.token === 'YOUR_BAIDU_TOKEN') {
    console.log('请先在百度站长平台获取推送token，并更新BAIDU_CONFIG.token');
    process.exit(1);
  }
  
  batchPush().catch(console.error);
}
